import { useState } from "react";
import {
  FiRefreshCw,
  FiTrendingUp,
  FiDownload,
  FiStar,
  FiPackage,
  FiActivity,
  FiArrowUp,
  FiArrowDown,
  FiEye,
  FiBarChart2,
  FiSearch,
  FiSettings,
} from "react-icons/fi";

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalPlugins: 3,
    totalDownloads: 125847,
    averageRating: 4.6,
    weeklyDownloads: 8432,
    monthlyGrowth: 12.5,
  });
  const [loading, setLoading] = useState(false);

  const plugins = [
    {
      name: "EmbedPress",
      downloads: 45230,
      rating: 4.8,
      rank: 15,
      change: +2,
      activeInstalls: "10,000+",
    },
    {
      name: "SchedulePress",
      downloads: 38917,
      rating: 4.5,
      rank: 23,
      change: -1,
      activeInstalls: "5,000+",
    },
    {
      name: "NotificationX",
      downloads: 41700,
      rating: 4.7,
      rank: 18,
      change: +5,
      activeInstalls: "20,000+",
    },
  ];

  const refreshData = async () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600 mt-2 text-lg">
              WordPress Plugin Performance Overview
            </p>
          </div>
          <div className="flex items-center gap-3">
            <div className="text-sm text-gray-500">
              Last updated: {new Date().toLocaleTimeString()}
            </div>
            <button
              onClick={refreshData}
              disabled={loading}
              className="btn-primary flex items-center space-x-2 px-4 py-2.5 rounded-xl font-semibold transition-all duration-200 hover:scale-105"
            >
              <FiRefreshCw
                className={`h-4 w-4 ${loading ? "animate-spin" : ""}`}
              />
              <span>Refresh Data</span>
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-300 group">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Total Plugins
              </p>
              <p className="text-3xl font-bold text-gray-900 mt-2">
                {stats.totalPlugins}
              </p>
              <div className="flex items-center mt-2">
                <FiTrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600 font-medium">
                  Active
                </span>
              </div>
            </div>
            <div className="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl group-hover:scale-110 transition-transform duration-300">
              <FiPackage className="h-8 w-8 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-300 group">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Total Downloads
              </p>
              <p className="text-3xl font-bold text-gray-900 mt-2">
                {stats.totalDownloads.toLocaleString()}
              </p>
              <div className="flex items-center mt-2">
                <FiArrowUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600 font-medium">
                  +{stats.monthlyGrowth}%
                </span>
              </div>
            </div>
            <div className="p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl group-hover:scale-110 transition-transform duration-300">
              <FiDownload className="h-8 w-8 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-300 group">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Average Rating
              </p>
              <p className="text-3xl font-bold text-gray-900 mt-2">
                {stats.averageRating.toFixed(1)}
              </p>
              <div className="flex items-center mt-2">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <FiStar
                      key={i}
                      className={`h-4 w-4 ${
                        i < Math.floor(stats.averageRating)
                          ? "fill-current"
                          : ""
                      }`}
                    />
                  ))}
                </div>
              </div>
            </div>
            <div className="p-4 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-2xl group-hover:scale-110 transition-transform duration-300">
              <FiStar className="h-8 w-8 text-white" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-all duration-300 group">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Weekly Downloads
              </p>
              <p className="text-3xl font-bold text-gray-900 mt-2">
                {stats.weeklyDownloads.toLocaleString()}
              </p>
              <div className="flex items-center mt-2">
                <FiActivity className="h-4 w-4 text-purple-500 mr-1" />
                <span className="text-sm text-purple-600 font-medium">
                  This week
                </span>
              </div>
            </div>
            <div className="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl group-hover:scale-110 transition-transform duration-300">
              <FiTrendingUp className="h-8 w-8 text-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Plugin Performance Overview */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
        <div className="px-6 py-5 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-bold text-gray-900">
              Plugin Performance Overview
            </h2>
            <button className="text-sm text-blue-600 hover:text-blue-700 font-medium flex items-center gap-2">
              <FiEye className="h-4 w-4" />
              View All
            </button>
          </div>
        </div>

        <div className="p-6">
          <div className="grid gap-4">
            {plugins.map((plugin, index) => (
              <div
                key={index}
                className="bg-gradient-to-r from-gray-50 to-white rounded-xl p-6 border border-gray-100 hover:shadow-md transition-all duration-300 group"
              >
                <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                  <div className="flex items-center space-x-4">
                    <div className="relative">
                      <div className="h-12 w-12 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg">
                        <span className="text-white font-bold text-lg">
                          {plugin.name.charAt(0)}
                        </span>
                      </div>
                      {plugin.change !== 0 && (
                        <div
                          className={`absolute -top-1 -right-1 h-6 w-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                            plugin.change > 0 ? "bg-green-500" : "bg-red-500"
                          }`}
                        >
                          {plugin.change > 0 ? "+" : ""}
                          {plugin.change}
                        </div>
                      )}
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-gray-900 group-hover:text-blue-600 transition-colors">
                        {plugin.name}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {plugin.activeInstalls} active installations
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-8 space-y-4 sm:space-y-0">
                    <div className="flex justify-around sm:contents">
                      <div className="text-center">
                        <p className="text-xl sm:text-2xl font-bold text-gray-900">
                          {plugin.downloads.toLocaleString()}
                        </p>
                        <p className="text-xs text-gray-600 uppercase tracking-wide">
                          Downloads
                        </p>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center justify-center">
                          <FiStar className="h-4 w-4 text-yellow-400 mr-1" />
                          <span className="text-lg font-bold text-gray-900">
                            {plugin.rating.toFixed(1)}
                          </span>
                        </div>
                        <p className="text-xs text-gray-600 uppercase tracking-wide">
                          Rating
                        </p>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center justify-center">
                          <span className="text-xl sm:text-2xl font-bold text-gray-900">
                            #{plugin.rank}
                          </span>
                          {plugin.change !== 0 &&
                            (plugin.change > 0 ? (
                              <FiArrowUp className="h-4 w-4 text-green-500 ml-1" />
                            ) : (
                              <FiArrowDown className="h-4 w-4 text-red-500 ml-1" />
                            ))}
                        </div>
                        <p className="text-xs text-gray-600 uppercase tracking-wide">
                          Rank
                        </p>
                      </div>
                    </div>

                    <div className="flex space-x-2 justify-center sm:justify-start">
                      <button className="px-3 py-2 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors font-medium text-sm">
                        View Details
                      </button>
                      <button className="px-3 py-2 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors font-medium text-sm">
                        Analytics
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
        <div className="px-6 py-5 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white">
          <h2 className="text-xl font-bold text-gray-900">Quick Actions</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button className="group p-6 bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl hover:from-blue-100 hover:to-blue-200 transition-all duration-300 text-left border border-blue-200 hover:border-blue-300 hover:shadow-lg hover:scale-105">
              <div className="p-3 bg-blue-500 rounded-xl w-fit mb-4 group-hover:bg-blue-600 transition-colors duration-300 shadow-lg">
                <FiDownload className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-bold text-gray-900 mb-2">
                Update Download Data
              </h3>
              <p className="text-sm text-gray-600">
                Fetch latest download statistics from WordPress.org
              </p>
            </button>

            <button className="group p-6 bg-gradient-to-br from-green-50 to-green-100 rounded-xl hover:from-green-100 hover:to-green-200 transition-all duration-300 text-left border border-green-200 hover:border-green-300 hover:shadow-lg hover:scale-105">
              <div className="p-3 bg-green-500 rounded-xl w-fit mb-4 group-hover:bg-green-600 transition-colors duration-300 shadow-lg">
                <FiBarChart2 className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-bold text-gray-900 mb-2">Generate Report</h3>
              <p className="text-sm text-gray-600">
                Create comprehensive performance report
              </p>
            </button>

            <button className="group p-6 bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl hover:from-purple-100 hover:to-purple-200 transition-all duration-300 text-left border border-purple-200 hover:border-purple-300 hover:shadow-lg hover:scale-105">
              <div className="p-3 bg-purple-500 rounded-xl w-fit mb-4 group-hover:bg-purple-600 transition-colors duration-300 shadow-lg">
                <FiSearch className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-bold text-gray-900 mb-2">Keyword Analysis</h3>
              <p className="text-sm text-gray-600">
                Analyze keyword rankings and trends
              </p>
            </button>

            <button className="group p-6 bg-gradient-to-br from-orange-50 to-orange-100 rounded-xl hover:from-orange-100 hover:to-orange-200 transition-all duration-300 text-left border border-orange-200 hover:border-orange-300 hover:shadow-lg hover:scale-105">
              <div className="p-3 bg-orange-500 rounded-xl w-fit mb-4 group-hover:bg-orange-600 transition-colors duration-300 shadow-lg">
                <FiSettings className="h-6 w-6 text-white" />
              </div>
              <h3 className="font-bold text-gray-900 mb-2">Settings</h3>
              <p className="text-sm text-gray-600">
                Configure tracking and notification settings
              </p>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
