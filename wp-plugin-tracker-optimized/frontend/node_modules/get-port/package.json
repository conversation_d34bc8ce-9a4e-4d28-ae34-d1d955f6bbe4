{"name": "get-port", "version": "4.2.0", "description": "Get an available port", "license": "MIT", "repository": "sindresorhus/get-port", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=6"}, "scripts": {"test": "xo && ava && tsd-check"}, "files": ["index.js", "index.d.ts"], "keywords": ["port", "find", "finder", "portfinder", "free", "available", "connection", "connect", "open", "net", "tcp", "scan", "random", "preferred", "chosen"], "devDependencies": {"ava": "^1.2.1", "pify": "^3.0.0", "tsd-check": "^0.3.0", "xo": "^0.24.0"}}