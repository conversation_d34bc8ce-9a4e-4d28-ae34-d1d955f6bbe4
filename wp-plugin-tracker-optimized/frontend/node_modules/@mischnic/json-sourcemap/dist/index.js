var $hlrUL$lezercommon = require("@lezer/common");
var $hlrUL$json5 = require("json5");
var $hlrUL$lezerlr = require("@lezer/lr");

function $parcel$interopDefault(a) {
  return a && a.__esModule ? a.default : a;
}
function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "parse", () => $83c796b2b9543406$export$98e6a39c04603d36);
// This file was generated by lezer-generator. You probably shouldn't edit it.

const $1adaef8fb7bb1497$export$8f49e4af10703ce3 = (0, $hlrUL$lezerlr.LRParser).deserialize({
    version: 14,
    states: "%QO]QPOOOOQO'#Cd'#CdOtQQO'#CgO!PQPO'#ClOOQO'#Cs'#CsQOQPOOOOQO'#Ci'#CiO!WQPO'#ChO!]QPO'#CuOOQO,59R,59RO!eQPO,59ROOQO'#Cm'#CmO!jQPO'#CyOOQO,59W,59WO!rQPO,59WO]QPO,59SO!wQQO,59aO#SQPO,59aOOQO1G.m1G.mO#[QPO,59eO#cQPO,59eOOQO1G.r1G.rOOQO1G.n1G.nOOQO,59Y,59YO#kQQO1G.{OOQO-E6l-E6lOOQO,59Z,59ZO#vQPO1G/POOQO-E6m-E6mPwQQO'#CnP]QPO'#Co",
    stateData: "$R~OfOSPOSQOS~OSSOTSOUSOVSOYQO_ROhPO~OXXOhUOjUO~O^]O~P]Ok_O~Ol`OXiX~OXbO~OlcO^mX~O^eO~OhUOjUOXia~OlhOXia~O^ma~P]OlkO^ma~OhUOjUOXii~O^mi~P]OPQj~",
    goto: "!}nPPPPPPPPoPPow!PPPo!V!_!ePPP!kP!wPPP!z]SOR_cknQWQVg`hmXVQ`hmQ[RVjcknQaWRiaQd[RldQTOWZRcknRf_RYQR^R",
    nodeNames: "⚠ LineComment BlockComment JsonText True False Null Number String } { Object Property PropertyName ] [ Array ArrayValue",
    maxTerm: 29,
    nodeProps: [
        [
            "group",
            -7,
            4,
            5,
            6,
            7,
            8,
            11,
            16,
            "Value"
        ],
        [
            "openedBy",
            9,
            "{",
            14,
            "["
        ],
        [
            "closedBy",
            10,
            "}",
            15,
            "]"
        ]
    ],
    skippedNodes: [
        0,
        1,
        2
    ],
    repeatNodeCount: 2,
    tokenData: "!!`~R!OXY$RYZ$RZ[$R[]$R]^$Rpq$Rrs$Wtu.Xwx0b{|2Y|}6p}!O2Y!O!P2l!P!Q6u!Q!R3l!R![5Q![!]8t!c!k.X!k!l8y!l!p.X!p!q@}!q!}.X!}#OB|#O#P/S#P#QCR#R#S.X#T#Y.X#Y#ZCW#Z#b.X#b#cHU#c#h.X#h#iLU#i#o.X#o#p!!U#q#r!!Z$f$g$R$g$IV.X$IV$IW$R$IW$I|.X$I|$I}$R$I}$JO$R$JU;'S.X;'S;=`0[<%l?HT.X?HT?HU$R?HUO.X~$WOf~~$ZXOp$vpq$Wqr$Wrs%`s#O$W#O#P&s#P;'S$W;'S;=`.R<%lO$W~$yVOr$vrs%`s#O$v#O#P%e#P;'S$v;'S;=`&m<%lO$v~%eOh~~%haYZ$v]^$vrs$vwx$v!Q!R$v#O#P$v#T#U$v#U#V$v#Y#Z$v#b#c$v#f#g$v#h#i$v#i#j$v#j#k$v#l#m$v$I|$I}$v$I}$JO$v~&pP;=`<%l$v~&vbYZ$v]^$vrs$Wwx$v!P!Q(O!Q!R$v#O#P$W#T#U$v#U#V$W#Y#Z$W#b#c$W#f#g$W#h#i$W#i#j*c#j#k$v#l#m$v$I|$I}$v$I}$JO$v~(RWpq(Oqr(Ors%`s#O(O#O#P(k#P;'S(O;'S;=`*]<%lO(O~(nXrs(O!P!Q(O#O#P(O#U#V(O#Y#Z(O#b#c(O#f#g(O#h#i(O#i#j)Z~)^R!Q![)g!c!i)g#T#Z)g~)jR!Q![)s!c!i)s#T#Z)s~)vR!Q![*P!c!i*P#T#Z*P~*SR!Q![(O!c!i(O#T#Z(O~*`P;=`<%l(O~*f]Or$vrs%`s!Q$v!Q![+_![!c$v!c!i+_!i#O$v#O#P%e#P#T$v#T#Z+_#Z;'S$v;'S;=`&m<%lO$v~+b]Or$vrs%`s!Q$v!Q![,Z![!c$v!c!i,Z!i#O$v#O#P%e#P#T$v#T#Z,Z#Z;'S$v;'S;=`&m<%lO$v~,^]Or$vrs%`s!Q$v!Q![-V![!c$v!c!i-V!i#O$v#O#P%e#P#T$v#T#Z-V#Z;'S$v;'S;=`&m<%lO$v~-Y]Or$vrs%`s!Q$v!Q![$W![!c$v!c!i$W!i#O$v#O#P%e#P#T$v#T#Z$W#Z;'S$v;'S;=`&m<%lO$v~.UP;=`<%l$WQ.^[jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XQ/VP#i#j/YQ/]R!Q![/f!c!i/f#T#Z/fQ/iR!Q![/r!c!i/r#T#Z/rQ/uR!Q![0O!c!i0O#T#Z0OQ0RR!Q![.X!c!i.X#T#Z.XQ0_P;=`<%l.X~0eVOw0bwx%`x#O0b#O#P0z#P;'S0b;'S;=`2S<%lO0b~0}aYZ0b]^0brs0bwx0b!Q!R0b#O#P0b#T#U0b#U#V0b#Y#Z0b#b#c0b#f#g0b#h#i0b#i#j0b#j#k0b#l#m0b$I|$I}0b$I}$JO0b~2VP;=`<%l0bP2]T!O!P2l!Q!R3l!R![5Q!k!l5c!p!q6dP2oP!Q![2rP2wRVP!Q![2r!g!h3Q#X#Y3QP3TR{|3^}!O3^!Q![3dP3aP!Q![3dP3iPVP!Q![3dP3qTVP!O!P2r!Q![4Q!g!h3Q#X#Y3Q#l#m4fP4TQ!O!P4Z!Q![4QP4`QVP!g!h3Q#X#Y3QP4iR!Q![4r!c!i4r#T#Z4rP4wRVP!Q![4r!c!i4r#T#Z4rP5VSVP!O!P2r!Q![5Q!g!h3Q#X#Y3QP5fP#b#c5iP5lP#Y#Z5oP5rP#]#^5uP5xP#b#c5{P6OP#]#^6RP6UP#h#i6XP6[P#m#n6_P6dOVPP6gP#T#U6jP6mP!p!q6_~6uOl~~6xQz{7O!P!Q8V~7RTOz7Oz{7b{;'S7O;'S;=`8P<%lO7O~7eVOz7Oz{7b{!P7O!P!Q7z!Q;'S7O;'S;=`8P<%lO7O~8POQ~~8SP;=`<%l7O~8[UP~OY8VZ]8V^$I|8V$JO;'S8V;'S;=`8n<%lO8V~8qP;=`<%l8V~8yOk~R9O^jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#b.X#b#c9z#c#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XR:P^jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#Y.X#Y#Z:{#Z#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XR;Q^jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#].X#]#^;|#^#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XR<R^jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#b.X#b#c<}#c#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XR=S^jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#].X#]#^>O#^#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XR>T^jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#h.X#h#i?P#i#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XR?U^jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#m.X#m#n@Q#n#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XR@X[VPjQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XRAS]jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#UA{#U#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XRBQ^jQtu.X!Q![.X!c!p.X!p!q@Q!q!}.X#O#P/S#R#S.X#T#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.X~CRO_~~CWO^~RC]]jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#UDU#U#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XRDZ^jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#`.X#`#aEV#a#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XRE[^jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#g.X#g#hFW#h#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XRF]^jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#X.X#X#YGX#Y#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XRG`[TPjQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XRHZ^jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#i.X#i#jIV#j#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XRI[^jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#`.X#`#aJW#a#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XRJ]^jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#`.X#`#aKX#a#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XRK`[UPjQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XRLZ^jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#f.X#f#gMV#g#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XRM[^jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#i.X#i#jNW#j#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XRN]^jQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#X.X#X#Y! X#Y#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.XR! `[SPjQtu.X!Q![.X!c!}.X#O#P/S#R#S.X#T#o.X$g$IV.X$IW$I|.X$JU;'S.X;'S;=`0[<%l?HT.X?HUO.X~!!ZOY~~!!`OX~",
    tokenizers: [
        0,
        1
    ],
    topRules: {
        "JsonText": [
            0,
            3
        ]
    },
    dialects: {
        json5: 137
    },
    tokenPrec: 0
});


// This file was generated by lezer-generator. You probably shouldn't edit it.
const $121152950f502dea$export$e6476262d0d4122e = 1, $121152950f502dea$export$33c356ab5b93be35 = 2, $121152950f502dea$export$8bc1c2b4dd1e923b = 3, $121152950f502dea$export$4bc0976997a4d94e = 4, $121152950f502dea$export$5d3e9aafef2fffbe = 5, $121152950f502dea$export$26c9c3a80cd996ae = 6, $121152950f502dea$export$fffa67e515d04022 = 7, $121152950f502dea$export$89b8e0fa65f6a914 = 8, $121152950f502dea$export$164a3ab98abb171d = 11, $121152950f502dea$export$41b04b3a73e7216d = 12, $121152950f502dea$export$e546d8cfcc0684e2 = 13, $121152950f502dea$export$c4be6576ca6fe4aa = 16, $121152950f502dea$export$f06b0ce79fd44095 = 17, $121152950f502dea$export$386a487b17ea4d92 = 0;






function $83c796b2b9543406$export$98e6a39c04603d36(input, reviver, { dialect: dialect = "json" , tabWidth: tabWidth = 4  } = {}) {
    // Let these parsers throw any errors about invalid input
    let data = dialect === "JSON5" ? (0, ($parcel$interopDefault($hlrUL$json5))).parse(input, reviver) : JSON.parse(input, reviver);
    let tree = (0, $1adaef8fb7bb1497$export$8f49e4af10703ce3).configure({
        strict: true,
        dialect: dialect === "JSON5" ? "json5" : "json"
    }).parse(input);
    let pointers = new Map();
    let currentPath = [
        ""
    ];
    tree.iterate({
        enter (node) {
            // if (type.isError) {
            // 	let fromPos = posToLineColumn(input, from, tabWidth);
            // 	let error = new SyntaxError(
            // 		`Failed to parse (${fromPos.line}:${fromPos.column})`
            // 	);
            // 	error.lineNumber = fromPos.line;
            // 	error.columnNumber = fromPos.column;
            // 	throw error;
            // }
            let group = node.type.prop((0, $hlrUL$lezercommon.NodeProp).group);
            if (group === null || group === void 0 ? void 0 : group.includes("Value")) $83c796b2b9543406$var$mapMerge(pointers, $83c796b2b9543406$var$toJsonPointer(currentPath), {
                value: $83c796b2b9543406$var$posToLineColumn(input, node.from, tabWidth),
                valueEnd: $83c796b2b9543406$var$posToLineColumn(input, node.to, tabWidth)
            });
            if (node.name === "PropertyName") {
                let name = input.slice(node.from, node.to);
                let quoted = name[0] === `'` || name[0] == `"`;
                currentPath.push(quoted ? name.slice(1, -1) : name);
                $83c796b2b9543406$var$mapMerge(pointers, $83c796b2b9543406$var$toJsonPointer(currentPath), {
                    key: $83c796b2b9543406$var$posToLineColumn(input, node.from, tabWidth),
                    keyEnd: $83c796b2b9543406$var$posToLineColumn(input, node.to, tabWidth)
                });
            } else if (node.name === "Array") currentPath.push(0);
        },
        leave (node) {
            if (node.name === "Property" || node.name === "Array") currentPath.pop();
            else if (node.name === "ArrayValue") // @ts-ignore
            currentPath[currentPath.length - 1]++;
        }
    });
    return {
        data: data,
        pointers: Object.fromEntries(pointers)
    };
}
function $83c796b2b9543406$var$mapMerge(map, key, data) {
    let value = map.get(key);
    value = {
        ...value,
        ...data
    };
    map.set(key, value);
}
function $83c796b2b9543406$var$posToLineColumn(input, pos, tabWidth) {
    let line = $83c796b2b9543406$var$countNewLines(input, pos);
    let lineStart = input.lastIndexOf("\n", pos - 1) + 1;
    let column = $83c796b2b9543406$var$countColumn(input, lineStart, pos, tabWidth);
    return {
        line: line,
        column: column,
        pos: pos
    };
}
function $83c796b2b9543406$var$countNewLines(str, end) {
    let count = 0;
    for(let i = 0; i < end; i++)if (str[i] === "\n") count++;
    return count;
}
function $83c796b2b9543406$var$countColumn(str, start, end, tabWidth) {
    let count = 0;
    for(let i = start; i < end; i++)count += str[i] === "	" ? tabWidth : 1;
    return count;
}
const $83c796b2b9543406$var$ESCAPE_REGEX = /[~/]/g;
function $83c796b2b9543406$var$toJsonPointer(path) {
    let str = "";
    for (let e of path)if (typeof e === "string") str += e.replace($83c796b2b9543406$var$ESCAPE_REGEX, (v)=>v === "~" ? "~0" : "~1") + "/";
    else str += String(e) + "/";
    return str.slice(0, -1);
}


