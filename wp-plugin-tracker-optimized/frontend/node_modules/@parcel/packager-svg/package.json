{"name": "@parcel/packager-svg", "version": "2.15.2", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/SVGPackager.js", "source": "src/SVGPackager.js", "engines": {"node": ">= 16.0.0", "parcel": "^2.15.2"}, "dependencies": {"@parcel/plugin": "2.15.2", "@parcel/rust": "2.15.2", "@parcel/types": "2.15.2", "@parcel/utils": "2.15.2"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}