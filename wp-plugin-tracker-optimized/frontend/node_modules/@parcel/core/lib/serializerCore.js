"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.serializeRaw = exports.deserializeRaw = void 0;
function _v() {
  const data = _interopRequireDefault(require("v8"));
  _v = function () {
    return data;
  };
  return data;
}
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
let serializeRaw = exports.serializeRaw = _v().default.serialize;
let deserializeRaw = exports.deserializeRaw = _v().default.deserialize;