"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
exports.dependencyToInternalDependency = dependencyToInternalDependency;
exports.getPublicDependency = getPublicDependency;
var _types = require("../types");
function _nullthrows() {
  const data = _interopRequireDefault(require("nullthrows"));
  _nullthrows = function () {
    return data;
  };
  return data;
}
var _Environment = _interopRequireDefault(require("./Environment"));
var _Target = _interopRequireDefault(require("./Target"));
var _Symbols = require("./Symbols");
var _projectPath = require("../projectPath");
var _utils = require("../utils");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
const SpecifierTypeNames = Object.keys(_types.SpecifierType);
const PriorityNames = Object.keys(_types.Priority);
const inspect = Symbol.for('nodejs.util.inspect.custom');
const internalDependencyToDependency = new WeakMap();
const _dependencyToInternalDependency = new WeakMap();
function dependencyToInternalDependency(dependency) {
  return (0, _nullthrows().default)(_dependencyToInternalDependency.get(dependency));
}
function getPublicDependency(dep, options) {
  let existing = internalDependencyToDependency.get(dep);
  if (existing != null) {
    return existing;
  }
  return new Dependency(dep, options);
}
class Dependency {
  #dep /*: InternalDependency */;
  #options /*: ParcelOptions */;
  constructor(dep, options) {
    this.#dep = dep;
    this.#options = options;
    _dependencyToInternalDependency.set(this, dep);
    internalDependencyToDependency.set(dep, this);
    return this;
  }

  // $FlowFixMe
  [inspect]() {
    return `Dependency(${String(this.sourcePath)} -> ${this.specifier})`;
  }
  get id() {
    return this.#dep.id;
  }
  get specifier() {
    return this.#dep.specifier;
  }
  get specifierType() {
    return SpecifierTypeNames[this.#dep.specifierType];
  }
  get priority() {
    return PriorityNames[this.#dep.priority];
  }
  get needsStableName() {
    return this.#dep.needsStableName;
  }
  get bundleBehavior() {
    let bundleBehavior = this.#dep.bundleBehavior;
    return bundleBehavior == null ? null : _types.BundleBehaviorNames[bundleBehavior];
  }
  get isEntry() {
    return this.#dep.isEntry;
  }
  get isOptional() {
    return this.#dep.isOptional;
  }
  get loc() {
    return (0, _utils.fromInternalSourceLocation)(this.#options.projectRoot, this.#dep.loc);
  }
  get env() {
    return new _Environment.default(this.#dep.env, this.#options);
  }
  get packageConditions() {
    // Merge custom conditions with conditions stored as bitflags.
    // Order is not important because exports conditions are resolved
    // in the order they are declared in the package.json.
    let conditions = this.#dep.customPackageConditions;
    if (this.#dep.packageConditions) {
      conditions = conditions ? [...conditions] : [];
      for (let key in _types.ExportsCondition) {
        if (this.#dep.packageConditions & _types.ExportsCondition[key]) {
          conditions.push(key);
        }
      }
    }
    return conditions;
  }
  get meta() {
    return this.#dep.meta;
  }
  get symbols() {
    return new _Symbols.MutableDependencySymbols(this.#options, this.#dep);
  }
  get target() {
    let target = this.#dep.target;
    return target ? new _Target.default(target, this.#options) : null;
  }
  get sourceAssetId() {
    // TODO: does this need to be public?
    return this.#dep.sourceAssetId;
  }
  get sourcePath() {
    // TODO: does this need to be public?
    return (0, _projectPath.fromProjectPath)(this.#options.projectRoot, this.#dep.sourcePath);
  }
  get sourceAssetType() {
    return this.#dep.sourceAssetType;
  }
  get resolveFrom() {
    return (0, _projectPath.fromProjectPath)(this.#options.projectRoot, this.#dep.resolveFrom ?? this.#dep.sourcePath);
  }
  get range() {
    return this.#dep.range;
  }
  get pipeline() {
    return this.#dep.pipeline;
  }
}
exports.default = Dependency;