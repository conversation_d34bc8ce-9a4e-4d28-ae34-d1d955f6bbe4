"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "BuildError", {
  enumerable: true,
  get: function () {
    return _Parcel.BuildError;
  }
});
Object.defineProperty(exports, "INTERNAL_RESOLVE", {
  enumerable: true,
  get: function () {
    return _Parcel.INTERNAL_RESOLVE;
  }
});
Object.defineProperty(exports, "INTERNAL_TRANSFORM", {
  enumerable: true,
  get: function () {
    return _Parcel.INTERNAL_TRANSFORM;
  }
});
Object.defineProperty(exports, "Parcel", {
  enumerable: true,
  get: function () {
    return _Parcel.default;
  }
});
Object.defineProperty(exports, "createWorkerFarm", {
  enumerable: true,
  get: function () {
    return _Parcel.createWorkerFarm;
  }
});
Object.defineProperty(exports, "default", {
  enumerable: true,
  get: function () {
    return _Parcel.default;
  }
});
Object.defineProperty(exports, "deserialize", {
  enumerable: true,
  get: function () {
    return _serializer.deserialize;
  }
});
Object.defineProperty(exports, "prepareForSerialization", {
  enumerable: true,
  get: function () {
    return _serializer.prepareForSerialization;
  }
});
Object.defineProperty(exports, "registerSerializableClass", {
  enumerable: true,
  get: function () {
    return _serializer.registerSerializableClass;
  }
});
Object.defineProperty(exports, "restoreDeserializedObject", {
  enumerable: true,
  get: function () {
    return _serializer.restoreDeserializedObject;
  }
});
Object.defineProperty(exports, "serialize", {
  enumerable: true,
  get: function () {
    return _serializer.serialize;
  }
});
Object.defineProperty(exports, "unregisterSerializableClass", {
  enumerable: true,
  get: function () {
    return _serializer.unregisterSerializableClass;
  }
});
var _serializer = require("./serializer");
var _Parcel = _interopRequireWildcard(require("./Parcel"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }