"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.registerCoreWithSerializer = registerCoreWithSerializer;
function _graph() {
  const data = require("@parcel/graph");
  _graph = function () {
    return data;
  };
  return data;
}
var _serializer = require("./serializer");
var _AssetGraph = _interopRequireDefault(require("./AssetGraph"));
var _BundleGraph = _interopRequireDefault(require("./BundleGraph"));
var _ParcelConfig = _interopRequireDefault(require("./ParcelConfig"));
var _RequestTracker = require("./RequestTracker");
var _Config = _interopRequireDefault(require("./public/Config"));
var _package = _interopRequireDefault(require("../package.json"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
let coreRegistered;
function registerCoreWithSerializer() {
  if (coreRegistered) {
    return;
  }
  const packageVersion = _package.default.version;
  if (typeof packageVersion !== 'string') {
    throw new Error('Expected package version to be a string');
  }

  // $FlowFixMe[incompatible-cast]
  for (let [name, ctor] of Object.entries({
    AssetGraph: _AssetGraph.default,
    Config: _Config.default,
    BundleGraph: _BundleGraph.default,
    Graph: _graph().Graph,
    ParcelConfig: _ParcelConfig.default,
    RequestGraph: _RequestTracker.RequestGraph
    // $FlowFixMe[unclear-type]
  })) {
    (0, _serializer.registerSerializableClass)(packageVersion + ':' + name, ctor);
  }
  coreRegistered = true;
}