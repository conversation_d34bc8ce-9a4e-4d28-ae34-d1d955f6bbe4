{"name": "@parcel/core", "version": "2.15.2", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/index.js", "source": "src/index.js", "engines": {"node": ">= 16.0.0"}, "scripts": {"test": "mocha", "test-ci": "mocha", "check-ts": "tsc --noEmit index.d.ts"}, "dependencies": {"@mischnic/json-sourcemap": "^0.1.1", "@parcel/cache": "2.15.2", "@parcel/diagnostic": "2.15.2", "@parcel/events": "2.15.2", "@parcel/feature-flags": "2.15.2", "@parcel/fs": "2.15.2", "@parcel/graph": "3.5.2", "@parcel/logger": "2.15.2", "@parcel/package-manager": "2.15.2", "@parcel/plugin": "2.15.2", "@parcel/profiler": "2.15.2", "@parcel/rust": "2.15.2", "@parcel/source-map": "^2.1.1", "@parcel/types": "2.15.2", "@parcel/utils": "2.15.2", "@parcel/workers": "2.15.2", "base-x": "^3.0.11", "browserslist": "^4.24.5", "clone": "^2.1.2", "dotenv": "^16.5.0", "dotenv-expand": "^11.0.7", "json5": "^2.2.3", "msgpackr": "^1.11.2", "nullthrows": "^1.1.1", "semver": "^7.7.1"}, "devDependencies": {"@parcel/babel-register": "2.15.2", "@types/node": "^22.15.17", "graphviz": "^0.0.9", "tempy": "^0.2.1"}, "browser": {"./src/serializerCore.js": "./src/serializerCore.browser.js"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}