// // @flow
// 'use strict';

// const PackagerRunner = require('../src/PackagerRunner');
// const assert = require('assert');
// const path = require('path');

// const config = require('@parcel/config-default');

// describe('PackagerRunner', () => {
//   it('works', async () => {
//     let bundle = {
//       destPath: path.join(__dirname, 'dist', 'bundle.js'),
//       assets: [
//         {blobs: {code: require.resolve('./fixtures/module-a')}},
//         {blobs: {code: require.resolve('./fixtures/module-b')}},
//       ],
//     };

//     let packagerRunner = new PackagerRunner({
//       parcelConfig: config,
//       options: {}
//     });

//     await packagerRunner.runPackager({ bundle });
//   });
// });
