{"name": "@parcel/transformer-css", "version": "2.15.2", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/CSSTransformer.js", "source": "src/CSSTransformer.js", "engines": {"node": ">= 16.0.0", "parcel": "^2.15.2"}, "dependencies": {"@parcel/diagnostic": "2.15.2", "@parcel/plugin": "2.15.2", "@parcel/source-map": "^2.1.1", "@parcel/utils": "2.15.2", "browserslist": "^4.24.5", "lightningcss": "^1.30.1", "nullthrows": "^1.1.1"}, "devDependencies": {"lightningcss-wasm": "^1.30.1"}, "browser": {"lightningcss": "lightningcss-wasm"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}