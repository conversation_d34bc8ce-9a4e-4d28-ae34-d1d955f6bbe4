{"name": "@parcel/transformer-js", "version": "2.15.2", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/JSTransformer.js", "source": "src/JSTransformer.js", "scripts": {"test": "mocha"}, "engines": {"parcel": "^2.15.2", "node": ">= 16.0.0"}, "files": ["lib", "src"], "dependencies": {"@parcel/diagnostic": "2.15.2", "@parcel/plugin": "2.15.2", "@parcel/rust": "2.15.2", "@parcel/source-map": "^2.1.1", "@parcel/utils": "2.15.2", "@parcel/workers": "2.15.2", "@swc/helpers": "^0.5.0", "browserslist": "^4.24.5", "nullthrows": "^1.1.1", "regenerator-runtime": "^0.14.1", "semver": "^7.7.1"}, "peerDependencies": {"@parcel/core": "^2.15.2"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}