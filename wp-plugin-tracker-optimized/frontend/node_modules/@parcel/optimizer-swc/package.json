{"name": "@parcel/optimizer-swc", "version": "2.15.2", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/SwcOptimizer.js", "source": "src/SwcOptimizer.js", "engines": {"node": ">= 16.0.0", "parcel": "^2.15.2"}, "dependencies": {"@parcel/diagnostic": "2.15.2", "@parcel/plugin": "2.15.2", "@parcel/source-map": "^2.1.1", "@parcel/utils": "2.15.2", "@swc/core": "^1.11.24", "nullthrows": "^1.1.1"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}