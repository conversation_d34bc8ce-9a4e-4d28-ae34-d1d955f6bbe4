{"name": "@parcel/reporter-cli", "version": "2.15.2", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/CLIReporter.js", "source": "src/CLIReporter.js", "engines": {"node": ">= 16.0.0", "parcel": "^2.15.2"}, "targets": {"main": {"includeNodeModules": {"@parcel/plugin": false, "@parcel/types": false, "@parcel/utils": false, "chalk": false, "term-size": false}}}, "dependencies": {"@parcel/plugin": "2.15.2", "@parcel/types": "2.15.2", "@parcel/utils": "2.15.2", "chalk": "^4.1.2", "term-size": "^2.2.1"}, "devDependencies": {"@parcel/feature-flags": "2.15.2", "filesize": "^10.1.6", "nullthrows": "^1.1.1", "ora": "^5.4.1", "string-width": "^4.2.3", "wrap-ansi": "^7.0.0"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}