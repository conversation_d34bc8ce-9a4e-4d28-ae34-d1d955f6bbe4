{"name": "@parcel/transformer-babel", "version": "2.15.2", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/BabelTransformer.js", "source": "src/BabelTransformer.js", "engines": {"node": ">= 16.0.0", "parcel": "^2.15.2"}, "dependencies": {"@parcel/diagnostic": "2.15.2", "@parcel/plugin": "2.15.2", "@parcel/source-map": "^2.1.1", "@parcel/utils": "2.15.2", "browserslist": "^4.24.5", "json5": "^2.2.3", "nullthrows": "^1.1.1", "semver": "^7.7.1"}, "devDependencies": {"@babel/core": "^7.22.11", "@babel/preset-env": "^7.22.14", "@babel/types": "^7.22.11", "@parcel/types": "2.15.2"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}