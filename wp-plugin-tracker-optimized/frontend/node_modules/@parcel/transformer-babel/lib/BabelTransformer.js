"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _babelErrorUtils = require("./babelErrorUtils");
function _plugin() {
  const data = require("@parcel/plugin");
  _plugin = function () {
    return data;
  };
  return data;
}
function _utils() {
  const data = require("@parcel/utils");
  _utils = function () {
    return data;
  };
  return data;
}
function _sourceMap() {
  const data = _interopRequireDefault(require("@parcel/source-map"));
  _sourceMap = function () {
    return data;
  };
  return data;
}
function _semver() {
  const data = _interopRequireDefault(require("semver"));
  _semver = function () {
    return data;
  };
  return data;
}
var _babel = _interopRequireDefault(require("./babel7"));
var _config = require("./config");
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
var _default = exports.default = new (_plugin().Transformer)({
  loadConfig({
    config,
    options,
    logger
  }) {
    return (0, _config.load)(config, options, logger);
  },
  canReuseAST({
    ast
  }) {
    return ast.type === 'babel' && _semver().default.satisfies(ast.version, '^7.0.0');
  },
  async transform({
    asset,
    config,
    logger,
    options,
    tracer
  }) {
    try {
      if (config !== null && config !== void 0 && config.config) {
        if (asset.meta.babelPlugins != null && Array.isArray(asset.meta.babelPlugins)) {
          await (0, _babel.default)({
            asset,
            options,
            logger,
            babelOptions: config,
            additionalPlugins: asset.meta.babelPlugins,
            tracer
          });
        } else {
          await (0, _babel.default)({
            asset,
            options,
            logger,
            babelOptions: config,
            tracer
          });
        }
      }
      return [asset];
    } catch (e) {
      throw await (0, _babelErrorUtils.babelErrorEnhancer)(e, asset);
    }
  },
  async generate({
    asset,
    ast,
    options
  }) {
    let originalSourceMap = await asset.getMap();
    let sourceFileName = (0, _utils().relativeUrl)(options.projectRoot, asset.filePath);
    const babelCorePath = await options.packageManager.resolve('@babel/core', asset.filePath, {
      range: '^7.12.0',
      saveDev: true,
      shouldAutoInstall: options.shouldAutoInstall
    });
    const {
      default: generate
    } = await options.packageManager.require('@babel/generator', babelCorePath.resolved);
    let {
      code,
      rawMappings
    } = generate(ast.program, {
      sourceFileName,
      sourceMaps: !!asset.env.sourceMap,
      comments: true,
      importAttributesKeyword: 'with'
    });
    let map = new (_sourceMap().default)(options.projectRoot);
    if (rawMappings) {
      map.addIndexedMappings(rawMappings);
    }
    if (originalSourceMap) {
      // The babel AST already contains the correct mappings, but not the source contents.
      // We need to copy over the source contents from the original map.
      let sourcesContent = originalSourceMap.getSourcesContentMap();
      for (let filePath in sourcesContent) {
        let content = sourcesContent[filePath];
        if (content != null) {
          map.setSourceContent(filePath, content);
        }
      }
    }
    return {
      content: code,
      map
    };
  }
});