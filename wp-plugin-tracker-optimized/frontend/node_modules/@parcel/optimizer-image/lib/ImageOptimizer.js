"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
function _path() {
  const data = _interopRequireDefault(require("path"));
  _path = function () {
    return data;
  };
  return data;
}
function _process() {
  const data = _interopRequireDefault(require("process"));
  _process = function () {
    return data;
  };
  return data;
}
function _plugin() {
  const data = require("@parcel/plugin");
  _plugin = function () {
    return data;
  };
  return data;
}
function _utils() {
  const data = require("@parcel/utils");
  _utils = function () {
    return data;
  };
  return data;
}
function _diagnostic() {
  const data = require("@parcel/diagnostic");
  _diagnostic = function () {
    return data;
  };
  return data;
}
function _rust() {
  const data = require("@parcel/rust");
  _rust = function () {
    return data;
  };
  return data;
}
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
var _default = exports.default = new (_plugin().Optimizer)({
  async optimize({
    bundle,
    contents,
    logger
  }) {
    if (!bundle.env.shouldOptimize) {
      return {
        contents
      };
    }
    let buffer = await (0, _utils().blobToBuffer)(contents);

    // Attempt to optimize it, if the optimize fails we log a warning...
    try {
      let optimized = (0, _rust().optimizeImage)(bundle.type, buffer);
      return {
        contents: optimized.length < buffer.length ? optimized : buffer
      };
    } catch (err) {
      var _bundle$getMainEntry;
      const filepath = (_bundle$getMainEntry = bundle.getMainEntry()) === null || _bundle$getMainEntry === void 0 ? void 0 : _bundle$getMainEntry.filePath;
      const filename = filepath ? _path().default.relative(_process().default.cwd(), filepath) : 'unknown';
      logger.warn({
        message: (0, _diagnostic().md)`Could not optimize image ${filename}: ${err.message}`,
        stack: err.stack
      });
    }
    return {
      contents: buffer
    };
  }
});