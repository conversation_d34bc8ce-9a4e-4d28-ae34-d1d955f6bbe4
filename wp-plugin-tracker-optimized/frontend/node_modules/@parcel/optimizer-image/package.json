{"name": "@parcel/optimizer-image", "version": "2.15.2", "license": "MIT", "main": "lib/ImageOptimizer.js", "source": "src/ImageOptimizer.js", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "engines": {"node": ">= 16.0.0", "parcel": "^2.15.2"}, "files": ["lib"], "dependencies": {"@parcel/diagnostic": "2.15.2", "@parcel/plugin": "2.15.2", "@parcel/rust": "2.15.2", "@parcel/utils": "2.15.2", "@parcel/workers": "2.15.2"}, "peerDependencies": {"@parcel/core": "^2.15.2"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}