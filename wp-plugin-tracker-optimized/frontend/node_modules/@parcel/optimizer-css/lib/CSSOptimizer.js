"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
function _sourceMap() {
  const data = _interopRequireDefault(require("@parcel/source-map"));
  _sourceMap = function () {
    return data;
  };
  return data;
}
function _plugin() {
  const data = require("@parcel/plugin");
  _plugin = function () {
    return data;
  };
  return data;
}
function _lightningcss() {
  const data = _interopRequireWildcard(require("lightningcss"));
  _lightningcss = function () {
    return data;
  };
  return data;
}
function _utils() {
  const data = require("@parcel/utils");
  _utils = function () {
    return data;
  };
  return data;
}
function _browserslist() {
  const data = _interopRequireDefault(require("browserslist"));
  _browserslist = function () {
    return data;
  };
  return data;
}
function _nullthrows() {
  const data = _interopRequireDefault(require("nullthrows"));
  _nullthrows = function () {
    return data;
  };
  return data;
}
function _path() {
  const data = _interopRequireDefault(require("path"));
  _path = function () {
    return data;
  };
  return data;
}
function _diagnostic() {
  const data = require("@parcel/diagnostic");
  _diagnostic = function () {
    return data;
  };
  return data;
}
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
// $FlowFixMe - init for browser build.
var _default = exports.default = new (_plugin().Optimizer)({
  async loadConfig({
    config,
    logger,
    options
  }) {
    const configFile = await config.getConfig(['.cssnanorc', 'cssnano.config.json', 'cssnano.config.js', 'cssnano.config.cjs'], {
      packageKey: 'cssnano'
    });
    if (configFile) {
      let filename = _path().default.basename(configFile.filePath);
      let codeHighlights;
      let message;
      if (filename === 'package.json') {
        message = (0, _diagnostic().md)`
Parcel\'s default CSS minifer changed from cssnano to lightningcss, but a "cssnano" key was found in **package.json**. Either remove this configuration, or configure Parcel to use @parcel/optimizer-cssnano instead.
        `;
        let contents = await options.inputFS.readFile(configFile.filePath, 'utf8');
        codeHighlights = (0, _diagnostic().generateJSONCodeHighlights)(contents, [{
          key: '/cssnano',
          type: 'key'
        }]);
      } else {
        message = (0, _diagnostic().md)`Parcel\'s default CSS minifer changed from cssnano to lightningcss, but a __${filename}__ config file was found. Either remove this config file, or configure Parcel to use @parcel/optimizer-cssnano instead.`;
        codeHighlights = [{
          start: {
            line: 1,
            column: 1
          },
          end: {
            line: 1,
            column: 1
          }
        }];
      }
      logger.warn({
        message,
        documentationURL: 'https://parceljs.org/languages/css/#minification',
        codeFrames: [{
          filePath: configFile.filePath,
          codeHighlights
        }]
      });
    }
  },
  async optimize({
    bundle,
    bundleGraph,
    logger,
    contents: prevContents,
    getSourceMapReference,
    map: prevMap,
    options
  }) {
    if (!bundle.env.shouldOptimize) {
      return {
        contents: prevContents,
        map: prevMap
      };
    }
    let targets = getTargets(bundle.env.engines.browsers);
    let code = await (0, _utils().blobToBuffer)(prevContents);
    let unusedSymbols;
    if (bundle.env.shouldScopeHoist) {
      unusedSymbols = [];
      bundle.traverseAssets(asset => {
        if (asset.symbols.isCleared || asset.meta.cssModulesCompiled === 'postcss') {
          return;
        }
        let usedSymbols = bundleGraph.getUsedSymbols(asset);
        if (usedSymbols == null) {
          return;
        }
        let defaultImport = null;
        if (usedSymbols.has('default')) {
          let incoming = bundleGraph.getIncomingDependencies(asset);
          defaultImport = incoming.find(d => d.symbols.hasExportSymbol('default'));
          if (defaultImport) {
            var _defaultImport$symbol;
            let loc = (_defaultImport$symbol = defaultImport.symbols.get('default')) === null || _defaultImport$symbol === void 0 ? void 0 : _defaultImport$symbol.loc;
            logger.warn({
              message: 'CSS modules cannot be tree shaken when imported with a default specifier',
              ...(loc && {
                codeFrames: [{
                  filePath: (0, _nullthrows().default)((loc === null || loc === void 0 ? void 0 : loc.filePath) ?? defaultImport.sourcePath),
                  codeHighlights: [(0, _diagnostic().convertSourceLocationToHighlight)(loc)]
                }]
              }),
              hints: [`Instead do: import * as style from "${defaultImport.specifier}";`],
              documentationURL: 'https://parceljs.org/languages/css/#tree-shaking'
            });
          }
        }
        if (!defaultImport && !usedSymbols.has('*')) {
          for (let [symbol, {
            local
          }] of asset.symbols) {
            if (local !== 'default' && !usedSymbols.has(symbol)) {
              unusedSymbols.push(local);
            }
          }
        }
      });
    }

    // Inline style attributes in HTML need to be parsed differently from full CSS files.
    if (bundle.bundleBehavior === 'inline') {
      let entry = bundle.getMainEntry();
      if ((entry === null || entry === void 0 ? void 0 : entry.meta.type) === 'attr') {
        let result = (0, _lightningcss().transformStyleAttribute)({
          code,
          minify: true,
          targets
        });
        return {
          contents: Buffer.from(result.code)
        };
      }
    }

    // $FlowFixMe
    if (process.browser) {
      await (0, _lightningcss().default)();
    }
    let result = (0, _lightningcss().transform)({
      filename: bundle.name,
      code,
      minify: true,
      sourceMap: !!bundle.env.sourceMap,
      targets,
      unusedSymbols
    });
    let map;
    if (result.map != null) {
      let vlqMap = JSON.parse(Buffer.from(result.map).toString());
      map = new (_sourceMap().default)(options.projectRoot);
      map.addVLQMap(vlqMap);
      if (prevMap) {
        map.extends(prevMap);
      }
    }
    let contents = Buffer.from(result.code);
    if (bundle.env.sourceMap) {
      let reference = await getSourceMapReference(map);
      if (reference != null) {
        contents = contents.toString() + '\n' + '/*# sourceMappingURL=' + reference + ' */\n';
      }
    }
    return {
      contents: Buffer.from(contents),
      map
    };
  }
});
let cache = new Map();
function getTargets(browsers) {
  if (browsers == null) {
    return undefined;
  }
  let cached = cache.get(browsers);
  if (cached != null) {
    return cached;
  }
  let targets = (0, _lightningcss().browserslistToTargets)((0, _browserslist().default)(browsers));
  cache.set(browsers, targets);
  return targets;
}