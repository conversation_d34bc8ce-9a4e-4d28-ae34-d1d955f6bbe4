{"name": "@parcel/events", "version": "2.15.2", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/index.js", "source": "src/index.js", "engines": {"node": ">= 16.0.0"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}