{"mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iBAAiB;AACjB,4BAAM,IAAI,GAAG;;AAIb,SAAS,mCAAc,IAAI,EAAE,OAAO;IAClC,IAAI,UAAU,QAAQ,OAAO,KAAK,YAChC,QAAQ,OAAO,GAAG,QAAQ,GAAG,CAAC,OAAO;IAEvC,IAAI,CAAC,SACH,OAAO;IAGT,UAAU,QAAQ,KAAK,CAAC;IACxB,IAAI,QAAQ,OAAO,CAAC,QAAQ,IAC1B,OAAO;IAET,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;QACvC,IAAI,IAAI,OAAO,CAAC,EAAE,CAAC,WAAW;QAC9B,IAAI,KAAK,KAAK,MAAM,CAAC,CAAC,EAAE,MAAM,EAAE,WAAW,OAAO,GAChD,OAAO;IAEX;IACA,OAAO;AACT;AAEA,SAAS,gCAAW,IAAI,EAAE,IAAI,EAAE,OAAO;IACrC,IAAI,CAAC,KAAK,cAAc,MAAM,CAAC,KAAK,MAAM,IACxC,OAAO;IAET,OAAO,mCAAa,MAAM;AAC5B;AAEA,SAAS,4BAAO,IAAI,EAAE,OAAO,EAAE,EAAE;IAC/B,eAAQ,MAAM,SAAU,EAAE,EAAE,IAAI;QAC9B,GAAG,IAAI,KAAK,QAAQ,gCAAU,MAAM,MAAM;IAC5C;AACF;AAEA,SAAS,2BAAM,IAAI,EAAE,OAAO;IAC1B,OAAO,gCAAU,mBAAY,OAAO,MAAM;AAC5C;;;;;ACzCA,iBAAiB;AACjB,4BAAM,IAAI,GAAG;;AAIb,SAAS,4BAAO,IAAI,EAAE,OAAO,EAAE,EAAE;IAC/B,eAAQ,MAAM,SAAU,EAAE,EAAE,IAAI;QAC9B,GAAG,IAAI,KAAK,QAAQ,gCAAU,MAAM;IACtC;AACF;AAEA,SAAS,2BAAM,IAAI,EAAE,OAAO;IAC1B,OAAO,gCAAU,mBAAY,OAAO;AACtC;AAEA,SAAS,gCAAW,IAAI,EAAE,OAAO;IAC/B,OAAO,KAAK,MAAM,MAAM,gCAAU,MAAM;AAC1C;AAEA,SAAS,gCAAW,IAAI,EAAE,OAAO;IAC/B,IAAI,MAAM,KAAK,IAAI;IACnB,IAAI,MAAM,KAAK,GAAG;IAClB,IAAI,MAAM,KAAK,GAAG;IAElB,IAAI,QAAQ,QAAQ,GAAG,KAAK,YAC1B,QAAQ,GAAG,GAAG,QAAQ,MAAM,IAAI,QAAQ,MAAM;IAChD,IAAI,QAAQ,QAAQ,GAAG,KAAK,YAC1B,QAAQ,GAAG,GAAG,QAAQ,MAAM,IAAI,QAAQ,MAAM;IAEhD,IAAI,IAAI,SAAS,OAAO;IACxB,IAAI,IAAI,SAAS,OAAO;IACxB,IAAI,IAAI,SAAS,OAAO;IACxB,IAAI,KAAK,IAAI;IAEb,IAAI,MAAM,AAAC,MAAM,KACf,AAAC,MAAM,KAAM,QAAQ,SACrB,AAAC,MAAM,KAAM,QAAQ,SACrB,AAAC,MAAM,MAAO,UAAU;IAE1B,OAAO;AACT;;;;;ACxCA;;qCAEI;;yCACA;;;AAGJ,IAAI,+BAAS,UAAG,MAAM;AACtB,IAAI,mCAAa,UAAG,UAAU;AAC9B,IAAI,kCAAY,UAAG,SAAS,IAAI;AAEhC,IAAI,uCAAiB,QAAQ,QAAQ,IAAI;AAEzC,IAAI,sCAAgB,SAAS,WAAW,EAAE,QAAQ;IAC9C,6BAAO,aAAa,gCAAU,IAAI,EAClC,SAAS,GAAG;QACR,SAAS,CAAC;IACd;AACJ;AAEA,IAAI,0CAAoB,SAAS,WAAW;IACxC,IAAG;QACC,iCAAW,aAAa,gCAAU,IAAI;QACtC,OAAO;IACX,EAAC,OAAM,GAAE;QACL,OAAO;IACX;AACJ;AAEA,IAAI,wCAAkB,SAAS,WAAW,EAAE,QAAQ;IAChD,6BAAO,aAAa,gCAAU,IAAI,GAAG,gCAAU,IAAI,EAC/C,SAAS,GAAG;QACZ,SAAS,MAAM,CAAC;IACpB;AACJ;AAEA,IAAI,4CAAsB,SAAS,WAAW;IAC1C,IAAG;QACC,iCAAW,aAAa,gCAAU,IAAI,GAAG,gCAAU,IAAI;QACvD,OAAO;IACX,EAAC,OAAM,GAAE;QACL,OAAO;IACX;AACJ;AAEA,IAAI,0CAAoB,SAAS,WAAW,EAAE,kBAAkB,EAAE,QAAQ;IAEtE,oCAAc,aAAa,SAAS,MAAM;QAEtC,IAAG,CAAC,QAAO;YACP,IAAI,QAAQ,+BAAK,gBAAgB,qBAC3B,iBACA,oBAAoB,qBAAqB,eACzC,SAAU,KAAK,EAAE,MAAM,EAAE,MAAM;gBAC3B,SAAS,MAAM,CAAC,CAAC;YACrB;YACN;QACJ;QAEA,sCAAgB,aAAa;IACjC;AAEJ;AAEA,IAAI,6CAAuB,SAAS,WAAW,EAAE,kBAAkB,EAAE,QAAQ;IAC3E,mGAAmG;IACnG,IAAI,CAAE,uFAAuF,IAAI,CAAC,cAAe;QAC/G,SAAS,MAAM;QACf;IACF;IACA,IAAI,QAAQ,+BAAK,WAAW,oBAC1B,SAAU,KAAK;QACb,IAAI,UAAU,MACZ,SAAS,MAAM;aAEf,SAAS,MAAM;IAEnB;AAEJ;AAEA,IAAI,8CAAwB,SAAS,WAAW,EAAE,kBAAkB;IAClE,IAAG,wCAAkB,cACjB,IAAI;QACF,IAAI,SAAS,mCAAS,gBAAgB,qBAChC,iBACA,oBAAoB,qBAAqB;QAE/C,OAAO,CAAC,CAAC;IACX,EAAE,OAAO,OAAO;QACd,OAAO;IACT;IAEJ,OAAO,0CAAoB;AAC7B;AAEA,IAAI,iDAA2B,SAAS,WAAW,EAAE,kBAAkB,EAAE,QAAQ;IAC/E,mGAAmG;IACnG,IAAI,CAAE,uFAAuF,IAAI,CAAC,cAChG,OAAO;IAET,IAAI;QACA,IAAI,SAAS,mCAAS,WAAW,oBAAoB;YAAC,OAAO,EAAE;QAAA;QAC/D,OAAO,CAAC,CAAC;IACb,EAAE,OAAO,OAAO;QACZ,OAAO;IACX;AACF;AAEA,IAAI,mCAAa,SAAS,CAAC;IACzB,IAAI,qBAAqB,IAAI,CAAC,IAAI;QAChC,IAAI,MAAI,EAAE,OAAO,CAAC,MAAK,WAAS;QAChC,IAAI,EAAE,OAAO,CAAC,aAAa,IAAI,4CAA4C;SACxE,OAAO,CAAC,UAAU,QAAS,0EAA0E;IAC1G;IACA,OAAO;AACT;AAEA,IAAI,sCACF,mCAAa,SAAS,CAAC;IACrB,IAAI,aAAa,OAAO,IAAI,CAAC;IAC7B,IAAI,YAAY;QACd,IAAI,UAAU,MAAM,oBAAa,KAAK;QACtC,IAAI,WAAW,MAAM,qBAAc,KAAK;QACxC,OAAO,UAAU,MAAM;IACzB;IACA,OAAO,MAAM,IAAI;AACnB;AAGF,iBAAiB,SAAS,cAAc,WAAW,EAAE,QAAQ;IAC3D,IAAI,qBAAqB,iCAAW;IACpC,IAAI,CAAC,YAAY,OAAO,YAAY,aAClC,OAAO,IAAI,QAAQ,SAAS,OAAO,EAAE,MAAM;QACzC,cAAc,aAAa,SAAS,KAAK,EAAE,MAAM;YAC/C,IAAI,QACF,QAAQ;iBAER,OAAO;QAEX;IACF;IAEF,IAAI,sCACF,2CAAqB,aAAa,oBAAoB;SAEtD,wCAAkB,aAAa,oBAAoB;AAEvD;AAEA,eAAe,IAAI,GAAG,SAAS,WAAW;IACxC,IAAI,qBAAqB,iCAAW;IACpC,IAAI,sCACF,OAAO,+CAAyB,aAAa;SAE7C,OAAO,4CAAsB,aAAa;AAE9C;;;;;;;;;;;;;;;;;;;;;;;;;AC1HA,mDAAA;AACA,MAAMmB,OAAO;AACb,MAAMC,SAAS;AACf,MAAMC,iBAAiB;AACvB,MAAMC,mBAAmB;AACzB,MAAMC,UAAUJ,OAAOC;AACvB,MAAMI,aAAaH,iBAAiBC;AAEpC,MAAMG,eAAe,GAAGhB,CAAAA,GAAAA,qCAAAA,EAAKiB,GAAG,CAAA,YAAA,EAAejB,CAAAA,GAAAA,qCAAAA,EAAKiB,GAAG,EAAE;AAEzD,MAAMC,UAAU;AAChB,MAAMC,SAAS;AACf,MAAMC,aAAa;AAEnB,sGAAA;AACA,gGAAA;AACA,MAAMC,QAAQ,IAAIC;AAClB,MAAMC,WAAW,IAAID;AACrB,MAAME,qBAAqB,IAAIF;AAOxB,MAAMG;IAOXC,YACEC,EAAc,EACdC,WAAqB,EACrBC,SAA6B,CAC7B;QACA,IAAI,CAACF,EAAE,GAAGA;QACV,IAAI,CAACC,WAAW,GAAGA;QACnB,IAAI,CAACC,SAAS,GAAGA;QAEjB,uCAAA;QACA,IAAI,CAACC,iBAAiB,GAAGC,OAAOC,IAAI,CAACjC,CAAAA,GAAAA,uCAAAA,EAAOkC,WAAW,EAAEC,GAAG,CAACC,CAAAA,IAC3DA,EAAEC,SAAS,CAAC;IAEhB;IAEAC,kBAAgC;QAC9B,OAAO,IAAI9B,CAAAA,GAAAA,0CAAAA,EAAa,IAAI,CAACqB,WAAW,EAAE;YACxCD,IACE,IAAI,CAACA,EAAE,YAAY9B,CAAAA,GAAAA,sBAAAA,KAAUyC,QAAQC,QAAQ,CAACC,GAAG,IAAI,OACjDC,YACA;gBACEC,MAAM1C,CAAAA,OAAQ,IAAI,CAAC2B,EAAE,CAACgB,YAAY,CAAC3C;gBACnC4C,MAAM5C,CAAAA;oBACJ,IAAI6C,QAAQ;oBACZ,IAAI;wBACF,IAAIC,OAAO,IAAI,CAACnB,EAAE,CAACoB,SAAS,CAAC/C;wBAC7B,IAAI8C,KAAKE,cAAc,IAAI;4BACzBH,SAASzB;4BACT0B,OAAO,IAAI,CAACnB,EAAE,CAACsB,QAAQ,CAACjD;wBAC1B;wBACA,IAAI8C,KAAKI,MAAM,IACbL,SAAS3B;6BACJ,IAAI4B,KAAKK,WAAW,IACzBN,SAAS1B;oBAEb,EAAE,OAAOiC,KAAK;oBACZ,SAAA;oBAAA;oBAEF,OAAOP;gBACT;gBACAQ,UAAUrD,CAAAA,OAAQ,IAAI,CAAC2B,EAAE,CAAC2B,YAAY,CAACtD;YACzC;YACNuD,MAAM;YACNC,SAAS1C;YACT2C,YAAY1C;YACZ2C,gBAAgB;YAChBC,mBACErB,QAAQC,QAAQ,CAACC,GAAG,IAAI,OACpB,CAACoB,SAAQC;gBACP,2BAAA;gBACA,IAAIrB,MAAMzC,CAAAA,GAAAA,uCAAAA,EAAO+D,UAAU,CAAC9D,CAAAA,GAAAA,qCAAAA,EAAK+D,OAAO,CAACF;gBAEzC,OAAOrB,IAAIwB,oBAAoB,CAC7B,kDAAA;gBACAJ,UAAS,KACTC;YAEJ,IACApB;YACNwB,YAAY,IAAI,CAACnC,iBAAiB;YAClCoC,YAAY;QACd;IACF;IAEA,OAAOC,YAAYC,IAAS,EAAsB;QAChD,OAAO,IAAI3C,mBAAmB2C,KAAKzC,EAAE,EAAEyC,KAAKxC,WAAW,EAAEwC,KAAKvC,SAAS;IACzE;IAEAwC,YAKG;QACD,OAAO;YACLC,OAAO;YACP3C,IAAI,IAAI,CAACA,EAAE;YACXC,aAAa,IAAI,CAACA,WAAW;YAC7BC,WAAW,IAAI,CAACA,SAAhBA;QACF;IACF;IAEA,MAAM0C,QACJC,IAAyB,EACzBX,IAAc,EACdO,IAIE,EACY;QACd,IAAI,EAACK,QAAQ,EAAEC,IAAAA,EAAK,GAAG,MAAM,IAAI,CAACC,OAAO,CAACH,MAAMX,MAAMO;QACtD,IAAIM,SAAS,GAAG;YACdxE,CAAAA,GAAAA,6CAAAA,EAAO0E,IAAI,CAAC;gBACVC,SAAS;gBACTC,QAAQ;gBACRC,YAAY;oBACV;wBACEC,UAAUP;wBACVQ,gBAAgB,EAAhBA;oBACF;iBAAA;YAEJ;YAEA,4DAAA;YACA,IAAI3C,QAAQ4C,QAAQ,KAAK,WAAWlF,CAAAA,GAAAA,qCAAAA,EAAKmF,UAAU,CAACV,WAClDA,WAAWjE,CAAAA,GAAAA,wBAAAA,EAAciE;YAG3B,aAAA;YACA,OAAO,MAAM,CAACA;QAChB;QACA,OAAO,IAAI,CAACW,IAAI,CAACX,UAAUZ;IAC7B;IAEAwB,YAAYb,IAAyB,EAAEX,IAAc,EAAO;QAC1D,IAAI,EAACY,QAAAA,EAAS,GAAG,IAAI,CAACa,WAAW,CAACd,MAAMX;QACxC,OAAO,IAAI,CAACuB,IAAI,CAACX,UAAUZ;IAC7B;IAEAuB,KAAKJ,QAAkB,EAAEnB,IAAc,EAAO;QAC5C,IAAI,CAAC7D,CAAAA,GAAAA,qCAAAA,EAAKmF,UAAU,CAACH,WACnB,sBAAA;QACA,aAAA;QACA,OAAOT,QAAQS;QAGjB,2BAAA;QACA,MAAMO,eAAexF,CAAAA,GAAAA,uCAAAA,EAAOyF,MAAM,CAACR,SAAS;QAC5C,IAAIO,iBAAiB9C,WACnB,OAAO8C,aAAaE,OAAO;QAG7B,aAAA;QACA,IAAIC,IAAI,IAAI3F,CAAAA,GAAAA,uCAAAA,EAAOiF,UAAUjF,CAAAA,GAAAA,uCAAAA,EAAOyF,MAAM,CAAC3B,KAAK,IAAID,OAAO+B,MAAM;QAEjE,qCAAA;QACA,MAAM1B,aAAalC,OAAOC,IAAI,CAACjC,CAAAA,GAAAA,uCAAAA,EAAOkC,WAAW;QACjD,8FAAA;QACA,iGAAA;QACA,yBAAA;QACA,IAAIgC,WAAW2B,MAAM,KAAK,IAAI,CAAC9D,iBAAiB,CAAC8D,MAAM,EAAE;YACvD,IAAI,CAAC9D,iBAAiB,GAAGmC,WAAW/B,GAAG,CAACC,CAAAA,IAAKA,EAAEC,SAAS,CAAC;YACzD,IAAI,CAACyD,QAAQ,GAAG,IAAI,CAACxD,eAAe;QACtC;QAEA,2BAAA;QACAtC,CAAAA,GAAAA,uCAAAA,EAAOyF,MAAM,CAACR,SAAS,GAAGU;QAE1B,kEAAA;QACAA,EAAEnB,OAAO,GAAGuB,CAAAA;YACV,OAAO,IAAI,CAACT,WAAW,CAACS,IAAId;QAC9B;QAEA,8EAAA;QACA,IAAI,EAACrC,YAAY,EAAEM,QAAAA,EAAS,GAAGnD,CAAAA,GAAAA,mCAAAA;QAC/B,aAAA;QACAA,CAAAA,GAAAA,mCAAAA,EAAS6C,YAAY,GAAG,CAACoD,UAAUC;YACjC,OAAO,IAAI,CAACrE,EAAE,CAACgB,YAAY,CAACoD,UAAUC;QACxC;QAEA,aAAA;QACAlG,CAAAA,GAAAA,mCAAAA,EAASmD,QAAQ,GAAG8C,CAAAA;YAClB,OAAO,IAAI,CAACpE,EAAE,CAACsB,QAAQ,CAAC8C;QAC1B;QAEA,IAAI,CAACf,SAASiB,QAAQ,CAACjF,eAAe;YACpC,IAAIkF,UAAUlG,CAAAA,GAAAA,qCAAAA,EAAKkG,OAAO,CAAClB;YAC3B,IACE,AAACkB,CAAAA,YAAY,SACXA,YAAY,UACZA,YAAY,UACZA,YAAY,MAAA,KACd,aAAA;YACA,CAACnG,CAAAA,GAAAA,uCAAAA,EAAOkC,WAAW,CAACiE,QAAQ,EAC5B;gBACA,IAAIC,UAAUT,EAAEU,QAAQ;gBACxBV,EAAEU,QAAQ,GAAG,CAACC,MAAMN;oBAClB,IAAIO,MAAM7F,CAAAA,GAAAA,4BAAAA,EAAc4F,MAAM;kCAC5BN;wBACAnC,QAAQ;4BAACc,MAAM;4BAAY6B,eAAe;wBAAI;oBAChD;oBACAJ,QAAQK,IAAI,CAACd,GAAGY,IAAID,IAAI,EAAEN;gBAC5B;gBAEA,aAAA;gBACAhG,CAAAA,GAAAA,uCAAAA,EAAOkC,WAAW,CAACiE,QAAQ,GAAG,CAACR,GAAGK;oBAChC,aAAA;oBACA,OAAOhG,CAAAA,GAAAA,uCAAAA,EAAOkC,WAAW,CAACiE,QAAQ;oBAClC,aAAA;oBACAnG,CAAAA,GAAAA,uCAAAA,EAAOkC,WAAW,CAAC,MAAM,CAACyD,GAAGK;gBAC/B;YACF;QACF;QAEA,IAAI;YACFL,EAAEN,IAAI,CAACJ;QACT,EAAE,OAAO5B,KAAK;YACZ,2BAAA;YACA,OAAOrD,CAAAA,GAAAA,uCAAAA,EAAOyF,MAAM,CAACR,SAAS;YAC9B,MAAM5B;QACR,SAAU;YACR,aAAA;YACAtD,CAAAA,GAAAA,mCAAAA,EAAS6C,YAAY,GAAGA;YACxB,aAAA;YACA7C,CAAAA,GAAAA,mCAAAA,EAASmD,QAAQ,GAAGA;QACtB;QAEA,OAAOyC,EAAED,OAAO;IAClB;IAEA,MAAMd,QACJmB,EAAuB,EACvBjC,IAAc,EACd4C,OAIE,EACoC;QACtC,IAAIC,UAAU1G,CAAAA,GAAAA,qCAAAA,EAAK+D,OAAO,CAACF;QAC3B,IAAI8C,MAAMD,UAAU,MAAMZ;QAC1B,IAAIrB,WAAWpD,MAAMuF,GAAG,CAACD;QACzB,IAAI,CAAClC,UAAU;YACb,IAAI,CAACD,KAAK,GAAGrE,CAAAA,GAAAA,iCAAAA,EAAe2F;YAC5B,IAAI;gBACFrB,WAAW,IAAI,CAACoC,eAAe,CAACf,IAAIjC;YACtC,EAAE,OAAO1B,GAAG;gBACV,IACEA,EAAEkE,IAAI,KAAK,sBACXI,SAASK,sBAAsB,QAC/BhB,GAAGiB,UAAU,CAAC,KAAK,kCAHrB;kBAIE;oBACA,IACE5E,EAAEkE,IAAI,KAAK,sBACXI,SAASK,sBAAsB,MAC/B;wBACA,IAAI1D,MAAM,IAAI5D,CAAAA,GAAAA,iDAAAA,EAAoB;4BAChCwH,YAAY;gCACVnC,SAASnF,CAAAA,GAAAA,sCAAAA,EAAeyC,EAAE0C,OAAO;gCACjCoC,OAAO;oCACL;iCADFA;4BAGF;wBACF;wBACA,2CAAA;wBACA7D,IAAIiD,IAAI,GAAG;wBACX,MAAMjD;oBACR,OACE,MAAMjB;gBAEV;gBAEA,IAAI+E,YAAY,MAAM9G,CAAAA,GAAAA,sCAAAA,EACpB,IAAI,CAACuB,EAAE,EACP6C,MACAX,MACA,IAAI,CAACjC,WACP;gBAEA,IAAIsF,aAAa,MAAM;oBACrB,IAAI,CAACC,UAAU,CAACrB,IAAIjC;oBACpB,MAAM,IAAI,CAACuD,OAAO,CAAC;wBAAC;kCAAC5C;4BAAM6C,OAAOZ,SAASY;wBAAK;qBAAE,EAAExD,MAAM;wBACxDyD,SAASb,SAASa,WAAW;oBAC/B;oBAEA,OAAO,IAAI,CAAC3C,OAAO,CAACmB,IAAIjC,MAAM;wBAC5B,GAAG4C,OAAO;wBACVK,mBAAmB;oBACrB;gBACF;gBAEA,MAAM,IAAItH,CAAAA,GAAAA,iDAAAA,EAAoB;oBAC5BwH,YAAYE,UAAUK,MAAM,CAACrF,GAAG,CAACsF,CAAAA,QAAU,CAAA;4BACzC3C,SAASjF,CAAAA,GAAAA,0BAAAA,CAAE,CAAA,uBAAA,EAA0B4E,KAAI,qEAAA,CAAuE;4BAChHM,QAAQ;4BACRC,YAAY;gCACV;oCACEC,UAAUkC,UAAUlC,QAAQ;oCAC5ByC,UAAU;oCACVpB,MAAMa,UAAUQ,IAAI;oCACpBzC,gBAAgBtF,CAAAA,GAAAA,kDAAAA,EAA2BuH,UAAUQ,IAAI,EAAE;wCACzD;4CACEf,KAAK,CAAA,CAAA,EAAIa,MAAK,CAAA,EAAI/H,CAAAA,GAAAA,8CAAAA,EAAuB+E,OAAO;4CAChDE,MAAM;4CACNG,SAAS;wCACX;qCACD;gCACH;6BAAA;wBAEJ,CAAA;gBACF;YACF;YAEA,IAAIwC,QAAQZ,SAASY;YACrB,IAAIA,SAAS,MAAM;gBACjB,IAAI/G,MAAMmE,SAASnE,GAAG;gBACtB,IAAIA,OAAO,QAAQ,CAACL,CAAAA,GAAAA,uCAAAA,EAAO0H,SAAS,CAACrH,IAAIsH,OAAO,EAAEP,QAAQ;oBACxD,IAAIH,YAAY,MAAM9G,CAAAA,GAAAA,sCAAAA,EACpB,IAAI,CAACuB,EAAE,EACP6C,MACAX,MACA,IAAI,CAACjC,WACP;oBAEA,IAAIsF,aAAa,QAAQT,SAASK,sBAAsB,MAAM;wBAC5D,IAAI,CAACK,UAAU,CAACrB,IAAIjC;wBACpB,MAAM,IAAI,CAACuD,OAAO,CAAC;4BAAC;sCAAC5C;uCAAM6C;4BAAK;yBAAE,EAAExD;wBACpC,OAAO,IAAI,CAACc,OAAO,CAACmB,IAAIjC,MAAM;4BAC5B,GAAG4C,OAAO;4BACVK,mBAAmB;wBACrB;oBACF,OAAO,IAAII,aAAa,MACtB,MAAM,IAAI1H,CAAAA,GAAAA,iDAAAA,EAAoB;wBAC5BwH,YAAY;4BACVnC,SAASjF,CAAAA,GAAAA,0BAAAA,CAAE,CAAA,uBAAA,EAA0B4E,KAAI,aAAA,EAAgB6C,MAAK,CAAA,CAAG;4BACjEvC,QAAQ;4BACRC,YAAY;gCACV;oCACEC,UAAUkC,UAAUlC,QAAQ;oCAC5ByC,UAAU;oCACVpB,MAAMa,UAAUQ,IAAI;oCACpBzC,gBAAgBtF,CAAAA,GAAAA,kDAAAA,EACduH,UAAUQ,IAAI,EACdR,UAAUK,MAAM,CAACrF,GAAG,CAACsF,CAAAA,QAAU,CAAA;4CAC7Bb,KAAK,CAAA,CAAA,EAAIa,MAAK,CAAA,EAAI/H,CAAAA,GAAAA,8CAAAA,EAAuB+E,OAAO;4CAChDE,MAAM;4CACNG,SAAS;wCACX,CAAA;gCAEJ;6BAAA;wBAEJ;oBACF;oBAGF,IAAI+C,UAAUtH,KAAKsH;oBACnB,IAAI/C,UAAUjF,CAAAA,GAAAA,0BAAAA,CAAE,CAAA,2BAAA,EAA8B4E,KAAI,iBAAA,EAAoB6C,MAAK,CAAA,CAAG;oBAC9E,IAAIO,WAAW,MACb/C,WAAWjF,CAAAA,GAAAA,0BAAAA,CAAE,CAAA,OAAA,EAAUgI,QAAO,CAAA,CAAG;oBAGnC,MAAM,IAAIpI,CAAAA,GAAAA,iDAAAA,EAAoB;wBAC5BwH,YAAY;qCACVnC;4BACAoC,OAAO;gCACL;6BADFA;wBAGF;oBACF;gBACF;YACF;YAEA5F,MAAMwG,GAAG,CAAClB,KAAKlC;YACfjD,mBAAmBsG,KAAK;YAExB,qDAAA;YACA,sGAAA;YACA,2HAAA;YACA,IAAI,CAAC9H,CAAAA,GAAAA,qCAAAA,EAAKmF,UAAU,CAACX,OAAO;gBAC1B,IAAIuD,iBAAiBxG,SAASqF,GAAG,CAAC/C;gBAClC,IAAI,CAACkE,gBAAgB;oBACnBA,iBAAiB,IAAIC;oBACrBzG,SAASsG,GAAG,CAAChE,MAAMkE;gBACrB;gBAEAA,eAAeE,GAAG,CAACzD;YACrB;QACF;QAEA,OAAOC;IACT;IAEAa,YACEd,IAAyB,EACzBX,IAAc,EACe;QAC7B,IAAI6C,UAAU1G,CAAAA,GAAAA,qCAAAA,EAAK+D,OAAO,CAACF;QAC3B,IAAI8C,MAAMD,UAAU,MAAMlC;QAC1B,IAAIC,WAAWpD,MAAMuF,GAAG,CAACD;QACzB,IAAI,CAAClC,UAAU;YACbA,WAAW,IAAI,CAACoC,eAAe,CAACrC,MAAMX;YACtCxC,MAAMwG,GAAG,CAAClB,KAAKlC;YACfjD,mBAAmBsG,KAAK;YAExB,IAAI,CAAC9H,CAAAA,GAAAA,qCAAAA,EAAKmF,UAAU,CAACX,OAAO;gBAC1B,IAAIuD,iBAAiBxG,SAASqF,GAAG,CAAC/C;gBAClC,IAAI,CAACkE,gBAAgB;oBACnBA,iBAAiB,IAAIC;oBACrBzG,SAASsG,GAAG,CAAChE,MAAMkE;gBACrB;gBAEAA,eAAeE,GAAG,CAACzD;YACrB;QACF;QAEA,OAAOC;IACT;IAEA,MAAM2C,QACJc,OAA6B,EAC7BrE,IAAc,EACdO,IAAqB,EACrB;QACA,MAAM/D,CAAAA,GAAAA,qBAAAA,EAAe,IAAI,CAACsB,EAAE,EAAE,IAAI,EAAEuG,SAASrE,MAAM,IAAI,CAACjC,WAAW,EAAE;YACnEuG,kBAAkB,IAAI,CAACtG,SAAS;YAChC,GAAGuC,IAAH;QACF;IACF;IAEAgE,iBAAiB5D,IAAyB,EAAEX,IAAc,EAAiB;QACzE,IAAI6C,UAAU1G,CAAAA,GAAAA,qCAAAA,EAAK+D,OAAO,CAACF;QAE3B,IAAIY,WAAWpD,MAAMuF,GAAG,CADTF,UAAU,MAAMlC;QAG/B,IAAIC,YAAYzE,CAAAA,GAAAA,qCAAAA,EAAKmF,UAAU,CAACV,SAASA,QAAQ,GAAG;YAClD,IAAI4D,SAAS7G,mBAAmBoF,GAAG,CAACnC,SAASA,QAAQ;YACrD,IAAI4D,UAAU,MACZ,OAAOA;YAGT,IAAIC,MAAM;gBACRC,wBAAwB,EAAE;gBAC1BC,wBAAwB,IAAIR;gBAC5BS,qBAAqB;YACvB;YAEA,IAAIC,OAAO,IAAIV;YACf,IAAIW,SAASA,CAACnE,MAAMX;gBAClB,IAAI6C,UAAU1G,CAAAA,GAAAA,qCAAAA,EAAK+D,OAAO,CAACF;gBAC3B,IAAI8C,MAAMD,UAAU,MAAMlC;gBAC1B,IAAIkE,KAAKE,GAAG,CAACjC,MACX;gBAGF+B,KAAKT,GAAG,CAACtB;gBACT,IAAIlC,WAAWpD,MAAMuF,GAAG,CAACD;gBACzB,IAAI,CAAClC,YAAY,CAACzE,CAAAA,GAAAA,qCAAAA,EAAKmF,UAAU,CAACV,SAASA,QAAQ,GACjD;gBAGF6D,IAAIC,sBAAsB,CAACM,IAAI,IAAIpE,SAAS8D,sBAAsB;gBAClED,IAAIE,sBAAsB,CAACP,GAAG,CAACxD,SAASA,QAAQ;gBAEhD,KAAK,IAAIqE,QAAQrE,SAAS+D,sBAAsB,CAC9CF,IAAIE,sBAAsB,CAACP,GAAG,CAACa;gBAGjC,IAAIf,iBAAiBxG,SAASqF,GAAG,CAACnC,SAASA,QAAQ;gBACnD,IAAIsD,gBACF,KAAK,IAAIgB,aAAahB,eACpBY,OAAOI,WAAWtE,SAASA,QAAQ;YAGzC;YAEAkE,OAAOnE,MAAMX;YAEb,2FAAA;YACA,uGAAA;YACA,IAAIY,SAASC,IAAI,KAAK,GAAG;gBACvB,IAAIsE,gBAAgB,IAAI,CAACnD,QAAQ,CAACuC,gBAAgB,CAAC3D,SAASA,QAAQ;gBACpEuE,cAAcR,sBAAsB,CAACS,OAAO,CAACC,CAAAA,IAC3CZ,IAAIE,sBAAsB,CAACP,GAAG,CAACiB;gBAEjCF,cAAcT,sBAAsB,CAACU,OAAO,CAACC,CAAAA,IAC3CZ,IAAIC,sBAAsB,CAACM,IAAI,CAACK;gBAElCZ,IAAIG,mBAAmB,KAAKO,cAAcP,mBAAmB;gBAC7D,IAAIH,IAAIG,mBAAmB,EACzBvI,CAAAA,GAAAA,6CAAAA,EAAO0E,IAAI,CAAC;oBACVC,SAASjF,CAAAA,GAAAA,0BAAAA,CAAE,CAAA,EAAGI,CAAAA,GAAAA,qCAAAA,EAAKmJ,QAAQ,CACzB,IAAI,CAACvH,WAAW,EAChB6C,SAASA,QACX,EAAC,4HAAA,CAA8H;oBAC/HK,QAAQ;gBACV;YAEJ;YAEAtD,mBAAmBqG,GAAG,CAACpD,SAASA,QAAQ,EAAE6D;YAC1C,OAAOA;QACT;QAEA,OAAO;YACLC,wBAAwB,EAAE;YAC1BC,wBAAwB,IAAIR;YAC5BS,qBAAqB;QACvB;IACF;IAEAtB,WAAW3C,IAAyB,EAAEX,IAAc,EAAE;QACpD,IAAI6E,OAAO,IAAIV;QAEf,IAAIb,aAAaA,CAAC3C,MAAMX;YACtB,IAAI6C,UAAU1G,CAAAA,GAAAA,qCAAAA,EAAK+D,OAAO,CAACF;YAC3B,IAAI8C,MAAMD,UAAU,MAAMlC;YAC1B,IAAIkE,KAAKE,GAAG,CAACjC,MACX;YAGF+B,KAAKT,GAAG,CAACtB;YACT,IAAIlC,WAAWpD,MAAMuF,GAAG,CAACD;YACzB,IAAI,CAAClC,YAAY,CAACzE,CAAAA,GAAAA,qCAAAA,EAAKmF,UAAU,CAACV,SAASA,QAAQ,GACjD;YAGFjD,mBAAmB4H,MAAM,CAAC3E,SAASA,QAAQ;YAE3C,aAAA;YACA,IAAIb,UAAS7D,CAAAA,GAAAA,uCAAAA,EAAOyF,MAAM,CAACf,SAASA,QAAQ,CAAC;YAC7C,IAAIb,SACF,aAAA;YACA,OAAO7D,CAAAA,GAAAA,uCAAAA,EAAOyF,MAAM,CAACf,SAASA,QAAQ,CAAC;YAGzC,IAAIsD,iBAAiBxG,SAASqF,GAAG,CAACnC,SAASA,QAAQ;YACnD,IAAIsD,gBACF,KAAK,IAAIgB,aAAahB,eACpBZ,WAAW4B,WAAWtE,SAASA,QAAQ;YAI3ClD,SAAS6H,MAAM,CAAC3E,SAASA,QAAQ;YACjCpD,MAAM+H,MAAM,CAACzC;QACf;QAEAQ,WAAW3C,MAAMX;QACjB,IAAI,CAACgC,QAAQ,GAAG,IAAI,CAACxD,eAAe;IACtC;IAEAwE,gBAAgBrC,IAAY,EAAEX,IAAY,EAA+B;QACvE,IAAI,IAAI,CAACgC,QAAQ,IAAI,MACnB,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACxD,eAAe;QAGtC,IAAIiG,MAAM,IAAI,CAACzC,QAAQ,CAAClB,OAAO,CAAC;YAC9BoB,UAAUvB;YACV6E,eAAe;YACf1D,QAAQ9B;QACV;QAEA,gDAAA;QACA,8DAAA;QACA,IAAIvB,QAAQC,QAAQ,CAACC,GAAG,IAAI,QAAQ8F,IAAIE,sBAAsB,EAAE;YAC9D,2BAAA;YACA,IAAIhG,MAAMzC,CAAAA,GAAAA,uCAAAA,EAAO+D,UAAU,CAAC9D,CAAAA,GAAAA,qCAAAA,EAAK+D,OAAO,CAACF;YACzCyE,IAAIE,sBAAsB,CAACK,IAAI,CAACrG,IAAIwB,oBAAoB,CAAC,UAAU;QACrE;QAEA,IAAIsE,IAAIgB,KAAK,EAAE;YACb,IAAInH,IAAI,IAAIoH,MAAM,CAAA,0BAAA,EAA6B/E,KAAI,QAAA,EAAWX,KAAI,CAAA,CAAG;YACrE,aAAA;YACA1B,EAAEkE,IAAI,GAAG;YACT,MAAMlE;QACR;QACA,OAAQmG,IAAIkB,UAAU,CAAC9E,IAAI;YACzB,KAAK;gBAAQ;oBACX,IAAI+E,OAAO,IAAI;oBACf,IAAIhF,WAAW6D,IAAIkB,UAAU,CAACE,KAAK;oBACnC,OAAO;kCACLjF;wBACA+D,wBAAwB,IAAIR,IAAIM,IAAIE,sBAAsB;wBAC1DD,wBAAwBD,IAAIC,sBAAsB;wBAClD7D,MAAM4D,IAAIqB,UAAU;wBACpB,IAAIrJ,OAAM;4BACR,IAAIsJ,UAAUH,KAAK9H,EAAE,CAACkI,gBAAgB,CACpC;gCAAC;6BAAe,EAChBpF,UACAgF,KAAK7H,WACP;4BACA,OAAOgI,UACHE,KAAKC,KAAK,CAACN,KAAK9H,EAAE,CAACgB,YAAY,CAACiH,SAAS,WACzC;wBACN;oBACF;gBACF;YACA,KAAK;gBAAW;oBACd,IAAI,EAACI,MAAM,EAAEpG,QAAAA,OAAAA,EAAO,GAAG0E,IAAIkB,UAAU,CAACE,KAAK;oBAC3C,OAAO;wBACLjF,UAAUuF,SAAS,GAAGA,OAAM,CAAA,EAAIpG,SAAQ,GAAGA;wBAC3C4E,wBAAwB,IAAIR,IAAIM,IAAIE,sBAAsB;wBAC1DD,wBAAwBD,IAAIC,sBAAsB;wBAClD7D,MAAM4D,IAAIqB,UAAVjF;oBACF;gBACF;YACA;gBACE,MAAM,IAAI6E,MAAM;QACpB;IACF;AACF;AAEAhK,CAAAA,GAAAA,2CAAAA,EACE,GAAGe,CAAAA,GAAAA,6CAAAA,EAAIsH,OAAO,CAAA,mBAAA,CAAqB,EACnCnG;;;;;;;;;;;;;;ACzoBK,MAAM0I,4CAGwDC,CAAAA,GAAAA,yBAAAA,IACjEC,CAAAA,GAAAA,qBAAAA,EAAUD,CAAAA,GAAAA,yBAAAA,KACV,uCAAA;AACAA,CAAAA,GAAAA,yBAAAA;AAEG,SAASE,0CACdC,aAA4B;IAE5B,OAAOA,cAAclD,KAAK,IAAI,OAC1B;QAACkD,cAAc/F,IAAI;QAAE+F,cAAclD,KAAK;KAAC,CAACmD,IAAI,CAAC,OAC/CD,cAAc/F,IAAI;AACxB;AAEO,SAASiG,0CAAgCC,aAE9C;IACA,OAAO3I,OAAOyB,OAAO,CAACkH,eAAexI,GAAG,CAAC,CAAC,CAACsC,MAAM6C,MAAM;QACrD4C,CAAAA,GAAAA,uCAAAA,EAAU,OAAO5C,UAAU;QAC3B,OAAO;kBACL7C;mBACA6C;QACF;IACF;AACF;AAEO,eAAejH,0CACpBuB,EAAc,EACd6C,IAAY,EACZmG,KAAe,EACf/I,WAAqB;IAErB,IAAIgI,UAAU,MAAMM,CAAAA,GAAAA,gCAAAA,EAAcvI,IAAIgJ,OAAO;QAAC;KAAe,EAAE/I;IAC/D,IAAIgI,WAAW,MACb;IAGF,IAAIgB,SAAS,MAAMjJ,GAAGkJ,QAAQ,CAACjB,SAAS;IACxC,IAAItJ;IACJ,IAAI;QACFA,MAAMwJ,KAAKC,KAAK,CAACa;IACnB,EAAE,OAAOzI,GAAG;QACV,kBAAA;QACA,MAAM,IAAI3C,CAAAA,GAAAA,iDAAAA,EAAoB;YAC5BwH,YAAY;gBACVnC,SAAS;gBACTC,QAAQ;YACV;QACF;IACF;IAEA,IAAI,OAAOxE,QAAQ,YAAYA,OAAO,MACpC,kBAAA;IACA,MAAM,IAAId,CAAAA,GAAAA,iDAAAA,EAAoB;QAC5BwH,YAAY;YACVnC,SAAS;YACTC,QAAQ;QACV;IACF;IAGF,IAAIyC,SAAS,EAAE;IACf,KAAK,IAAIC,SAAS;QAAC;QAAgB;QAAmB;KAAmB,CACvE,IACE,OAAOlH,GAAG,CAACkH,MAAM,KAAK,YACtBlH,GAAG,CAACkH,MAAM,IAAI,QACdlH,GAAG,CAACkH,MAAM,CAAChD,KAAK,IAAI,MAEpB+C,OAAOsB,IAAI,CAACrB;IAIhB,IAAID,OAAO3B,MAAM,GAAG,GAClB,OAAO;QACLZ,UAAU4E;QACVlC,MAAMkD;gBACNrD;IACF;AAEJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC7DA,eAAeH,8BACbzF,EAAc,EACd4J,cAA8B,EAC9BrD,OAA6B,EAC7BrE,IAAc,EACdjC,WAAqB,EACrB6E,UAA0B,CAAC,CAAC;IAE5B,IAAI,gBAAC+E,eAAe,eAAMlE,UAAU,wBAAMa,gBAAAA,EAAiB,GAAG1B;IAC9D,IAAIgF,cAAcvD,QAAQhG,GAAG,CAACwD,CAAAA,IAAKA,EAAElB,IAAI,EAAEgG,IAAI,CAAC;IAEhDtK,CAAAA,GAAAA,6CAAAA,EAAOwL,QAAQ,CAAC,CAAA,WAAA,EAAcD,YAAW,GAAA,CAAK;IAE9C,IAAIE,cAAc,MAAMzB,CAAAA,GAAAA,gCAAAA,EACtBvI,IACAkC,MACA;QAAC;KAAe,EAChBjC;IAEF,IAAIgK,MAAMD,cAAc3L,CAAAA,GAAAA,qCAAAA,EAAK+D,OAAO,CAAC4H,eAAehK,GAAGiK,GAAG;IAE1D,IAAI,CAACzD,kBACHA,mBAAmB,MAAM0D,gDAA0BlK,IAAIkC,MAAMjC;IAG/D,IAAI;QACF,MAAMuG,iBAAiBf,OAAO,CAAC;qBAC7Bc;qBACAZ;iBACAsE;YACAE,aAAaH;gBACbhK;QACF;IACF,EAAE,OAAOyB,KAAK;QACZ,MAAM,IAAImG,MAAM,CAAA,kBAAA,EAAqBkC,YAAW,EAAA,EAAKrI,IAAIyB,OAAO,EAAE;IACpE;IAEA,IAAI2G,cACF,MAAMO,QAAQC,GAAG,CACf9D,QAAQhG,GAAG,CAACwD,CAAAA,IACVuG,8CACEtK,IACA4J,gBACA7F,GACA7B,MACAjC,aACA6E;AAKV;AAEA,eAAewF,8CACbtK,EAAc,EACd4J,cAA8B,EAC9B3H,MAAqB,EACrBC,IAAc,EACdjC,WAAqB,EACrB6E,OAAO;IAEP,MAAM,YAAChC,QAAAA,EAAS,GAAG,MAAM8G,eAAe5G,OAAO,CAACf,OAAOY,IAAI,EAAEX;IAC7D,MAAMqI,YAAyBpB,CAAAA,GAAAA,6CAAAA,EAC7B,MAAMC,CAAAA,GAAAA,6BAAAA,EAAWpJ,IAAI8C,UAAU;QAAC;KAAe,EAAE7C,cACjDuK,MAAM;IACR,MAAMC,QAAQF,UAAUG,gBAAgB,IAAI,CAAC;IAE7C,IAAInE,UAAgC,EAAE;IACtC,KAAK,IAAI,CAAC1D,MAAM6C,MAAM,IAAItF,OAAOyB,OAAO,CAAC4I,OAAQ;QAC/CnC,CAAAA,GAAAA,uCAAAA,EAAU,OAAO5C,UAAU;QAE3B,IAAIH,YAAY,MAAM9G,CAAAA,GAAAA,sCAAAA,EACpBuB,IACA6C,MACAX,MACAjC;QAEF,IAAIsF,WAAW;YACb,IAAI,OAAC5G,GAAAA,EAAI,GAAG,MAAMiL,eAAe5G,OAAO,CAACH,MAAMX;YAC/CoG,CAAAA,GAAAA,uCAAAA,EAAU3J;YACV,IAAI,CAACL,CAAAA,GAAAA,uCAAAA,EAAO0H,SAAS,CAACrH,IAAIsH,OAAO,EAAEP,QACjC,MAAM,IAAI7H,CAAAA,GAAAA,iDAAAA,EAAoB;gBAC5BwH,YAAY;oBACVnC,SAASjF,CAAAA,GAAAA,0BAAAA,CAAE,CAAA,uCAAA,EAA0C4E,KAAI,OAAA,EAAUZ,OAAOY,IAAI,CAAA,qBAAA,EAAwBlE,IAAIsH,OAAO,CAAA,sBAAA,EAAyBP,MAAK,CAAE;oBACjJvC,QAAQ;oBACRC,YAAY;wBACV;4BACEC,UAAUkC,UAAUlC,QAAQ;4BAC5ByC,UAAU;4BACVpB,MAAMa,UAAUQ,IAAI;4BACpBzC,gBAAgBtF,CAAAA,GAAAA,kDAAAA,EACduH,UAAUQ,IAAI,EACdR,UAAUK,MAAM,CAACrF,GAAG,CAACsF,CAAAA,QAAU,CAAA;oCAC7Bb,KAAK,CAAA,CAAA,EAAIa,MAAK,CAAA,EAAI/H,CAAAA,GAAAA,8CAAAA,EAAuB+E,OAAO;oCAChDE,MAAM;oCACNG,SAAS;gCACX,CAAA;wBAEJ;qBAAA;gBAEJ;YACF;YAGF;QACF;QACAqD,QAAQW,IAAI,CAAC;kBAACrE;mBAAM6C;QAAK;IAC3B;IAEA,IAAIa,QAAQtC,MAAM,EAChB,MAAMwB,8BACJzF,IACA4J,gBACArD,SACArE,MACAjC,aACAG,OAAOuK,MAAM,CAAC,CAAC,GAAG7F,SAAS;QAAC+E,cAAc;IAAK;AAGrD;AAEA,eAAeK,gDACblK,EAAc,EACd4K,QAAkB,EAClB3K,WAAqB;IAErB,IAAI4K,aAAa,MAAMtC,CAAAA,GAAAA,gCAAAA,EACrBvI,IACA4K,UACA;QAAC;QAAqB;QAAkB;KAAY,EACpD3K;IAGF,IAAI6K,aAAaD,cAAcxM,CAAAA,GAAAA,qCAAAA,EAAK0M,QAAQ,CAACF;IAE7C,uEAAA;IACA,yEAAA;IACA,IAAIC,eAAe,qBACjB,OAAO,IAAIvB,CAAAA,GAAAA,UAAAA;SACN,IAAIuB,eAAe,kBACxB,OAAO,IAAIrB,CAAAA,GAAAA,WAAAA;SACN,IAAIqB,eAAe,aACxB,OAAO,IAAItB,CAAAA,GAAAA,WAAAA;IAGb,IAAIwB,wBAAwBtB,CAAAA,GAAAA,cAAAA,KAA4B7G;IACxD,IAAImI,0BAA0B,OAC5B,OAAO,IAAIzB,CAAAA,GAAAA,UAAAA;SACN,IAAIyB,0BAA0B,QACnC,OAAO,IAAIxB,CAAAA,GAAAA,WAAAA;SACN,IAAIwB,0BAA0B,QACnC,OAAO,IAAIvB,CAAAA,GAAAA,WAAAA;IAGb,IAAI,MAAMD,CAAAA,GAAAA,WAAAA,EAAKyB,MAAM,IACnB,OAAO,IAAIzB,CAAAA,GAAAA,WAAAA;SACN,IAAI,MAAMC,CAAAA,GAAAA,WAAAA,EAAKwB,MAAM,IAC1B,OAAO,IAAIxB,CAAAA,GAAAA,WAAAA;SAEX,OAAO,IAAIF,CAAAA,GAAAA,UAAAA;AAEf;AAEA,IAAI2B,8BAAQ,IAAI7B,CAAAA,GAAAA,+BAAAA,EAAa;IAAC8B,eAAe;AAAC;AAC9C,IAAIC,0CAAiC,IAAI/E;AAKlC,SAASgF,0CACdrL,EAAc,EACd4J,cAA8B,EAC9BrD,OAA6B,EAC7BlD,QAAkB,EAClBpD,WAAqB,EACrB6E,OAAwB;IAExByB,UAAUA,QAAQhG,GAAG,CAAC+K,CAAAA,UAAY,CAAA;YAChCzI,MAAM8G,CAAAA,GAAAA,cAAAA,EAAwB2B,QAAQzI,IAAI;YAC1C6C,OAAO4F,QAAQ5F,KAAfA;QACF,CAAA;IAEA,qEAAA;IACA,gFAAA;IACA,gBAAA;IACA,IAAI6F,mBAAmBhF,QAAQiF,MAAM,CACnCzH,CAAAA,IAAK,CAACqH,wCAAkBnE,GAAG,CAACwE,0CAAoB1H;IAElD,IAAIwH,iBAAiBtH,MAAM,EAAE;QAC3B,KAAK,IAAIF,KAAKwH,iBACZH,wCAAkB9E,GAAG,CAACmF,0CAAoB1H;QAG5CmH,4BACG5E,GAAG,CAAC,IACHb,8BACEzF,IACA4J,gBACA2B,kBACAlI,UACApD,aACA6E,SACA4G,IAAI,CAAC;gBACL,KAAK,IAAI3H,KAAKwH,iBACZH,wCAAkB3D,MAAM,CAACgE,0CAAoB1H;YAEjD,IAED2H,IAAI,CACH,KAAO,GACP,KAAO;IAEb;IAEA,OAAOR,4BAAMS,GAAG;AAClB;AAEO,SAASjN,0CACdsB,EAAc,EACd4J,cAA8B,EAC9BrD,OAA6B,EAC7BlD,QAAkB,EAClBpD,WAAqB,EACrB6E,OAAwB;IAExB,IAAIwE,CAAAA,GAAAA,8CAAAA,EAAWsC,QAAQ,IAAI;QACzB,IAAIC,YAAYvC,CAAAA,GAAAA,8CAAAA,EAAWwC,YAAY;QACvC,qEAAA;QACA,IAAIC,aACF,AACA,CAACpL,QAAQqL,GAAG,CAACC,iBAAiB,GAC1B5N,CAAAA,GAAAA,qCAAAA,EAAKwK,IAAI,CAACqD,yCAAW,MAAM,kBAC3BC;QACN,OAAON,UAAUO,UAAU,CAAC;YAC1BC,UAAUN;YACVO,MAAM;gBAACtM;gBAAI4J;gBAAgBrD;gBAASlD;gBAAUpD;gBAAa6E;aAAQ;YACnEyH,QAAQ;QACV;IACF;IAEA,OAAOlB,0CACLrL,IACA4J,gBACArD,SACAlD,UACApD,aACA6E;AAEJ;AAEA,SAAS2G,0CAAoB7C,aAA4B;IACvD,OAAO;QAACA,cAAc/F,IAAI;QAAE+F,cAAclD,KAAK;KAAC,CAACmD,IAAI,CAAC;AACxD;;;;AC3RA;AAEA,SAAS,iCAAW,CAAC,EAAE,OAAO;IAC5B,IAAI,KAAK,MACP,OAAO;IAET,IAAI,QAAQ,IAAI,MAAM,YAAY,YAAY,UAAU,oBAAoB;IAC5E,MAAM,WAAW,GAAG,GAAG,qCAAqC;IAC5D,MAAM;AACR;AAEA,iBAAiB;AACjB,eAAe,OAAO,GAAG;AAEzB,OAAO,cAAc,CAAC,gBAAgB,cAAc;IAAC,OAAO;AAAI;;;;;;;;;;;;;;;;;;ACAhE,MAAM6D,gCAAU;AAET,MAAMnD;IACX,MAAM9D,QAAQ,WACZc,OAAO,OACP0D,GAAG,MACHjK,EAAE,eACFmK,WAAW,WACXxE,UAAU,MACO,EAAiB;QAClC,0DAAA;QACA,oCAAA;QACA,IAAIwE,eAAe,MACjB,MAAMnK,GAAG2M,SAAS,CAACtO,CAAAA,GAAAA,qCAAAA,EAAKwK,IAAI,CAACoB,KAAK,iBAAiB;QAGrD,IAAIqC,OAAO;YAAC;YAAW;YAAU3G,UAAU,eAAe;SAAS,CAACiH,MAAM,CACxErG,QAAQhG,GAAG,CAACoI,CAAAA,GAAAA,oCAAAA;QAGd,+FAAA;QACA,4FAAA;QACA,4CAAA;QACA,IAAIqD,MAAM,CAAC;QACX,IAAK,IAAIhH,OAAOrE,QAAQqL,GAAG,CACzB,IAAI,CAAChH,IAAII,UAAU,CAAC,WAAWJ,QAAQ,cAAcA,QAAQ,YAC3DgH,GAAG,CAAChH,IAAI,GAAGrE,QAAQqL,GAAG,CAAChH,IAAI;QAI/B,IAAI6H,iBAAiBL,CAAAA,GAAAA,6CAAAA,EAAME,+BAASJ,MAAM;iBAACrC;iBAAK+B;QAAG;QACnD,IAAIc,SAAS;QACbD,eAAeC,MAAM,CAACC,EAAE,CAAC,QAASC,CAAAA;YAChCF,UAAUE,IAAIC,QAAQ;QACxB;QAEA,IAAIC,SAAS,EAAE;QACfL,eAAeK,MAAM,CAACH,EAAE,CAAC,QAASC,CAAAA;YAChCE,OAAOhG,IAAI,CAAC8F,IAAIC,QAAQ,GAAGE,IAAI;QACjC;QAEA,IAAI;YACF,MAAMV,CAAAA,GAAAA,cAAAA,EAAmBI;YAEzB,IAAIO,UAAsBjF,KAAKC,KAAK,CAAC0E;YACrC,IAAIO,aAAaD,QAAQE,KAAK,CAACrJ,MAAM;YACrC,IAAIoJ,aAAa,GACf9O,CAAAA,GAAAA,6CAAAA,EAAOgP,GAAG,CAAC;gBACTpK,QAAQ;gBACRD,SAAS,CAAA,MAAA,EAASmK,WAAlBnK,iBAAAA,CAAAA;YACF;YAGF,wEAAA;YACA,yEAAA;YACA,+BAAA;YACA,KAAK,IAAIA,WAAWgK,OAClB,IAAIhK,QAAQe,MAAM,GAAG,GACnB1F,CAAAA,GAAAA,6CAAAA,EAAOgP,GAAG,CAAC;gBACTpK,QAAQ;yBACRD;YACF;QAGN,EAAE,OAAO1C,GAAG;YACV,MAAM,IAAIoH,MACR,oCACEpH,EAAE0C,OAAO,GACT,QACAgK,OAAOrE,IAAI,CAAC;QAElB;IACF;AACF;AAMAjL,CAAAA,GAAAA,2CAAAA,EAA0B,GAAGe,CAAAA,GAAAA,6CAAAA,EAAIsH,OAAO,CAAA,IAAA,CAAM,EAAEsD;;;;AC7FhD;;;;;;AAMA,SAAS,4BAAM,OAAO,EAAE,IAAI,EAAE,OAAO;IACjC,sBAAsB;IACtB,MAAM,SAAS,OAAM,SAAS,MAAM;IAEpC,0BAA0B;IAC1B,MAAM,UAAU,2BAAS,OAAO,OAAO,EAAE,OAAO,IAAI,EAAE,OAAO,OAAO;IAEpE,uEAAuE;IACvE,mFAAmF;IACnF,OAAO,gBAAgB,CAAC,SAAS;IAEjC,OAAO;AACX;AAEA,SAAS,gCAAU,OAAO,EAAE,IAAI,EAAE,OAAO;IACrC,sBAAsB;IACtB,MAAM,SAAS,OAAM,SAAS,MAAM;IAEpC,0BAA0B;IAC1B,MAAM,SAAS,+BAAa,OAAO,OAAO,EAAE,OAAO,IAAI,EAAE,OAAO,OAAO;IAEvE,yGAAyG;IACzG,OAAO,KAAK,GAAG,OAAO,KAAK,IAAI,OAAO,gBAAgB,CAAC,OAAO,MAAM,EAAE;IAEtE,OAAO;AACX;AAEA,iBAAiB;AACjB,eAAe,KAAK,GAAG;AACvB,eAAe,IAAI,GAAG;AAEtB,eAAe,MAAM,GAAG;AACxB,eAAe,OAAO,GAAG;;;;ACtCzB;;;;;;;;AAOA,MAAM,8BAAQ,QAAQ,QAAQ,KAAK;AACnC,MAAM,2CAAqB;AAC3B,MAAM,wCAAkB;AAExB,SAAS,oCAAc,MAAM;IACzB,OAAO,IAAI,GAAG,OAAe;IAE7B,MAAM,UAAU,OAAO,IAAI,IAAI,OAAY,OAAO,IAAI;IAEtD,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI;QAC/B,OAAO,OAAO,GAAG;QAEjB,OAAO,OAAe;IAC1B;IAEA,OAAO,OAAO,IAAI;AACtB;AAEA,SAAS,oCAAc,MAAM;IACzB,IAAI,CAAC,6BACD,OAAO;IAGX,oCAAoC;IACpC,MAAM,cAAc,oCAAc;IAElC,iEAAiE;IACjE,MAAM,aAAa,CAAC,yCAAmB,IAAI,CAAC;IAE5C,qFAAqF;IACrF,gEAAgE;IAChE,IAAI,OAAO,OAAO,CAAC,UAAU,IAAI,YAAY;QACzC,gGAAgG;QAChG,4FAA4F;QAC5F,4FAA4F;QAC5F,gCAAgC;QAChC,MAAM,6BAA6B,sCAAgB,IAAI,CAAC;QAExD,4EAA4E;QAC5E,6EAA6E;QAC7E,OAAO,OAAO,GAAG,sBAAe,OAAO,OAAO;QAE9C,6BAA6B;QAC7B,OAAO,OAAO,GAAG,eAAe,OAAO,OAAO;QAC9C,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,MAAQ,gBAAgB,KAAK;QAE5D,MAAM,eAAe;YAAC,OAAO,OAAO;SAAC,CAAC,MAAM,CAAC,OAAO,IAAI,EAAE,IAAI,CAAC;QAE/D,OAAO,IAAI,GAAG;YAAC;YAAM;YAAM;YAAM,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC;SAAC;QACrD,OAAO,OAAO,GAAG,QAAQ,GAAG,CAAC,OAAO,IAAI;QACxC,OAAO,OAAO,CAAC,wBAAwB,GAAG,MAAM,2DAA2D;IAC/G;IAEA,OAAO;AACX;AAEA,SAAS,4BAAM,OAAO,EAAE,IAAI,EAAE,OAAO;IACjC,yCAAyC;IACzC,IAAI,QAAQ,CAAC,MAAM,OAAO,CAAC,OAAO;QAC9B,UAAU;QACV,OAAO;IACX;IAEA,OAAO,OAAO,KAAK,KAAK,CAAC,KAAK,EAAE,EAAE,6CAA6C;IAC/E,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG,UAAU,8CAA8C;IAEpF,0BAA0B;IAC1B,MAAM,SAAS;iBACX;cACA;iBACA;QACA,MAAM;QACN,UAAU;qBACN;kBACA;QACJ;IACJ;IAEA,iDAAiD;IACjD,OAAO,QAAQ,KAAK,GAAG,SAAS,oCAAc;AAClD;AAEA,iBAAiB;;;;AC1FjB;;;;;;AAMA,SAAS,4CAAsB,MAAM,EAAE,cAAc;IACjD,MAAM,MAAM,OAAO,OAAO,CAAC,GAAG,IAAI,QAAQ,GAAG;IAC7C,MAAM,MAAM,QAAQ,GAAG;IACvB,MAAM,eAAe,OAAO,OAAO,CAAC,GAAG,IAAI;IAC3C,6CAA6C;IAC7C,MAAM,kBAAkB,gBAAgB,QAAQ,KAAK,KAAK,aAAa,CAAC,QAAQ,KAAK,CAAC,QAAQ;IAE9F,qEAAqE;IACrE,uEAAuE;IACvE,IAAI,iBACA,IAAI;QACA,QAAQ,KAAK,CAAC,OAAO,OAAO,CAAC,GAAG;IACpC,EAAE,OAAO,KAAK;IACV,SAAS,GACb;IAGJ,IAAI;IAEJ,IAAI;QACA,WAAW,YAAW,OAAO,OAAO,EAAE;YAClC,MAAM,GAAG,CAAC,OAAW;qBAAE;YAAI,GAAG;YAC9B,SAAS,iBAAiB,wBAAiB;QAC/C;IACJ,EAAE,OAAO,GAAG;IACR,SAAS,GACb,SAAU;QACN,IAAI,iBACA,QAAQ,KAAK,CAAC;IAEtB;IAEA,wEAAwE;IACxE,6FAA6F;IAC7F,IAAI,UACA,WAAW,oBAAa,eAAe,OAAO,OAAO,CAAC,GAAG,GAAG,IAAI;IAGpE,OAAO;AACX;AAEA,SAAS,qCAAe,MAAM;IAC1B,OAAO,4CAAsB,WAAW,4CAAsB,QAAQ;AAC1E;AAEA,iBAAiB;;;;ACnDjB,MAAM,kCAAY,QAAQ,QAAQ,KAAK,WACnC,QAAQ,GAAG,CAAC,MAAM,KAAK,YACvB,QAAQ,GAAG,CAAC,MAAM,KAAK;;AAG3B,MAAM,8BAAQ,kCAAY,MAAM;;;AAGhC,MAAM,yCAAmB,CAAC,MACxB,OAAO,MAAM,CAAC,IAAI,MAAM,CAAC,WAAW,EAAE,KAAK,GAAG;QAAE,MAAM;IAAS;AAEjE,MAAM,oCAAc,CAAC,KAAK;IACxB,MAAM,QAAQ,IAAI,KAAK,IAAI;IAE3B,iEAAiE;IACjE,6CAA6C;IAC7C,MAAM,UAAU,IAAI,KAAK,CAAC,SAAS,mCAAa,IAAI,KAAK,CAAC,QAAQ;QAAC;KAAG,GAElE;QACE,sCAAsC;WAClC,kCAAY;YAAC,QAAQ,GAAG;SAAG,GAAG,EAAE;WACjC,AAAC,CAAA,IAAI,IAAI,IAAI,QAAQ,GAAG,CAAC,IAAI,IAC9B,sCAAsC,GAAG,EAAC,EAAG,KAAK,CAAC;KACtD;IAEL,MAAM,aAAa,kCACf,IAAI,OAAO,IAAI,QAAQ,GAAG,CAAC,OAAO,IAAI,wBACtC;IACJ,MAAM,UAAU,kCAAY,WAAW,KAAK,CAAC,SAAS;QAAC;KAAG;IAE1D,IAAI,iCACF;QAAA,IAAI,IAAI,OAAO,CAAC,SAAS,MAAM,OAAO,CAAC,EAAE,KAAK,IAC5C,QAAQ,OAAO,CAAC;IAAE;IAGtB,OAAO;iBACL;iBACA;oBACA;IACF;AACF;AAEA,MAAM,8BAAQ,CAAC,KAAK,KAAK;IACvB,IAAI,OAAO,QAAQ,YAAY;QAC7B,KAAK;QACL,MAAM,CAAC;IACT;IACA,IAAI,CAAC,KACH,MAAM,CAAC;IAET,MAAM,WAAE,OAAO,WAAE,OAAO,cAAE,UAAU,EAAE,GAAG,kCAAY,KAAK;IAC1D,MAAM,QAAQ,EAAE;IAEhB,MAAM,OAAO,CAAA,IAAK,IAAI,QAAQ,CAAC,SAAS;YACtC,IAAI,MAAM,QAAQ,MAAM,EACtB,OAAO,IAAI,GAAG,IAAI,MAAM,MAAM,GAAG,QAAQ,SACrC,OAAO,uCAAiB;YAE9B,MAAM,QAAQ,OAAO,CAAC,EAAE;YACxB,MAAM,WAAW,SAAS,IAAI,CAAC,SAAS,MAAM,KAAK,CAAC,GAAG,MAAM;YAE7D,MAAM,OAAO,iBAAU,UAAU;YACjC,MAAM,IAAI,CAAC,YAAY,YAAY,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,OAC7D;YAEJ,QAAQ,QAAQ,GAAG,GAAG;QACxB;IAEA,MAAM,UAAU,CAAC,GAAG,GAAG,KAAO,IAAI,QAAQ,CAAC,SAAS;YAClD,IAAI,OAAO,QAAQ,MAAM,EACvB,OAAO,QAAQ,KAAK,IAAI;YAC1B,MAAM,MAAM,OAAO,CAAC,GAAG;YACvB,OAAM,IAAI,KAAK;gBAAE,SAAS;YAAW,GAAG,CAAC,IAAI;gBAC3C,IAAI,CAAC,MAAM,IAAI;oBACb,IAAI,IAAI,GAAG,EACT,MAAM,IAAI,CAAC,IAAI;yBAEf,OAAO,QAAQ,IAAI;gBACvB;gBACA,OAAO,QAAQ,QAAQ,GAAG,GAAG,KAAK;YACpC;QACF;IAEA,OAAO,KAAK,KAAK,GAAG,IAAI,CAAC,CAAA,MAAO,GAAG,MAAM,MAAM,MAAM,KAAK;AAC5D;AAEA,MAAM,kCAAY,CAAC,KAAK;IACtB,MAAM,OAAO,CAAC;IAEd,MAAM,WAAE,OAAO,WAAE,OAAO,cAAE,UAAU,EAAE,GAAG,kCAAY,KAAK;IAC1D,MAAM,QAAQ,EAAE;IAEhB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAM;QACxC,MAAM,QAAQ,OAAO,CAAC,EAAE;QACxB,MAAM,WAAW,SAAS,IAAI,CAAC,SAAS,MAAM,KAAK,CAAC,GAAG,MAAM;QAE7D,MAAM,OAAO,iBAAU,UAAU;QACjC,MAAM,IAAI,CAAC,YAAY,YAAY,IAAI,CAAC,OAAO,IAAI,KAAK,CAAC,GAAG,KAAK,OAC7D;QAEJ,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAM;YACxC,MAAM,MAAM,IAAI,OAAO,CAAC,EAAE;YAC1B,IAAI;gBACF,MAAM,KAAK,OAAM,IAAI,CAAC,KAAK;oBAAE,SAAS;gBAAW;gBACjD,IAAI,IAAI;oBACN,IAAI,IAAI,GAAG,EACT,MAAM,IAAI,CAAC;yBAEX,OAAO;gBACX;YACF,EAAE,OAAO,IAAI,CAAC;QAChB;IACF;IAEA,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EACzB,OAAO;IAET,IAAI,IAAI,OAAO,EACb,OAAO;IAET,MAAM,uCAAiB;AACzB;AAEA,iBAAiB;AACjB,4BAAM,IAAI,GAAG;;;;;AC3Hb,IAAI;;;AACJ,IAAI,QAAQ,QAAQ,KAAK,WAAW,eAAO,eAAe,EACxD,6BAAO;KAEP,6BAAO;AAGT,iBAAiB;AACjB,4BAAM,IAAI,GAAG;AAEb,SAAS,4BAAO,IAAI,EAAE,OAAO,EAAE,EAAE;IAC/B,IAAI,OAAO,YAAY,YAAY;QACjC,KAAK;QACL,UAAU,CAAC;IACb;IAEA,IAAI,CAAC,IAAI;QACP,IAAI,OAAO,YAAY,YACrB,MAAM,IAAI,UAAU;QAGtB,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;YAC1C,4BAAM,MAAM,WAAW,CAAC,GAAG,SAAU,EAAE,EAAE,EAAE;gBACzC,IAAI,IACF,OAAO;qBAEP,QAAQ;YAEZ;QACF;IACF;IAEA,2BAAK,MAAM,WAAW,CAAC,GAAG,SAAU,EAAE,EAAE,EAAE;QACxC,oEAAoE;QACpE,IAAI,IACF;YAAA,IAAI,GAAG,IAAI,KAAK,YAAY,WAAW,QAAQ,YAAY,EAAE;gBAC3D,KAAK;gBACL,KAAK;YACP;QAAA;QAEF,GAAG,IAAI;IACT;AACF;AAEA,SAAS,2BAAM,IAAI,EAAE,OAAO;IAC1B,kCAAkC;IAClC,IAAI;QACF,OAAO,2BAAK,IAAI,CAAC,MAAM,WAAW,CAAC;IACrC,EAAE,OAAO,IAAI;QACX,IAAI,WAAW,QAAQ,YAAY,IAAI,GAAG,IAAI,KAAK,UACjD,OAAO;aAEP,MAAM;IAEV;AACF;;;;;;ACxDA;AAEA,MAAM,gCAAU,CAAC,UAAU,CAAC,CAAC;IAC5B,MAAM,cAAc,QAAQ,GAAG,IAAI,QAAQ,GAAG;IAC9C,MAAM,WAAW,QAAQ,QAAQ,IAAI,QAAQ,QAAQ;IAErD,IAAI,aAAa,SAChB,OAAO;IAGR,OAAO,OAAO,IAAI,CAAC,aAAa,OAAO,GAAG,IAAI,CAAC,CAAA,MAAO,IAAI,WAAW,OAAO,WAAW;AACxF;AAEA,iBAAiB;AACjB,+CAA+C;AAC/C,eAAe,OAAO,GAAG;;;;;;;;;AC8BzB,IAAA;AACA,IAAA;AA9CA;AAEA,oDAAoD;AACpD,MAAM,wCAAkB;AAExB,SAAS,oCAAc,GAAG;IACtB,oBAAoB;IACpB,MAAM,IAAI,OAAO,CAAC,uCAAiB;IAEnC,OAAO;AACX;AAEA,SAAS,qCAAe,GAAG,EAAE,qBAAqB;IAC9C,oBAAoB;IACpB,MAAM,GAAG,KAAK;IAEd,mDAAmD;IACnD,+FAA+F;IAC/F,0FAA0F;IAE1F,sDAAsD;IACtD,4DAA4D;IAC5D,MAAM,IAAI,OAAO,CAAC,mBAAmB;IAErC,4DAA4D;IAC5D,4CAA4C;IAC5C,gCAAgC;IAChC,MAAM,IAAI,OAAO,CAAC,kBAAkB;IAEpC,wCAAwC;IAExC,yBAAyB;IACzB,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;IAEhB,oBAAoB;IACpB,MAAM,IAAI,OAAO,CAAC,uCAAiB;IAEnC,wCAAwC;IACxC,IAAI,uBACA,MAAM,IAAI,OAAO,CAAC,uCAAiB;IAGvC,OAAO;AACX;AAEA,4CAAyB;AACzB,4CAA0B;;;;;AC9C1B;;;;AAKA,SAAS,kCAAY,OAAO;IACxB,yCAAyC;IACzC,MAAM,OAAO;IACb,MAAM,SAAS,OAAO,KAAK,CAAC;IAE5B,IAAI;IAEJ,IAAI;QACA,KAAK,mBAAY,SAAS;QAC1B,mBAAY,IAAI,QAAQ,GAAG,MAAM;QACjC,oBAAa;IACjB,EAAE,OAAO,GAAG,CAAc;IAE1B,iEAAiE;IACjE,OAAO,OAAe,OAAO,QAAQ;AACzC;AAEA,iBAAiB;;;;ACtBjB;;;AAGA,iBAAiB,CAAC,SAAS,EAAE;IAC5B,MAAM,QAAQ,OAAO,KAAK,CAAC;IAE3B,IAAI,CAAC,OACJ,OAAO;IAGR,MAAM,CAAC,MAAM,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,IAAI,KAAK,CAAC;IAC5D,MAAM,SAAS,KAAK,KAAK,CAAC,KAAK,GAAG;IAElC,IAAI,WAAW,OACd,OAAO;IAGR,OAAO,WAAW,GAAG,OAAO,CAAC,EAAE,UAAU,GAAG;AAC7C;;;;AClBA;AACA,iBAAiB;;;;;;;;ACDjB;AAEA,MAAM,8BAAQ,QAAQ,QAAQ,KAAK;AAEnC,SAAS,oCAAc,QAAQ,EAAE,OAAO;IACpC,OAAO,OAAO,MAAM,CAAC,IAAI,MAAM,GAAG,QAAQ,CAAC,EAAE,SAAS,OAAO,CAAC,OAAO,CAAC,GAAG;QACrE,MAAM;QACN,OAAO;QACP,SAAS,GAAG,QAAQ,CAAC,EAAE,SAAS,OAAO,EAAE;QACzC,MAAM,SAAS,OAAO;QACtB,WAAW,SAAS,IAAI;IAC5B;AACJ;AAEA,SAAS,uCAAiB,EAAE,EAAE,MAAM;IAChC,IAAI,CAAC,6BACD;IAGJ,MAAM,eAAe,GAAG,IAAI;IAE5B,GAAG,IAAI,GAAG,SAAU,IAAI,EAAE,IAAI;QAC1B,mEAAmE;QACnE,iDAAiD;QACjD,iEAAiE;QACjE,IAAI,SAAS,QAAQ;YACjB,MAAM,MAAM,mCAAa,MAAM;YAE/B,IAAI,KACA,OAAO,aAAa,IAAI,CAAC,IAAI,SAAS;QAE9C;QAEA,OAAO,aAAa,KAAK,CAAC,IAAI,YAAY,yCAAyC;IACvF;AACJ;AAEA,SAAS,mCAAa,MAAM,EAAE,MAAM;IAChC,IAAI,+BAAS,WAAW,KAAK,CAAC,OAAO,IAAI,EACrC,OAAO,oCAAc,OAAO,QAAQ,EAAE;IAG1C,OAAO;AACX;AAEA,SAAS,uCAAiB,MAAM,EAAE,MAAM;IACpC,IAAI,+BAAS,WAAW,KAAK,CAAC,OAAO,IAAI,EACrC,OAAO,oCAAc,OAAO,QAAQ,EAAE;IAG1C,OAAO;AACX;AAEA,iBAAiB;sBACb;kBACA;sBACA;mBACA;AACJ;;;;;;;;ACtDe,kDACbiE,YAA0B;IAE1B,OAAO,IAAIpD,QAAQ,CAACpH,SAASyK;QAC3BD,aAAaT,EAAE,CAAC,SAASU;QACzBD,aAAaT,EAAE,CAAC,SAASrI,CAAAA;YACvB,IAAIA,SAAS,GAAG;gBACd+I,OAAO,IAAI7F,MAAM;gBACjB;YACF;YAEA5E;QACF;IACF;AACF;;;;;AClBAf,iBAAiBkG,KAAKC,KAAK,CAAC;;;;;;;;;;;;;;;;;;;;;;;;ACgB5B,MAAMyF,iCAAW;AAoBjB,IAAIC;AACJ,IAAIC;AAEG,MAAMvE;IACX,aAAayB,SAA2B;QACtC,IAAI6C,iCAAW,MACb,OAAOA;QAGT,IAAI;YACFA,gCAAUE,QAAQ,MAAMN,CAAAA,GAAAA,6CAAAA,EAAc;QACxC,EAAE,OAAOjM,KAAK;YACZqM,gCAAU;QACZ;QAEA,OAAOA;IACT;IAEA,MAAMrI,QAAQ,WACZc,OAAO,OACP0D,GAAG,WACHtE,UAAU,MACO,EAAiB;QAClC,IAAIoI,qCAAe,MAAM;YACvB,IAAI9H,UAAU,MAAMuC,CAAAA,GAAAA,WAAAA,EAAK;YACzBuF,oCAAcE,SAAShI,QAAQ6G,MAAM,EAAE;QACzC;QAEA,IAAIR,OAAO;YAAC;YAAO;SAAS,CAACM,MAAM,CACjCrG,QAAQhG,GAAG,CAACoI,CAAAA,GAAAA,oCAAAA;QAGd,IAAIhD,SAAS;YACX2G,KAAKpF,IAAI,CAAC;YACV,IAAI6G,oCAAc,GAChBzB,KAAKpF,IAAI,CAAC;QAEd;QAEA,gGAAA;QACA,8FAAA;QACA,4CAAA;QACA,IAAI8E,MAAM,CAAC;QACX,IAAK,IAAIhH,OAAOrE,QAAQqL,GAAG,CACzB,IACE,CAAChH,IAAII,UAAU,CAAC,WAChBJ,QAAQ,sBACRA,QAAQ,cACRA,QAAQ,YAERgH,GAAG,CAAChH,IAAI,GAAGrE,QAAQqL,GAAG,CAAChH,IAAI;QAI/B,IAAI6H,iBAAiBL,CAAAA,GAAAA,6CAAAA,EAAMqB,gCAAUvB,MAAM;iBAACrC;iBAAK+B;QAAG;QACpDa,eAAeC,MAAfD,AACE,+EAAA;SACCqB,IAAI,CAACP,CAAAA,GAAAA,6CAAAA,KACLO,IAAI,CAAC,IAAIN,CAAAA,GAAAA,cAAAA,KACTb,EAAE,CAAC,SAASvM,CAAAA;YACXjC,CAAAA,GAAAA,6CAAAA,EAAOoJ,KAAK,CAACnH,GAAG;QAClB,GACCuM,EAAE,CAAC,QAAS7J,CAAAA;YACX,OAAQA,QAAQH,IAAI;gBAClB,KAAK;oBACHxE,CAAAA,GAAAA,6CAAAA,EAAOwL,QAAQ,CACboE,6BACE,CAAA,CAAA,EAAIjL,QAAQkL,IAAI,CAACC,OAAO,CAAA,CAAA,EAAInL,QAAQkL,IAAI,CAACE,KAAK,CAAA,EAAA,EAAKpL,QAAQkL,IAAI,CAAClL,OAAO,EACzE;oBAEF;gBACF,KAAK;gBACL,KAAK;oBACH3E,CAAAA,GAAAA,6CAAAA,EAAOgQ,IAAI,CAAC;wBACVpL,QAAQ;wBACRD,SAASiL,6BAAOjL,QAAQkL,IAAI;oBAC9B;oBACA;gBACF;YAEF;QACF;QAEFvB,eAAeK,MAAM,CAClBgB,IAAI,CAACP,CAAAA,GAAAA,6CAAAA,KACLO,IAAI,CAAC,IAAIN,CAAAA,GAAAA,cAAAA,KACTb,EAAE,CAAC,SAASvM,CAAAA;YACXjC,CAAAA,GAAAA,6CAAAA,EAAOoJ,KAAK,CAACnH,GAAG;QAClB,GACCuM,EAAE,CAAC,QAAS7J,CAAAA;YACX,OAAQA,QAAQH,IAAI;gBAClB,KAAK;oBACHxE,CAAAA,GAAAA,6CAAAA,EAAO0E,IAAI,CAAC;wBACVE,QAAQ;wBACRD,SAASiL,6BAAOjL,QAAQkL,IAAI;oBAC9B;oBACA;gBACF,KAAK;oBACH7P,CAAAA,GAAAA,6CAAAA,EAAOoJ,KAAK,CAAC;wBACXxE,QAAQ;wBACRD,SAASiL,6BAAOjL,QAAQkL,IAAI;oBAC9B;oBACA;gBACF;YAEF;QACF;QAEF,IAAI;YACF,OAAO,MAAM3B,CAAAA,GAAAA,cAAAA,EAAmBI;QAClC,EAAE,OAAOrM,GAAG;YACV,MAAM,IAAIoH,MAAM,oCAAoCpH,EAAE0C,OAAO;QAC/D;IACF;AACF;AAEA,SAASiL,6BAAOjL,OAAe;IAC7B,OAAO,WAAWA;AACpB;AAEAtF,CAAAA,GAAAA,2CAAAA,EAA0B,GAAGe,CAAAA,GAAAA,6CAAAA,EAAIsH,OAAO,CAAA,KAAA,CAAO,EAAEuD;;;;;AC5JjD,iBAAiB;;;;;ACAjB;;;;;;;;;;;;;;AAcA,GAEA;;0CAEM;;8CACA;AACN,MAAM,8BAAQ,OAAO;AACrB,MAAM,iCAAW,OAAO;AAExB,SAAS,gCAAW,KAAK,EAAE,GAAG,EAAE,EAAE;IAChC,IAAI;IACJ,IAAI,IAAI,CAAC,QAAQ,EAAE;QACjB,MAAM,MAAM,IAAI,CAAC,+BAAS,CAAC,KAAK,CAAC;QACjC,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,OAAO;QAE7B,IAAI,KAAK,MAAM,KAAK,GAAG,OAAO,KAAK,+CAA+C;;QAElF,0FAA0F;QAC1F,KAAK,KAAK;QACV,IAAI,CAAC,QAAQ,GAAG;IAClB,OAAO;QACL,IAAI,CAAC,4BAAM,IAAI,IAAI,CAAC,+BAAS,CAAC,KAAK,CAAC;QACpC,OAAO,IAAI,CAAC,4BAAM,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO;IACvC;IAEA,IAAI,CAAC,4BAAM,GAAG,KAAK,GAAG;IAEtB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAC/B,IAAI;QACF,2BAAK,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;IAChC,EAAE,OAAO,OAAO;QACd,OAAO,GAAG;IACZ;IAGF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,4BAAM,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS;IACnD,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;QACvC,GAAG,IAAI,MAAM;QACb;IACF;IAEA;AACF;AAEA,SAAS,4BAAO,EAAE;IAChB,sCAAsC;IACtC,IAAI,CAAC,4BAAM,IAAI,IAAI,CAAC,+BAAS,CAAC,GAAG;IAEjC,IAAI,IAAI,CAAC,4BAAM,EACb,IAAI;QACF,2BAAK,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAAM;IACpC,EAAE,OAAO,OAAO;QACd,OAAO,GAAG;IACZ;IAGF;AACF;AAEA,SAAS,2BAAM,IAAI,EAAE,GAAG;IACtB,IAAI,QAAQ,WACV,KAAK,IAAI,CAAC;AAEd;AAEA,SAAS,2BAAM,QAAQ;IACrB,OAAO;AACT;AAEA,SAAS,4BAAO,OAAO,EAAE,MAAM,EAAE,OAAO;IACtC,+CAA+C;IAC/C,UAAU,WAAW;IACrB,SAAS,UAAU;IACnB,UAAU,WAAW,CAAC;IAEtB,6BAA6B;IAC7B,OAAQ,UAAU,MAAM;QACtB,KAAK;YACH,8BAA8B;YAC9B,IAAI,OAAO,YAAY,YAAY;gBACjC,SAAS;gBACT,UAAU;YACZ,+BAA+B;YAC/B,OAAO,IAAI,OAAO,YAAY,YAAY,CAAE,CAAA,mBAAmB,MAAK,KAAM,CAAC,OAAO,CAAC,OAAO,KAAK,CAAC,EAAE;gBAChG,UAAU;gBACV,UAAU;YACZ;YACA;QAEF,KAAK;YACH,uCAAuC;YACvC,IAAI,OAAO,YAAY,YAAY;gBACjC,UAAU;gBACV,SAAS;gBACT,UAAU;YACZ,wCAAwC;YACxC,OAAO,IAAI,OAAO,WAAW,UAAU;gBACrC,UAAU;gBACV,SAAS;YACX;IACJ;IAEA,UAAU,OAAO,MAAM,CAAC,CAAC,GAAG;IAC5B,QAAQ,WAAW,GAAG;IACtB,QAAQ,SAAS,GAAG;IACpB,QAAQ,KAAK,GAAG;IAChB,QAAQ,kBAAkB,GAAG;IAE7B,MAAM,SAAS,wCAAc;IAE7B,MAAM,CAAC,4BAAM,GAAG;IAChB,MAAM,CAAC,+BAAS,GAAG,4CAAkB;IACrC,OAAO,OAAO,GAAG;IACjB,OAAO,MAAM,GAAG;IAChB,OAAO,SAAS,GAAG,QAAQ,SAAS;IACpC,OAAO,YAAY,GAAG,QAAQ,YAAY,IAAI;IAC9C,OAAO,QAAQ,GAAG;IAClB,OAAO,QAAQ,GAAG,SAAU,GAAG,EAAE,EAAE;QACjC,iDAAiD;QACjD,IAAI,CAAC,cAAc,CAAC,YAAY,GAAG;QACnC,GAAG;IACL;IAEA,OAAO;AACT;AAEA,iBAAiB;;;;;;;;;ACnIF,uDAA8BgF,CAAAA,GAAAA,uBAAAA;IAC3CzO,YAAY+E,OAAc,CAAE;QAC1B,KAAK,CAAC;YAAC,GAAGA,OAAO;YAAE2J,YAAY;QAAI;IACrC;IAEA,oEAAA;IACAC,WACEC,KAAsB,EACtBtK,QAAgB,EAChBuK,QAAqD,EACrD;QACA,IAAI;YACF,IAAIC;YACJ,IAAI;gBACFA,SAAS1G,KAAKC,KAAK,CAACuG,MAAM1B,QAAQ;YACpC,EAAE,OAAOzM,GAAG;gBACV,8DAAA;gBACA,mDAAA;gBACAjC,CAAAA,GAAAA,6CAAAA,EAAOuQ,OAAO,CAAC;oBACb5L,SAAS,mCAAmCyL,MAAM1B,QAAQ;oBAC1D9J,QAAQ;gBACV;gBACA;YACF;YACAyL,SAAS,MAAMC;QACjB,EAAE,OAAOpN,KAAK;YACZmN,SAASnN;QACX;IACF;AACF;;;;;;;;;;;;;;;;;;;;;;;;;;ACpBA,MAAMsN,iCAAW;AA+CjB,IAAIC;AACJ,IAAIC;AAEG,MAAMxF;IACX,aAAawB,SAA2B;QACtC,IAAI+D,iCAAW,MACb,OAAOA;QAGT,IAAI;YACFA,gCAAUhB,QAAQ,MAAMN,CAAAA,GAAAA,6CAAAA,EAAc;QACxC,EAAE,OAAOjM,KAAK;YACZuN,gCAAU;QACZ;QAEA,OAAOA;IACT;IAEA,MAAMvJ,QAAQ,WACZc,OAAO,OACP0D,GAAG,WACHtE,UAAU,MACO,EAAiB;QAClC,IAAIsJ,qCAAe,MAAM;YACvB,IAAIhJ,UAAU,MAAMuC,CAAAA,GAAAA,WAAAA,EAAK;YACzByG,oCAAchB,SAAShI,QAAQ6G,MAAM,EAAE;QACzC;QAEA,IAAIR,OAAO;YAAC;YAAO;YAAc;SAAS;QAC1C,IAAI3G,SACF2G,KAAKpF,IAAI,CAAC;QAEZ,IAAI+H,qCAAe,GACjB;YAAA,IAAIjP,CAAAA,GAAAA,mCAAAA,EAAGkP,UAAU,CAAC7Q,CAAAA,GAAAA,qCAAAA,EAAKwK,IAAI,CAACoB,KAAK,yBAC/B,iDAAA;YACAqC,KAAKpF,IAAI,CAAC;QACZ,OAEA,+BAAA;QACAoF,KAAKpF,IAAI,CAAC;QAEZoF,OAAOA,KAAKM,MAAM,CAACrG,QAAQhG,GAAG,CAACoI,CAAAA,GAAAA,oCAAAA;QAE/B,IAAIqD,MAAM,CAAC;QACX,IAAK,IAAIhH,OAAOrE,QAAQqL,GAAG,CACzB,IAAI,CAAChH,IAAII,UAAU,CAAC,WAAWJ,QAAQ,cAAcA,QAAQ,YAC3DgH,GAAG,CAAChH,IAAI,GAAGrE,QAAQqL,GAAG,CAAChH,IAAI;QAI/B,IAAIqI,aAAa,GACf8B,eAAe;QAEjB,IAAItC,iBAAiBL,CAAAA,GAAAA,6CAAAA,EAAMuC,gCAAUzC,MAAM;iBACzCrC;iBACA+B;QACF;QACAa,eAAeC,MAAM,CAClBoB,IAAI,CAACP,CAAAA,GAAAA,6CAAAA,KACLO,IAAI,CAAC,IAAIN,CAAAA,GAAAA,cAAAA,KACTb,EAAE,CAAC,SAASvM,CAAAA;YACXjC,CAAAA,GAAAA,6CAAAA,EAAO0E,IAAI,CAAC;gBACVE,QAAQ;gBACRD,SAAS1C,EAAEmO,KAAK;gBAChBS,OAAO5O,EAAE4O,KAATA;YACF;QACF,GACCrC,EAAE,CAAC,QAAShH,CAAAA;YACX,IAAIA,KAAKsJ,KAAK,KAAK,SACjB9Q,CAAAA,GAAAA,6CAAAA,EAAOoJ,KAAK,CAAC;gBACXxE,QAAQ;gBACRD,SAAS6C,KAAKtE,GAAG,CAACyB,OAAO;gBACzBkM,OAAOrJ,KAAKtE,GAAG,CAAC2N,KAAhBA;YACF;iBACK,IAAIrJ,KAAKsJ,KAAK,KAAK,UAAU,OAAOtJ,KAAK7C,OAAO,KAAK,UAC1D3E,CAAAA,GAAAA,6CAAAA,EAAOgQ,IAAI,CAAC;gBACVpL,QAAQ;gBACRD,SAASiL,6BAAOpI,KAAK7C,OAAO;YAC9B;iBACK,IAAI6C,KAAKlD,IAAI,KAAK,cAAc;gBACrCwK,cAActH,KAAKuH,KAAK,IAAI;gBAC5B6B,gBAAgBpJ,KAAKuJ,OAAO,IAAI;YAClC;QACF;QAEF,IAAIpC,SAAS,EAAE;QACfL,eAAeK,MAAM,CAClBH,EAAE,CAAC,QAAQwC,CAAAA;YACVrC,OAAOhG,IAAI,CAACqI,IAAItC,QAAQ;QAC1B,GACCF,EAAE,CAAC,SAASvM,CAAAA;YACXjC,CAAAA,GAAAA,6CAAAA,EAAO0E,IAAI,CAAC;gBACVE,QAAQ;gBACRD,SAAS1C,EAAE0C,OAAXA;YACF;QACF;QAEF,IAAI;YACF,MAAMuJ,CAAAA,GAAAA,cAAAA,EAAmBI;YAEzB,IAAIQ,aAAa,KAAK8B,eAAe,GACnC5Q,CAAAA,GAAAA,6CAAAA,EAAOgP,GAAG,CAAC;gBACTpK,QAAQ;gBACRD,SAAS,CAAA,MAAA,EAASmK,WAAU,CAAA,EAC1B8B,eAAe,IAAI,CAAA,YAAA,EAAeA,aAAY,CAAA,CAAG,GAAG,GADtDjM,iBAAAA,CAAAA;YAGF;YAGF,wEAAA;YACA,yEAAA;YACA,+BAAA;YACA,KAAK,IAAIA,WAAWgK,OAClB3O,CAAAA,GAAAA,6CAAAA,EAAOgP,GAAG,CAAC;gBACTpK,QAAQ;yBACRD;YACF;QAEJ,EAAE,OAAO1C,GAAG;YACV,MAAM,IAAIoH,MAAM;QAClB;IACF;AACF;AAEA,SAASuG,6BAAOjL,OAAe;IAC7B,OAAO,WAAWA;AACpB;AAEAtF,CAAAA,GAAAA,2CAAAA,EAA0B,GAAGe,CAAAA,GAAAA,6CAAAA,EAAIsH,OAAO,CAAA,KAAA,CAAO,EAAEwD;;;;;;;AC/LlC,kDACb+F,YAAqB7O,QAAQqL,GAAG,CAACyD,qBAAqB;IAEtD,IAAI,CAACD,WACH,OAAO1O;IAGT,MAAM4O,SAASF,UAAU7B,KAAK,CAAC,IAAI,CAAC,EAAE;IACtC,MAAMgC,eAAeD,OAAOE,WAAW,CAAC;IACxC,MAAM/M,OAAO6M,OAAOjP,SAAS,CAAC,GAAGkP;IACjC,OAAO;QACL9M,MAAMA;QACNoD,SAASyJ,OAAOjP,SAAS,CAACkP,eAAe;IAC3C;AACF;;;;;;;ACdA,MAAME,qCAAe;AAEN,kDAAiCC,UAAkB;IAChE,IAAIC,UAAUF,mCAAarH,IAAI,CAACsH;IAChC,IAAIC,SACF,OAAOA,OAAO,CAAC,EAAE;IAGnB,OAAO;AACT;;;;;;;;;;;;;;;;;;;;;;;;AEYO,MAAME;IACXC,WAAiC,IAAIvQ,MAArCuQ;IAEAC,SAASC,WAAmB,EAAEpQ,EAAc,EAAEmK,WAAqB,EAAE;QACnE,IAAI,CAAC+F,QAAQ,CAAChK,GAAG,CAACkK,aAAa;gBAACpQ;yBAAImK;QAAW;IACjD;IAEA,MAAM1E,QAAQ,WACZc,OAAO,MACPvG,EAAE,OACFiK,GAAG,eACHE,WAAW,WACXxE,UAAU,MACO,EAAiB;QAClC,IAAIwE,eAAe,MAAM;YACvBA,cAAc9L,CAAAA,GAAAA,qCAAAA,EAAKwK,IAAI,CAACoB,KAAK;YAC7B,MAAMjK,GAAG2M,SAAS,CAACxC,aAAa;QAClC;QAEA,IAAIxL,MAAMwJ,KAAKC,KAAK,CAAC,MAAMpI,GAAGkJ,QAAQ,CAACiB,aAAa;QACpD,IAAInF,MAAMW,UAAU,oBAAoB;QAExC,IAAI,CAAChH,GAAG,CAACqG,IAAI,EACXrG,GAAG,CAACqG,IAAI,GAAG,CAAC;QAGd,KAAK,IAAI/C,UAAUsE,QACjB5H,GAAG,CAACqG,IAAI,CAAC/C,OAAOY,IAAI,CAAC,GACnB,MAAO,MAAM,IAAI,CAACnE,cAAc,CAACuD,QAAQjC,IAAImK;QAGjD,MAAMnK,GAAG2M,SAAS,CAACxC,aAAahC,KAAKkI,SAAS,CAAC1R;IACjD;IAEA,MAAMD,eACJkK,aAA4B,EAC5B5I,EAAc,EACdmK,WAAqB,EACP;QACd,IAAIxL,MAAM,IAAI,CAACuR,QAAQ,CAACjL,GAAG,CAAC2D,cAAc/F,IAAI;QAC9C,IAAI,CAAClE,KACH,MAAM,IAAIiJ,MAAM,qBAAqBgB,cAAc/F,IAAI;QAGzD,IAAIyN,OAAOjS,CAAAA,GAAAA,qCAAAA,EAAKwK,IAAI,CAClBxK,CAAAA,GAAAA,qCAAAA,EAAK+D,OAAO,CAAC+H,cACb,gBACAvB,cAAc/F,IAChB;QACA,MAAMmN,CAAAA,GAAAA,mBAAAA,EAAIrR,IAAIqB,EAAE,EAAErB,IAAIwL,WAAW,EAAEnK,IAAIsQ;QAEvC,IAAIC,cAAcpI,KAAKC,KAAK,CAC1B,MAAMpI,GAAGkJ,QAAQ,CAAC7K,CAAAA,GAAAA,qCAAAA,EAAKwK,IAAI,CAACyH,MAAM,iBAAiB;QAGrD,IAAIC,YAAYC,YAAY,IAAI,MAC9B,KAAK,IAAIC,OAAO3H,CAAAA,GAAAA,sCAAAA,EACdyH,YAAYC,YACd,EACE,MAAM,IAAI,CAAC9R,cAAc,CAAC+R,KAAKzQ,IAAImK;QAIvC,OAAOoG,YAAYtK,OAAO;IAC5B;AACF;AAEArI,CAAAA,GAAAA,2CAAAA,EACE,GAAGe,CAAAA,GAAAA,6CAAAA,EAAIsH,OAAO,CAAA,qBAAA,CAAuB,EACrCgK;;;;;", "sources": ["node_modules/isexe/windows.js", "node_modules/isexe/mode.js", "node_modules/command-exists/lib/command-exists.js", "packages/core/package-manager/src/NodePackageManager.js", "packages/core/package-manager/src/utils.js", "packages/core/package-manager/src/installPackage.js", "node_modules/nullthrows/nullthrows.js", "packages/core/package-manager/src/Npm.js", "node_modules/cross-spawn/index.js", "node_modules/cross-spawn/lib/parse.js", "node_modules/cross-spawn/lib/util/resolveCommand.js", "node_modules/which/which.js", "node_modules/isexe/index.js", "node_modules/path-key/index.js", "node_modules/cross-spawn/lib/util/escape.js", "node_modules/cross-spawn/lib/util/readShebang.js", "node_modules/shebang-command/index.js", "node_modules/shebang-regex/index.js", "node_modules/cross-spawn/lib/enoent.js", "packages/core/package-manager/src/promiseFromProcess.js", "packages/core/package-manager/package.json", "packages/core/package-manager/src/Yarn.js", "node_modules/command-exists/index.js", "node_modules/split2/index.js", "packages/core/package-manager/src/JSONParseStream.js", "packages/core/package-manager/src/Pnpm.js", "packages/core/package-manager/src/getCurrentPackageManager.js", "packages/core/package-manager/src/validateModuleSpecifier.js", "packages/core/package-manager/src/index.js", "packages/core/package-manager/src/MockPackageInstaller.js"], "sourcesContent": ["module.exports = isexe\nisexe.sync = sync\n\nvar fs = require('fs')\n\nfunction checkPathExt (path, options) {\n  var pathext = options.pathExt !== undefined ?\n    options.pathExt : process.env.PATHEXT\n\n  if (!pathext) {\n    return true\n  }\n\n  pathext = pathext.split(';')\n  if (pathext.indexOf('') !== -1) {\n    return true\n  }\n  for (var i = 0; i < pathext.length; i++) {\n    var p = pathext[i].toLowerCase()\n    if (p && path.substr(-p.length).toLowerCase() === p) {\n      return true\n    }\n  }\n  return false\n}\n\nfunction checkStat (stat, path, options) {\n  if (!stat.isSymbolicLink() && !stat.isFile()) {\n    return false\n  }\n  return checkPathExt(path, options)\n}\n\nfunction isexe (path, options, cb) {\n  fs.stat(path, function (er, stat) {\n    cb(er, er ? false : checkStat(stat, path, options))\n  })\n}\n\nfunction sync (path, options) {\n  return checkStat(fs.statSync(path), path, options)\n}\n", "module.exports = isexe\nisexe.sync = sync\n\nvar fs = require('fs')\n\nfunction isexe (path, options, cb) {\n  fs.stat(path, function (er, stat) {\n    cb(er, er ? false : checkStat(stat, options))\n  })\n}\n\nfunction sync (path, options) {\n  return checkStat(fs.statSync(path), options)\n}\n\nfunction checkStat (stat, options) {\n  return stat.isFile() && checkMode(stat, options)\n}\n\nfunction checkMode (stat, options) {\n  var mod = stat.mode\n  var uid = stat.uid\n  var gid = stat.gid\n\n  var myUid = options.uid !== undefined ?\n    options.uid : process.getuid && process.getuid()\n  var myGid = options.gid !== undefined ?\n    options.gid : process.getgid && process.getgid()\n\n  var u = parseInt('100', 8)\n  var g = parseInt('010', 8)\n  var o = parseInt('001', 8)\n  var ug = u | g\n\n  var ret = (mod & o) ||\n    (mod & g) && gid === myGid ||\n    (mod & u) && uid === myUid ||\n    (mod & ug) && myUid === 0\n\n  return ret\n}\n", "'use strict';\n\nvar exec = require('child_process').exec;\nvar execSync = require('child_process').execSync;\nvar fs = require('fs');\nvar path = require('path');\nvar access = fs.access;\nvar accessSync = fs.accessSync;\nvar constants = fs.constants || fs;\n\nvar isUsingWindows = process.platform == 'win32'\n\nvar fileNotExists = function(commandName, callback){\n    access(commandName, constants.F_OK,\n    function(err){\n        callback(!err);\n    });\n};\n\nvar fileNotExistsSync = function(commandName){\n    try{\n        accessSync(commandName, constants.F_OK);\n        return false;\n    }catch(e){\n        return true;\n    }\n};\n\nvar localExecutable = function(commandName, callback){\n    access(commandName, constants.F_OK | constants.X_OK,\n        function(err){\n        callback(null, !err);\n    });\n};\n\nvar localExecutableSync = function(commandName){\n    try{\n        accessSync(commandName, constants.F_OK | constants.X_OK);\n        return true;\n    }catch(e){\n        return false;\n    }\n}\n\nvar commandExistsUnix = function(commandName, cleanedCommandName, callback) {\n\n    fileNotExists(commandName, function(isFile){\n\n        if(!isFile){\n            var child = exec('command -v ' + cleanedCommandName +\n                  ' 2>/dev/null' +\n                  ' && { echo >&1 ' + cleanedCommandName + '; exit 0; }',\n                  function (error, stdout, stderr) {\n                      callback(null, !!stdout);\n                  });\n            return;\n        }\n\n        localExecutable(commandName, callback);\n    });\n\n}\n\nvar commandExistsWindows = function(commandName, cleanedCommandName, callback) {\n  // Regex from Julio from: https://stackoverflow.com/questions/51494579/regex-windows-path-validator\n  if (!(/^(?!(?:.*\\s|.*\\.|\\W+)$)(?:[a-zA-Z]:)?(?:(?:[^<>:\"\\|\\?\\*\\n])+(?:\\/\\/|\\/|\\\\\\\\|\\\\)?)+$/m.test(commandName))) {\n    callback(null, false);\n    return;\n  }\n  var child = exec('where ' + cleanedCommandName,\n    function (error) {\n      if (error !== null){\n        callback(null, false);\n      } else {\n        callback(null, true);\n      }\n    }\n  )\n}\n\nvar commandExistsUnixSync = function(commandName, cleanedCommandName) {\n  if(fileNotExistsSync(commandName)){\n      try {\n        var stdout = execSync('command -v ' + cleanedCommandName +\n              ' 2>/dev/null' +\n              ' && { echo >&1 ' + cleanedCommandName + '; exit 0; }'\n              );\n        return !!stdout;\n      } catch (error) {\n        return false;\n      }\n  }\n  return localExecutableSync(commandName);\n}\n\nvar commandExistsWindowsSync = function(commandName, cleanedCommandName, callback) {\n  // Regex from Julio from: https://stackoverflow.com/questions/51494579/regex-windows-path-validator\n  if (!(/^(?!(?:.*\\s|.*\\.|\\W+)$)(?:[a-zA-Z]:)?(?:(?:[^<>:\"\\|\\?\\*\\n])+(?:\\/\\/|\\/|\\\\\\\\|\\\\)?)+$/m.test(commandName))) {\n    return false;\n  }\n  try {\n      var stdout = execSync('where ' + cleanedCommandName, {stdio: []});\n      return !!stdout;\n  } catch (error) {\n      return false;\n  }\n}\n\nvar cleanInput = function(s) {\n  if (/[^A-Za-z0-9_\\/:=-]/.test(s)) {\n    s = \"'\"+s.replace(/'/g,\"'\\\\''\")+\"'\";\n    s = s.replace(/^(?:'')+/g, '') // unduplicate single-quote at the beginning\n      .replace(/\\\\'''/g, \"\\\\'\" ); // remove non-escaped single-quote if there are enclosed between 2 escaped\n  }\n  return s;\n}\n\nif (isUsingWindows) {\n  cleanInput = function(s) {\n    var isPathName = /[\\\\]/.test(s);\n    if (isPathName) {\n      var dirname = '\"' + path.dirname(s) + '\"';\n      var basename = '\"' + path.basename(s) + '\"';\n      return dirname + ':' + basename;\n    }\n    return '\"' + s + '\"';\n  }\n}\n\nmodule.exports = function commandExists(commandName, callback) {\n  var cleanedCommandName = cleanInput(commandName);\n  if (!callback && typeof Promise !== 'undefined') {\n    return new Promise(function(resolve, reject){\n      commandExists(commandName, function(error, output) {\n        if (output) {\n          resolve(commandName);\n        } else {\n          reject(error);\n        }\n      });\n    });\n  }\n  if (isUsingWindows) {\n    commandExistsWindows(commandName, cleanedCommandName, callback);\n  } else {\n    commandExistsUnix(commandName, cleanedCommandName, callback);\n  }\n};\n\nmodule.exports.sync = function(commandName) {\n  var cleanedCommandName = cleanInput(commandName);\n  if (isUsingWindows) {\n    return commandExistsWindowsSync(commandName, cleanedCommandName);\n  } else {\n    return commandExistsUnixSync(commandName, cleanedCommandName);\n  }\n};\n", "// @flow\nimport type {FilePath, DependencySpecifier, SemverRange} from '@parcel/types';\nimport type {FileSystem} from '@parcel/fs';\nimport type {\n  ModuleRequest,\n  PackageManager,\n  PackageInstaller,\n  InstallOptions,\n  Invalidations,\n  PackageManagerResolveResult,\n} from '@parcel/types';\n\nimport {registerSerializableClass} from '@parcel/core';\nimport ThrowableDiagnostic, {\n  encodeJSONKeyComponent,\n  escapeMarkdown,\n  generateJSONCodeHighlights,\n  md,\n} from '@parcel/diagnostic';\nimport {NodeFS} from '@parcel/fs';\nimport nativeFS from 'fs';\nimport Module from 'module';\nimport path from 'path';\nimport semver from 'semver';\nimport logger from '@parcel/logger';\n\nimport {getModuleParts} from '@parcel/utils';\nimport {getConflictingLocalDependencies} from './utils';\nimport {installPackage} from './installPackage';\nimport pkg from '../package.json';\nimport {ResolverBase} from '@parcel/node-resolver-core';\nimport {pathToFileURL} from 'url';\nimport {transformSync} from '@swc/core';\n\n// Package.json fields. Must match package_json.rs.\nconst MAIN = 1 << 0;\nconst SOURCE = 1 << 2;\nconst NODE_CONDITION = 1 << 3;\nconst SOURCE_CONDITION = 1 << 17;\nconst ENTRIES = MAIN | SOURCE;\nconst CONDITIONS = NODE_CONDITION | SOURCE_CONDITION;\n\nconst NODE_MODULES = `${path.sep}node_modules${path.sep}`;\n\nconst IS_FILE = 1 << 0;\nconst IS_DIR = 1 << 1;\nconst IS_SYMLINK = 1 << 2;\n\n// There can be more than one instance of NodePackageManager, but node has only a single module cache.\n// Therefore, the resolution cache and the map of parent to child modules should also be global.\nconst cache = new Map<DependencySpecifier, PackageManagerResolveResult>();\nconst children = new Map<FilePath, Set<DependencySpecifier>>();\nconst invalidationsCache = new Map<string, Invalidations>();\n\n// This implements a package manager for Node by monkey patching the Node require\n// algorithm so that it uses the specified FileSystem instead of the native one.\n// It also handles installing packages when they are required if not already installed.\n// See https://github.com/nodejs/node/blob/master/lib/internal/modules/cjs/loader.js\n// for reference to Node internals.\nexport class NodePackageManager implements PackageManager {\n  fs: FileSystem;\n  projectRoot: FilePath;\n  installer: ?PackageInstaller;\n  resolver: ResolverBase;\n  currentExtensions: Array<string>;\n\n  constructor(\n    fs: FileSystem,\n    projectRoot: FilePath,\n    installer?: ?PackageInstaller,\n  ) {\n    this.fs = fs;\n    this.projectRoot = projectRoot;\n    this.installer = installer;\n\n    // $FlowFixMe - no type for _extensions\n    this.currentExtensions = Object.keys(Module._extensions).map(e =>\n      e.substring(1),\n    );\n  }\n\n  _createResolver(): ResolverBase {\n    return new ResolverBase(this.projectRoot, {\n      fs:\n        this.fs instanceof NodeFS && process.versions.pnp == null\n          ? undefined\n          : {\n              read: path => this.fs.readFileSync(path),\n              kind: path => {\n                let flags = 0;\n                try {\n                  let stat = this.fs.lstatSync(path);\n                  if (stat.isSymbolicLink()) {\n                    flags |= IS_SYMLINK;\n                    stat = this.fs.statSync(path);\n                  }\n                  if (stat.isFile()) {\n                    flags |= IS_FILE;\n                  } else if (stat.isDirectory()) {\n                    flags |= IS_DIR;\n                  }\n                } catch (err) {\n                  // ignore\n                }\n                return flags;\n              },\n              readLink: path => this.fs.readlinkSync(path),\n            },\n      mode: 2,\n      entries: ENTRIES,\n      conditions: CONDITIONS,\n      packageExports: true,\n      moduleDirResolver:\n        process.versions.pnp != null\n          ? (module, from) => {\n              // $FlowFixMe[prop-missing]\n              let pnp = Module.findPnpApi(path.dirname(from));\n\n              return pnp.resolveToUnqualified(\n                // append slash to force loading builtins from npm\n                module + '/',\n                from,\n              );\n            }\n          : undefined,\n      extensions: this.currentExtensions,\n      typescript: true,\n    });\n  }\n\n  static deserialize(opts: any): NodePackageManager {\n    return new NodePackageManager(opts.fs, opts.projectRoot, opts.installer);\n  }\n\n  serialize(): {|\n    $$raw: boolean,\n    fs: FileSystem,\n    projectRoot: FilePath,\n    installer: ?PackageInstaller,\n  |} {\n    return {\n      $$raw: false,\n      fs: this.fs,\n      projectRoot: this.projectRoot,\n      installer: this.installer,\n    };\n  }\n\n  async require(\n    name: DependencySpecifier,\n    from: FilePath,\n    opts: ?{|\n      range?: ?SemverRange,\n      shouldAutoInstall?: boolean,\n      saveDev?: boolean,\n    |},\n  ): Promise<any> {\n    let {resolved, type} = await this.resolve(name, from, opts);\n    if (type === 2) {\n      logger.warn({\n        message: 'ES module dependencies are experimental.',\n        origin: '@parcel/package-manager',\n        codeFrames: [\n          {\n            filePath: resolved,\n            codeHighlights: [],\n          },\n        ],\n      });\n\n      // On Windows, Node requires absolute paths to be file URLs.\n      if (process.platform === 'win32' && path.isAbsolute(resolved)) {\n        resolved = pathToFileURL(resolved);\n      }\n\n      // $FlowFixMe\n      return import(resolved);\n    }\n    return this.load(resolved, from);\n  }\n\n  requireSync(name: DependencySpecifier, from: FilePath): any {\n    let {resolved} = this.resolveSync(name, from);\n    return this.load(resolved, from);\n  }\n\n  load(filePath: FilePath, from: FilePath): any {\n    if (!path.isAbsolute(filePath)) {\n      // Node builtin module\n      // $FlowFixMe\n      return require(filePath);\n    }\n\n    // $FlowFixMe[prop-missing]\n    const cachedModule = Module._cache[filePath];\n    if (cachedModule !== undefined) {\n      return cachedModule.exports;\n    }\n\n    // $FlowFixMe\n    let m = new Module(filePath, Module._cache[from] || module.parent);\n\n    // $FlowFixMe _extensions not in type\n    const extensions = Object.keys(Module._extensions);\n    // This handles supported extensions changing due to, for example, esbuild/register being used\n    // We assume that the extension list will change in size - as these tools usually add support for\n    // additional extensions.\n    if (extensions.length !== this.currentExtensions.length) {\n      this.currentExtensions = extensions.map(e => e.substring(1));\n      this.resolver = this._createResolver();\n    }\n\n    // $FlowFixMe[prop-missing]\n    Module._cache[filePath] = m;\n\n    // Patch require within this module so it goes through our require\n    m.require = id => {\n      return this.requireSync(id, filePath);\n    };\n\n    // Patch `fs.readFileSync` temporarily so that it goes through our file system\n    let {readFileSync, statSync} = nativeFS;\n    // $FlowFixMe\n    nativeFS.readFileSync = (filename, encoding) => {\n      return this.fs.readFileSync(filename, encoding);\n    };\n\n    // $FlowFixMe\n    nativeFS.statSync = filename => {\n      return this.fs.statSync(filename);\n    };\n\n    if (!filePath.includes(NODE_MODULES)) {\n      let extname = path.extname(filePath);\n      if (\n        (extname === '.ts' ||\n          extname === '.tsx' ||\n          extname === '.mts' ||\n          extname === '.cts') &&\n        // $FlowFixMe\n        !Module._extensions[extname]\n      ) {\n        let compile = m._compile;\n        m._compile = (code, filename) => {\n          let out = transformSync(code, {\n            filename,\n            module: {type: 'commonjs', ignoreDynamic: true},\n          });\n          compile.call(m, out.code, filename);\n        };\n\n        // $FlowFixMe\n        Module._extensions[extname] = (m, filename) => {\n          // $FlowFixMe\n          delete Module._extensions[extname];\n          // $FlowFixMe\n          Module._extensions['.js'](m, filename);\n        };\n      }\n    }\n\n    try {\n      m.load(filePath);\n    } catch (err) {\n      // $FlowFixMe[prop-missing]\n      delete Module._cache[filePath];\n      throw err;\n    } finally {\n      // $FlowFixMe\n      nativeFS.readFileSync = readFileSync;\n      // $FlowFixMe\n      nativeFS.statSync = statSync;\n    }\n\n    return m.exports;\n  }\n\n  async resolve(\n    id: DependencySpecifier,\n    from: FilePath,\n    options?: ?{|\n      range?: ?SemverRange,\n      shouldAutoInstall?: boolean,\n      saveDev?: boolean,\n    |},\n  ): Promise<PackageManagerResolveResult> {\n    let basedir = path.dirname(from);\n    let key = basedir + ':' + id;\n    let resolved = cache.get(key);\n    if (!resolved) {\n      let [name] = getModuleParts(id);\n      try {\n        resolved = this.resolveInternal(id, from);\n      } catch (e) {\n        if (\n          e.code !== 'MODULE_NOT_FOUND' ||\n          options?.shouldAutoInstall !== true ||\n          id.startsWith('.') // a local file, don't autoinstall\n        ) {\n          if (\n            e.code === 'MODULE_NOT_FOUND' &&\n            options?.shouldAutoInstall !== true\n          ) {\n            let err = new ThrowableDiagnostic({\n              diagnostic: {\n                message: escapeMarkdown(e.message),\n                hints: [\n                  'Autoinstall is disabled, please install this package manually and restart Parcel.',\n                ],\n              },\n            });\n            // $FlowFixMe - needed for loadParcelPlugin\n            err.code = 'MODULE_NOT_FOUND';\n            throw err;\n          } else {\n            throw e;\n          }\n        }\n\n        let conflicts = await getConflictingLocalDependencies(\n          this.fs,\n          name,\n          from,\n          this.projectRoot,\n        );\n\n        if (conflicts == null) {\n          this.invalidate(id, from);\n          await this.install([{name, range: options?.range}], from, {\n            saveDev: options?.saveDev ?? true,\n          });\n\n          return this.resolve(id, from, {\n            ...options,\n            shouldAutoInstall: false,\n          });\n        }\n\n        throw new ThrowableDiagnostic({\n          diagnostic: conflicts.fields.map(field => ({\n            message: md`Could not find module \"${name}\", but it was listed in package.json. Run your package manager first.`,\n            origin: '@parcel/package-manager',\n            codeFrames: [\n              {\n                filePath: conflicts.filePath,\n                language: 'json',\n                code: conflicts.json,\n                codeHighlights: generateJSONCodeHighlights(conflicts.json, [\n                  {\n                    key: `/${field}/${encodeJSONKeyComponent(name)}`,\n                    type: 'key',\n                    message: 'Defined here, but not installed',\n                  },\n                ]),\n              },\n            ],\n          })),\n        });\n      }\n\n      let range = options?.range;\n      if (range != null) {\n        let pkg = resolved.pkg;\n        if (pkg == null || !semver.satisfies(pkg.version, range)) {\n          let conflicts = await getConflictingLocalDependencies(\n            this.fs,\n            name,\n            from,\n            this.projectRoot,\n          );\n\n          if (conflicts == null && options?.shouldAutoInstall === true) {\n            this.invalidate(id, from);\n            await this.install([{name, range}], from);\n            return this.resolve(id, from, {\n              ...options,\n              shouldAutoInstall: false,\n            });\n          } else if (conflicts != null) {\n            throw new ThrowableDiagnostic({\n              diagnostic: {\n                message: md`Could not find module \"${name}\" satisfying ${range}.`,\n                origin: '@parcel/package-manager',\n                codeFrames: [\n                  {\n                    filePath: conflicts.filePath,\n                    language: 'json',\n                    code: conflicts.json,\n                    codeHighlights: generateJSONCodeHighlights(\n                      conflicts.json,\n                      conflicts.fields.map(field => ({\n                        key: `/${field}/${encodeJSONKeyComponent(name)}`,\n                        type: 'key',\n                        message: 'Found this conflicting local requirement.',\n                      })),\n                    ),\n                  },\n                ],\n              },\n            });\n          }\n\n          let version = pkg?.version;\n          let message = md`Could not resolve package \"${name}\" that satisfies ${range}.`;\n          if (version != null) {\n            message += md` Found ${version}.`;\n          }\n\n          throw new ThrowableDiagnostic({\n            diagnostic: {\n              message,\n              hints: [\n                'Looks like the incompatible version was installed transitively. Add this package as a direct dependency with a compatible version range.',\n              ],\n            },\n          });\n        }\n      }\n\n      cache.set(key, resolved);\n      invalidationsCache.clear();\n\n      // Add the specifier as a child to the parent module.\n      // Don't do this if the specifier was an absolute path, as this was likely a dynamically resolved path\n      // (e.g. babel uses require() to load .babelrc.js configs and we don't want them to be added  as children of babel itself).\n      if (!path.isAbsolute(name)) {\n        let moduleChildren = children.get(from);\n        if (!moduleChildren) {\n          moduleChildren = new Set();\n          children.set(from, moduleChildren);\n        }\n\n        moduleChildren.add(name);\n      }\n    }\n\n    return resolved;\n  }\n\n  resolveSync(\n    name: DependencySpecifier,\n    from: FilePath,\n  ): PackageManagerResolveResult {\n    let basedir = path.dirname(from);\n    let key = basedir + ':' + name;\n    let resolved = cache.get(key);\n    if (!resolved) {\n      resolved = this.resolveInternal(name, from);\n      cache.set(key, resolved);\n      invalidationsCache.clear();\n\n      if (!path.isAbsolute(name)) {\n        let moduleChildren = children.get(from);\n        if (!moduleChildren) {\n          moduleChildren = new Set();\n          children.set(from, moduleChildren);\n        }\n\n        moduleChildren.add(name);\n      }\n    }\n\n    return resolved;\n  }\n\n  async install(\n    modules: Array<ModuleRequest>,\n    from: FilePath,\n    opts?: InstallOptions,\n  ) {\n    await installPackage(this.fs, this, modules, from, this.projectRoot, {\n      packageInstaller: this.installer,\n      ...opts,\n    });\n  }\n\n  getInvalidations(name: DependencySpecifier, from: FilePath): Invalidations {\n    let basedir = path.dirname(from);\n    let cacheKey = basedir + ':' + name;\n    let resolved = cache.get(cacheKey);\n\n    if (resolved && path.isAbsolute(resolved.resolved)) {\n      let cached = invalidationsCache.get(resolved.resolved);\n      if (cached != null) {\n        return cached;\n      }\n\n      let res = {\n        invalidateOnFileCreate: [],\n        invalidateOnFileChange: new Set(),\n        invalidateOnStartup: false,\n      };\n\n      let seen = new Set();\n      let addKey = (name, from) => {\n        let basedir = path.dirname(from);\n        let key = basedir + ':' + name;\n        if (seen.has(key)) {\n          return;\n        }\n\n        seen.add(key);\n        let resolved = cache.get(key);\n        if (!resolved || !path.isAbsolute(resolved.resolved)) {\n          return;\n        }\n\n        res.invalidateOnFileCreate.push(...resolved.invalidateOnFileCreate);\n        res.invalidateOnFileChange.add(resolved.resolved);\n\n        for (let file of resolved.invalidateOnFileChange) {\n          res.invalidateOnFileChange.add(file);\n        }\n\n        let moduleChildren = children.get(resolved.resolved);\n        if (moduleChildren) {\n          for (let specifier of moduleChildren) {\n            addKey(specifier, resolved.resolved);\n          }\n        }\n      };\n\n      addKey(name, from);\n\n      // If this is an ES module, we won't have any of the dependencies because import statements\n      // cannot be intercepted. Instead, ask the resolver to parse the file and recursively analyze the deps.\n      if (resolved.type === 2) {\n        let invalidations = this.resolver.getInvalidations(resolved.resolved);\n        invalidations.invalidateOnFileChange.forEach(i =>\n          res.invalidateOnFileChange.add(i),\n        );\n        invalidations.invalidateOnFileCreate.forEach(i =>\n          res.invalidateOnFileCreate.push(i),\n        );\n        res.invalidateOnStartup ||= invalidations.invalidateOnStartup;\n        if (res.invalidateOnStartup) {\n          logger.warn({\n            message: md`${path.relative(\n              this.projectRoot,\n              resolved.resolved,\n            )} contains non-statically analyzable dependencies in its module graph. This causes Parcel to invalidate the cache on startup.`,\n            origin: '@parcel/package-manager',\n          });\n        }\n      }\n\n      invalidationsCache.set(resolved.resolved, res);\n      return res;\n    }\n\n    return {\n      invalidateOnFileCreate: [],\n      invalidateOnFileChange: new Set(),\n      invalidateOnStartup: false,\n    };\n  }\n\n  invalidate(name: DependencySpecifier, from: FilePath) {\n    let seen = new Set();\n\n    let invalidate = (name, from) => {\n      let basedir = path.dirname(from);\n      let key = basedir + ':' + name;\n      if (seen.has(key)) {\n        return;\n      }\n\n      seen.add(key);\n      let resolved = cache.get(key);\n      if (!resolved || !path.isAbsolute(resolved.resolved)) {\n        return;\n      }\n\n      invalidationsCache.delete(resolved.resolved);\n\n      // $FlowFixMe\n      let module = Module._cache[resolved.resolved];\n      if (module) {\n        // $FlowFixMe\n        delete Module._cache[resolved.resolved];\n      }\n\n      let moduleChildren = children.get(resolved.resolved);\n      if (moduleChildren) {\n        for (let specifier of moduleChildren) {\n          invalidate(specifier, resolved.resolved);\n        }\n      }\n\n      children.delete(resolved.resolved);\n      cache.delete(key);\n    };\n\n    invalidate(name, from);\n    this.resolver = this._createResolver();\n  }\n\n  resolveInternal(name: string, from: string): PackageManagerResolveResult {\n    if (this.resolver == null) {\n      this.resolver = this._createResolver();\n    }\n\n    let res = this.resolver.resolve({\n      filename: name,\n      specifierType: 'commonjs',\n      parent: from,\n    });\n\n    // Invalidate whenever the .pnp.js file changes.\n    // TODO: only when we actually resolve a node_modules package?\n    if (process.versions.pnp != null && res.invalidateOnFileChange) {\n      // $FlowFixMe[prop-missing]\n      let pnp = Module.findPnpApi(path.dirname(from));\n      res.invalidateOnFileChange.push(pnp.resolveToUnqualified('pnpapi', null));\n    }\n\n    if (res.error) {\n      let e = new Error(`Could not resolve module \"${name}\" from \"${from}\"`);\n      // $FlowFixMe\n      e.code = 'MODULE_NOT_FOUND';\n      throw e;\n    }\n    switch (res.resolution.type) {\n      case 'Path': {\n        let self = this;\n        let resolved = res.resolution.value;\n        return {\n          resolved,\n          invalidateOnFileChange: new Set(res.invalidateOnFileChange),\n          invalidateOnFileCreate: res.invalidateOnFileCreate,\n          type: res.moduleType,\n          get pkg() {\n            let pkgPath = self.fs.findAncestorFile(\n              ['package.json'],\n              resolved,\n              self.projectRoot,\n            );\n            return pkgPath\n              ? JSON.parse(self.fs.readFileSync(pkgPath, 'utf8'))\n              : null;\n          },\n        };\n      }\n      case 'Builtin': {\n        let {scheme, module} = res.resolution.value;\n        return {\n          resolved: scheme ? `${scheme}:${module}` : module,\n          invalidateOnFileChange: new Set(res.invalidateOnFileChange),\n          invalidateOnFileCreate: res.invalidateOnFileCreate,\n          type: res.moduleType,\n        };\n      }\n      default:\n        throw new Error('Unknown resolution type');\n    }\n  }\n}\n\nregisterSerializableClass(\n  `${pkg.version}:NodePackageManager`,\n  NodePackageManager,\n);\n", "// @flow strict-local\n\nimport type {FilePath, ModuleRequest} from '@parcel/types';\nimport type {FileSystem} from '@parcel/fs';\n\nimport invariant from 'assert';\nimport ThrowableDiagnostic from '@parcel/diagnostic';\nimport {resolveConfig} from '@parcel/utils';\nimport {exec as _exec} from 'child_process';\nimport {promisify} from 'util';\n\nexport const exec: (\n  command: string,\n  options?: child_process$execOpts,\n) => Promise<{|stdout: string | Buffer, stderr: string | Buffer|}> = _exec\n  ? promisify(_exec)\n  : // _exec is undefined in browser builds\n    _exec;\n\nexport function npmSpecifierFromModuleRequest(\n  moduleRequest: ModuleRequest,\n): string {\n  return moduleRequest.range != null\n    ? [moduleRequest.name, moduleRequest.range].join('@')\n    : moduleRequest.name;\n}\n\nexport function moduleRequestsFromDependencyMap(dependencyMap: {|\n  [string]: string,\n|}): Array<ModuleRequest> {\n  return Object.entries(dependencyMap).map(([name, range]) => {\n    invariant(typeof range === 'string');\n    return {\n      name,\n      range,\n    };\n  });\n}\n\nexport async function getConflictingLocalDependencies(\n  fs: FileSystem,\n  name: string,\n  local: FilePath,\n  projectRoot: FilePath,\n): Promise<?{|json: string, filePath: FilePath, fields: Array<string>|}> {\n  let pkgPath = await resolveConfig(fs, local, ['package.json'], projectRoot);\n  if (pkgPath == null) {\n    return;\n  }\n\n  let pkgStr = await fs.readFile(pkgPath, 'utf8');\n  let pkg;\n  try {\n    pkg = JSON.parse(pkgStr);\n  } catch (e) {\n    // TODO: codeframe\n    throw new ThrowableDiagnostic({\n      diagnostic: {\n        message: 'Failed to parse package.json',\n        origin: '@parcel/package-manager',\n      },\n    });\n  }\n\n  if (typeof pkg !== 'object' || pkg == null) {\n    // TODO: codeframe\n    throw new ThrowableDiagnostic({\n      diagnostic: {\n        message: 'Expected package.json contents to be an object.',\n        origin: '@parcel/package-manager',\n      },\n    });\n  }\n\n  let fields = [];\n  for (let field of ['dependencies', 'devDependencies', 'peerDependencies']) {\n    if (\n      typeof pkg[field] === 'object' &&\n      pkg[field] != null &&\n      pkg[field][name] != null\n    ) {\n      fields.push(field);\n    }\n  }\n\n  if (fields.length > 0) {\n    return {\n      filePath: pkgPath,\n      json: pkgStr,\n      fields,\n    };\n  }\n}\n", "// @flow\n\nimport type {FilePath, PackageJSON} from '@parcel/types';\nimport type {\n  ModuleRequest,\n  PackageManager,\n  PackageInstaller,\n  InstallOptions,\n} from '@parcel/types';\nimport type {FileSystem} from '@parcel/fs';\n\nimport invariant from 'assert';\nimport path from 'path';\nimport nullthrows from 'nullthrows';\nimport semver from 'semver';\nimport ThrowableDiagnostic, {\n  generateJSONCodeHighlights,\n  encodeJSONKeyComponent,\n  md,\n} from '@parcel/diagnostic';\nimport logger from '@parcel/logger';\nimport {loadConfig, PromiseQueue, resolveConfig} from '@parcel/utils';\nimport WorkerFarm from '@parcel/workers';\n\nimport {Npm} from './Npm';\nimport {Yarn} from './Yarn';\nimport {Pnpm} from './Pnpm.js';\nimport {getConflictingLocalDependencies} from './utils';\nimport getCurrentPackageManager from './getCurrentPackageManager';\nimport validateModuleSpecifier from './validateModuleSpecifier';\n\nasync function install(\n  fs: FileSystem,\n  packageManager: PackageManager,\n  modules: Array<ModuleRequest>,\n  from: FilePath,\n  projectRoot: FilePath,\n  options: InstallOptions = {},\n): Promise<void> {\n  let {installPeers = true, saveDev = true, packageInstaller} = options;\n  let moduleNames = modules.map(m => m.name).join(', ');\n\n  logger.progress(`Installing ${moduleNames}...`);\n\n  let fromPkgPath = await resolveConfig(\n    fs,\n    from,\n    ['package.json'],\n    projectRoot,\n  );\n  let cwd = fromPkgPath ? path.dirname(fromPkgPath) : fs.cwd();\n\n  if (!packageInstaller) {\n    packageInstaller = await determinePackageInstaller(fs, from, projectRoot);\n  }\n\n  try {\n    await packageInstaller.install({\n      modules,\n      saveDev,\n      cwd,\n      packagePath: fromPkgPath,\n      fs,\n    });\n  } catch (err) {\n    throw new Error(`Failed to install ${moduleNames}: ${err.message}`);\n  }\n\n  if (installPeers) {\n    await Promise.all(\n      modules.map(m =>\n        installPeerDependencies(\n          fs,\n          packageManager,\n          m,\n          from,\n          projectRoot,\n          options,\n        ),\n      ),\n    );\n  }\n}\n\nasync function installPeerDependencies(\n  fs: FileSystem,\n  packageManager: PackageManager,\n  module: ModuleRequest,\n  from: FilePath,\n  projectRoot: FilePath,\n  options,\n) {\n  const {resolved} = await packageManager.resolve(module.name, from);\n  const modulePkg: PackageJSON = nullthrows(\n    await loadConfig(fs, resolved, ['package.json'], projectRoot),\n  ).config;\n  const peers = modulePkg.peerDependencies || {};\n\n  let modules: Array<ModuleRequest> = [];\n  for (let [name, range] of Object.entries(peers)) {\n    invariant(typeof range === 'string');\n\n    let conflicts = await getConflictingLocalDependencies(\n      fs,\n      name,\n      from,\n      projectRoot,\n    );\n    if (conflicts) {\n      let {pkg} = await packageManager.resolve(name, from);\n      invariant(pkg);\n      if (!semver.satisfies(pkg.version, range)) {\n        throw new ThrowableDiagnostic({\n          diagnostic: {\n            message: md`Could not install the peer dependency \"${name}\" for \"${module.name}\", installed version ${pkg.version} is incompatible with ${range}`,\n            origin: '@parcel/package-manager',\n            codeFrames: [\n              {\n                filePath: conflicts.filePath,\n                language: 'json',\n                code: conflicts.json,\n                codeHighlights: generateJSONCodeHighlights(\n                  conflicts.json,\n                  conflicts.fields.map(field => ({\n                    key: `/${field}/${encodeJSONKeyComponent(name)}`,\n                    type: 'key',\n                    message: 'Found this conflicting local requirement.',\n                  })),\n                ),\n              },\n            ],\n          },\n        });\n      }\n\n      continue;\n    }\n    modules.push({name, range});\n  }\n\n  if (modules.length) {\n    await install(\n      fs,\n      packageManager,\n      modules,\n      from,\n      projectRoot,\n      Object.assign({}, options, {installPeers: false}),\n    );\n  }\n}\n\nasync function determinePackageInstaller(\n  fs: FileSystem,\n  filepath: FilePath,\n  projectRoot: FilePath,\n): Promise<PackageInstaller> {\n  let configFile = await resolveConfig(\n    fs,\n    filepath,\n    ['package-lock.json', 'pnpm-lock.yaml', 'yarn.lock'],\n    projectRoot,\n  );\n\n  let configName = configFile && path.basename(configFile);\n\n  // Always use the package manager that seems to be used in the project,\n  // falling back to a different one wouldn't update the existing lockfile.\n  if (configName === 'package-lock.json') {\n    return new Npm();\n  } else if (configName === 'pnpm-lock.yaml') {\n    return new Pnpm();\n  } else if (configName === 'yarn.lock') {\n    return new Yarn();\n  }\n\n  let currentPackageManager = getCurrentPackageManager()?.name;\n  if (currentPackageManager === 'npm') {\n    return new Npm();\n  } else if (currentPackageManager === 'yarn') {\n    return new Yarn();\n  } else if (currentPackageManager === 'pnpm') {\n    return new Pnpm();\n  }\n\n  if (await Yarn.exists()) {\n    return new Yarn();\n  } else if (await Pnpm.exists()) {\n    return new Pnpm();\n  } else {\n    return new Npm();\n  }\n}\n\nlet queue = new PromiseQueue({maxConcurrent: 1});\nlet modulesInstalling: Set<string> = new Set();\n\n// Exported so that it may be invoked from the worker api below.\n// Do not call this directly! This can result in concurrent package installations\n// across multiple instances of the package manager.\nexport function _addToInstallQueue(\n  fs: FileSystem,\n  packageManager: PackageManager,\n  modules: Array<ModuleRequest>,\n  filePath: FilePath,\n  projectRoot: FilePath,\n  options?: InstallOptions,\n): Promise<mixed> {\n  modules = modules.map(request => ({\n    name: validateModuleSpecifier(request.name),\n    range: request.range,\n  }));\n\n  // Wrap PromiseQueue and track modules that are currently installing.\n  // If a request comes in for a module that is currently installing, don't bother\n  // enqueuing it.\n  let modulesToInstall = modules.filter(\n    m => !modulesInstalling.has(getModuleRequestKey(m)),\n  );\n  if (modulesToInstall.length) {\n    for (let m of modulesToInstall) {\n      modulesInstalling.add(getModuleRequestKey(m));\n    }\n\n    queue\n      .add(() =>\n        install(\n          fs,\n          packageManager,\n          modulesToInstall,\n          filePath,\n          projectRoot,\n          options,\n        ).then(() => {\n          for (let m of modulesToInstall) {\n            modulesInstalling.delete(getModuleRequestKey(m));\n          }\n        }),\n      )\n      .then(\n        () => {},\n        () => {},\n      );\n  }\n\n  return queue.run();\n}\n\nexport function installPackage(\n  fs: FileSystem,\n  packageManager: PackageManager,\n  modules: Array<ModuleRequest>,\n  filePath: FilePath,\n  projectRoot: FilePath,\n  options?: InstallOptions,\n): Promise<mixed> {\n  if (WorkerFarm.isWorker()) {\n    let workerApi = WorkerFarm.getWorkerApi();\n    // TODO this should really be `__filename` but without the rewriting.\n    let bundlePath =\n      process.env.PARCEL_BUILD_ENV === 'production' &&\n      !process.env.PARCEL_SELF_BUILD\n        ? path.join(__dirname, '..', 'lib/index.js')\n        : __filename;\n    return workerApi.callMaster({\n      location: bundlePath,\n      args: [fs, packageManager, modules, filePath, projectRoot, options],\n      method: '_addToInstallQueue',\n    });\n  }\n\n  return _addToInstallQueue(\n    fs,\n    packageManager,\n    modules,\n    filePath,\n    projectRoot,\n    options,\n  );\n}\n\nfunction getModuleRequestKey(moduleRequest: ModuleRequest): string {\n  return [moduleRequest.name, moduleRequest.range].join('@');\n}\n", "'use strict';\n\nfunction nullthrows(x, message) {\n  if (x != null) {\n    return x;\n  }\n  var error = new Error(message !== undefined ? message : 'Got unexpected ' + x);\n  error.framesToPop = 1; // Skip nullthrows's own stack frame.\n  throw error;\n}\n\nmodule.exports = nullthrows;\nmodule.exports.default = nullthrows;\n\nObject.defineProperty(module.exports, '__esModule', {value: true});\n", "// @flow strict-local\n\nimport type {PackageInstaller, InstallerOptions} from '@parcel/types';\n\nimport path from 'path';\nimport spawn from 'cross-spawn';\nimport logger from '@parcel/logger';\nimport promiseFromProcess from './promiseFromProcess';\nimport {registerSerializableClass} from '@parcel/core';\nimport {npmSpecifierFromModuleRequest} from './utils';\n\n// $FlowFixMe\nimport pkg from '../package.json';\n\nconst NPM_CMD = 'npm';\n\nexport class Npm implements PackageInstaller {\n  async install({\n    modules,\n    cwd,\n    fs,\n    packagePath,\n    saveDev = true,\n  }: InstallerOptions): Promise<void> {\n    // npm doesn't auto-create a package.json when installing,\n    // so create an empty one if needed.\n    if (packagePath == null) {\n      await fs.writeFile(path.join(cwd, 'package.json'), '{}');\n    }\n\n    let args = ['install', '--json', saveDev ? '--save-dev' : '--save'].concat(\n      modules.map(npmSpecifierFromModuleRequest),\n    );\n\n    // When Parcel is run by npm (e.g. via package.json scripts), several environment variables are\n    // added. When parcel in turn calls npm again, these can cause npm to behave stragely, so we\n    // filter them out when installing packages.\n    let env = {};\n    for (let key in process.env) {\n      if (!key.startsWith('npm_') && key !== 'INIT_CWD' && key !== 'NODE_ENV') {\n        env[key] = process.env[key];\n      }\n    }\n\n    let installProcess = spawn(NPM_CMD, args, {cwd, env});\n    let stdout = '';\n    installProcess.stdout.on('data', (buf: Buffer) => {\n      stdout += buf.toString();\n    });\n\n    let stderr = [];\n    installProcess.stderr.on('data', (buf: Buffer) => {\n      stderr.push(buf.toString().trim());\n    });\n\n    try {\n      await promiseFromProcess(installProcess);\n\n      let results: NPMResults = JSON.parse(stdout);\n      let addedCount = results.added.length;\n      if (addedCount > 0) {\n        logger.log({\n          origin: '@parcel/package-manager',\n          message: `Added ${addedCount} packages via npm`,\n        });\n      }\n\n      // Since we succeeded, stderr might have useful information not included\n      // in the json written to stdout. It's also not necessary to log these as\n      // errors as they often aren't.\n      for (let message of stderr) {\n        if (message.length > 0) {\n          logger.log({\n            origin: '@parcel/package-manager',\n            message,\n          });\n        }\n      }\n    } catch (e) {\n      throw new Error(\n        'npm failed to install modules: ' +\n          e.message +\n          ' - ' +\n          stderr.join('\\n'),\n      );\n    }\n  }\n}\n\ntype NPMResults = {|\n  added: Array<{name: string, ...}>,\n|};\n\nregisterSerializableClass(`${pkg.version}:Npm`, Npm);\n", "'use strict';\n\nconst cp = require('child_process');\nconst parse = require('./lib/parse');\nconst enoent = require('./lib/enoent');\n\nfunction spawn(command, args, options) {\n    // Parse the arguments\n    const parsed = parse(command, args, options);\n\n    // Spawn the child process\n    const spawned = cp.spawn(parsed.command, parsed.args, parsed.options);\n\n    // Hook into child process \"exit\" event to emit an error if the command\n    // does not exists, see: https://github.com/IndigoUnited/node-cross-spawn/issues/16\n    enoent.hookChildProcess(spawned, parsed);\n\n    return spawned;\n}\n\nfunction spawnSync(command, args, options) {\n    // Parse the arguments\n    const parsed = parse(command, args, options);\n\n    // Spawn the child process\n    const result = cp.spawnSync(parsed.command, parsed.args, parsed.options);\n\n    // Analyze if the command does not exist, see: https://github.com/IndigoUnited/node-cross-spawn/issues/16\n    result.error = result.error || enoent.verifyENOENTSync(result.status, parsed);\n\n    return result;\n}\n\nmodule.exports = spawn;\nmodule.exports.spawn = spawn;\nmodule.exports.sync = spawnSync;\n\nmodule.exports._parse = parse;\nmodule.exports._enoent = enoent;\n", "'use strict';\n\nconst path = require('path');\nconst resolveCommand = require('./util/resolveCommand');\nconst escape = require('./util/escape');\nconst readShebang = require('./util/readShebang');\n\nconst isWin = process.platform === 'win32';\nconst isExecutableRegExp = /\\.(?:com|exe)$/i;\nconst isCmdShimRegExp = /node_modules[\\\\/].bin[\\\\/][^\\\\/]+\\.cmd$/i;\n\nfunction detectShebang(parsed) {\n    parsed.file = resolveCommand(parsed);\n\n    const shebang = parsed.file && readShebang(parsed.file);\n\n    if (shebang) {\n        parsed.args.unshift(parsed.file);\n        parsed.command = shebang;\n\n        return resolveCommand(parsed);\n    }\n\n    return parsed.file;\n}\n\nfunction parseNonShell(parsed) {\n    if (!isWin) {\n        return parsed;\n    }\n\n    // Detect & add support for shebangs\n    const commandFile = detectShebang(parsed);\n\n    // We don't need a shell if the command filename is an executable\n    const needsShell = !isExecutableRegExp.test(commandFile);\n\n    // If a shell is required, use cmd.exe and take care of escaping everything correctly\n    // Note that `forceShell` is an hidden option used only in tests\n    if (parsed.options.forceShell || needsShell) {\n        // Need to double escape meta chars if the command is a cmd-shim located in `node_modules/.bin/`\n        // The cmd-shim simply calls execute the package bin file with NodeJS, proxying any argument\n        // Because the escape of metachars with ^ gets interpreted when the cmd.exe is first called,\n        // we need to double escape them\n        const needsDoubleEscapeMetaChars = isCmdShimRegExp.test(commandFile);\n\n        // Normalize posix paths into OS compatible paths (e.g.: foo/bar -> foo\\bar)\n        // This is necessary otherwise it will always fail with ENOENT in those cases\n        parsed.command = path.normalize(parsed.command);\n\n        // Escape command & arguments\n        parsed.command = escape.command(parsed.command);\n        parsed.args = parsed.args.map((arg) => escape.argument(arg, needsDoubleEscapeMetaChars));\n\n        const shellCommand = [parsed.command].concat(parsed.args).join(' ');\n\n        parsed.args = ['/d', '/s', '/c', `\"${shellCommand}\"`];\n        parsed.command = process.env.comspec || 'cmd.exe';\n        parsed.options.windowsVerbatimArguments = true; // Tell node's spawn that the arguments are already escaped\n    }\n\n    return parsed;\n}\n\nfunction parse(command, args, options) {\n    // Normalize arguments, similar to nodejs\n    if (args && !Array.isArray(args)) {\n        options = args;\n        args = null;\n    }\n\n    args = args ? args.slice(0) : []; // Clone array to avoid changing the original\n    options = Object.assign({}, options); // Clone object to avoid changing the original\n\n    // Build our parsed object\n    const parsed = {\n        command,\n        args,\n        options,\n        file: undefined,\n        original: {\n            command,\n            args,\n        },\n    };\n\n    // Delegate further parsing to shell or non-shell\n    return options.shell ? parsed : parseNonShell(parsed);\n}\n\nmodule.exports = parse;\n", "'use strict';\n\nconst path = require('path');\nconst which = require('which');\nconst getPathKey = require('path-key');\n\nfunction resolveCommandAttempt(parsed, withoutPathExt) {\n    const env = parsed.options.env || process.env;\n    const cwd = process.cwd();\n    const hasCustomCwd = parsed.options.cwd != null;\n    // Worker threads do not have process.chdir()\n    const shouldSwitchCwd = hasCustomCwd && process.chdir !== undefined && !process.chdir.disabled;\n\n    // If a custom `cwd` was specified, we need to change the process cwd\n    // because `which` will do stat calls but does not support a custom cwd\n    if (shouldSwitchCwd) {\n        try {\n            process.chdir(parsed.options.cwd);\n        } catch (err) {\n            /* Empty */\n        }\n    }\n\n    let resolved;\n\n    try {\n        resolved = which.sync(parsed.command, {\n            path: env[getPathKey({ env })],\n            pathExt: withoutPathExt ? path.delimiter : undefined,\n        });\n    } catch (e) {\n        /* Empty */\n    } finally {\n        if (shouldSwitchCwd) {\n            process.chdir(cwd);\n        }\n    }\n\n    // If we successfully resolved, ensure that an absolute path is returned\n    // Note that when a custom `cwd` was used, we need to resolve to an absolute path based on it\n    if (resolved) {\n        resolved = path.resolve(hasCustomCwd ? parsed.options.cwd : '', resolved);\n    }\n\n    return resolved;\n}\n\nfunction resolveCommand(parsed) {\n    return resolveCommandAttempt(parsed) || resolveCommandAttempt(parsed, true);\n}\n\nmodule.exports = resolveCommand;\n", "const isWindows = process.platform === 'win32' ||\n    process.env.OSTYPE === 'cygwin' ||\n    process.env.OSTYPE === 'msys'\n\nconst path = require('path')\nconst COLON = isWindows ? ';' : ':'\nconst isexe = require('isexe')\n\nconst getNotFoundError = (cmd) =>\n  Object.assign(new Error(`not found: ${cmd}`), { code: 'ENOENT' })\n\nconst getPathInfo = (cmd, opt) => {\n  const colon = opt.colon || COLON\n\n  // If it has a slash, then we don't bother searching the pathenv.\n  // just check the file itself, and that's it.\n  const pathEnv = cmd.match(/\\//) || isWindows && cmd.match(/\\\\/) ? ['']\n    : (\n      [\n        // windows always checks the cwd first\n        ...(isWindows ? [process.cwd()] : []),\n        ...(opt.path || process.env.PATH ||\n          /* istanbul ignore next: very unusual */ '').split(colon),\n      ]\n    )\n  const pathExtExe = isWindows\n    ? opt.pathExt || process.env.PATHEXT || '.EXE;.CMD;.BAT;.COM'\n    : ''\n  const pathExt = isWindows ? pathExtExe.split(colon) : ['']\n\n  if (isWindows) {\n    if (cmd.indexOf('.') !== -1 && pathExt[0] !== '')\n      pathExt.unshift('')\n  }\n\n  return {\n    pathEnv,\n    pathExt,\n    pathExtExe,\n  }\n}\n\nconst which = (cmd, opt, cb) => {\n  if (typeof opt === 'function') {\n    cb = opt\n    opt = {}\n  }\n  if (!opt)\n    opt = {}\n\n  const { pathEnv, pathExt, pathExtExe } = getPathInfo(cmd, opt)\n  const found = []\n\n  const step = i => new Promise((resolve, reject) => {\n    if (i === pathEnv.length)\n      return opt.all && found.length ? resolve(found)\n        : reject(getNotFoundError(cmd))\n\n    const ppRaw = pathEnv[i]\n    const pathPart = /^\".*\"$/.test(ppRaw) ? ppRaw.slice(1, -1) : ppRaw\n\n    const pCmd = path.join(pathPart, cmd)\n    const p = !pathPart && /^\\.[\\\\\\/]/.test(cmd) ? cmd.slice(0, 2) + pCmd\n      : pCmd\n\n    resolve(subStep(p, i, 0))\n  })\n\n  const subStep = (p, i, ii) => new Promise((resolve, reject) => {\n    if (ii === pathExt.length)\n      return resolve(step(i + 1))\n    const ext = pathExt[ii]\n    isexe(p + ext, { pathExt: pathExtExe }, (er, is) => {\n      if (!er && is) {\n        if (opt.all)\n          found.push(p + ext)\n        else\n          return resolve(p + ext)\n      }\n      return resolve(subStep(p, i, ii + 1))\n    })\n  })\n\n  return cb ? step(0).then(res => cb(null, res), cb) : step(0)\n}\n\nconst whichSync = (cmd, opt) => {\n  opt = opt || {}\n\n  const { pathEnv, pathExt, pathExtExe } = getPathInfo(cmd, opt)\n  const found = []\n\n  for (let i = 0; i < pathEnv.length; i ++) {\n    const ppRaw = pathEnv[i]\n    const pathPart = /^\".*\"$/.test(ppRaw) ? ppRaw.slice(1, -1) : ppRaw\n\n    const pCmd = path.join(pathPart, cmd)\n    const p = !pathPart && /^\\.[\\\\\\/]/.test(cmd) ? cmd.slice(0, 2) + pCmd\n      : pCmd\n\n    for (let j = 0; j < pathExt.length; j ++) {\n      const cur = p + pathExt[j]\n      try {\n        const is = isexe.sync(cur, { pathExt: pathExtExe })\n        if (is) {\n          if (opt.all)\n            found.push(cur)\n          else\n            return cur\n        }\n      } catch (ex) {}\n    }\n  }\n\n  if (opt.all && found.length)\n    return found\n\n  if (opt.nothrow)\n    return null\n\n  throw getNotFoundError(cmd)\n}\n\nmodule.exports = which\nwhich.sync = whichSync\n", "var fs = require('fs')\nvar core\nif (process.platform === 'win32' || global.TESTING_WINDOWS) {\n  core = require('./windows.js')\n} else {\n  core = require('./mode.js')\n}\n\nmodule.exports = isexe\nisexe.sync = sync\n\nfunction isexe (path, options, cb) {\n  if (typeof options === 'function') {\n    cb = options\n    options = {}\n  }\n\n  if (!cb) {\n    if (typeof Promise !== 'function') {\n      throw new TypeError('callback not provided')\n    }\n\n    return new Promise(function (resolve, reject) {\n      isexe(path, options || {}, function (er, is) {\n        if (er) {\n          reject(er)\n        } else {\n          resolve(is)\n        }\n      })\n    })\n  }\n\n  core(path, options || {}, function (er, is) {\n    // ignore EACCES because that just means we aren't allowed to run it\n    if (er) {\n      if (er.code === 'EACCES' || options && options.ignoreErrors) {\n        er = null\n        is = false\n      }\n    }\n    cb(er, is)\n  })\n}\n\nfunction sync (path, options) {\n  // my kingdom for a filtered catch\n  try {\n    return core.sync(path, options || {})\n  } catch (er) {\n    if (options && options.ignoreErrors || er.code === 'EACCES') {\n      return false\n    } else {\n      throw er\n    }\n  }\n}\n", "'use strict';\n\nconst pathKey = (options = {}) => {\n\tconst environment = options.env || process.env;\n\tconst platform = options.platform || process.platform;\n\n\tif (platform !== 'win32') {\n\t\treturn 'PATH';\n\t}\n\n\treturn Object.keys(environment).reverse().find(key => key.toUpperCase() === 'PATH') || 'Path';\n};\n\nmodule.exports = pathKey;\n// TODO: Remove this for the next major release\nmodule.exports.default = pathKey;\n", "'use strict';\n\n// See http://www.robvanderwoude.com/escapechars.php\nconst metaCharsRegExp = /([()\\][%!^\"`<>&|;, *?])/g;\n\nfunction escapeCommand(arg) {\n    // Escape meta chars\n    arg = arg.replace(metaCharsRegExp, '^$1');\n\n    return arg;\n}\n\nfunction escapeArgument(arg, doubleEscapeMetaChars) {\n    // Convert to string\n    arg = `${arg}`;\n\n    // Algorithm below is based on https://qntm.org/cmd\n    // It's slightly altered to disable JS backtracking to avoid hanging on specially crafted input\n    // Please see https://github.com/moxystudio/node-cross-spawn/pull/160 for more information\n\n    // Sequence of backslashes followed by a double quote:\n    // double up all the backslashes and escape the double quote\n    arg = arg.replace(/(?=(\\\\+?)?)\\1\"/g, '$1$1\\\\\"');\n\n    // Sequence of backslashes followed by the end of the string\n    // (which will become a double quote later):\n    // double up all the backslashes\n    arg = arg.replace(/(?=(\\\\+?)?)\\1$/, '$1$1');\n\n    // All other backslashes occur literally\n\n    // Quote the whole thing:\n    arg = `\"${arg}\"`;\n\n    // Escape meta chars\n    arg = arg.replace(metaCharsRegExp, '^$1');\n\n    // Double escape meta chars if necessary\n    if (doubleEscapeMetaChars) {\n        arg = arg.replace(metaCharsRegExp, '^$1');\n    }\n\n    return arg;\n}\n\nmodule.exports.command = escapeCommand;\nmodule.exports.argument = escapeArgument;\n", "'use strict';\n\nconst fs = require('fs');\nconst shebangCommand = require('shebang-command');\n\nfunction readShebang(command) {\n    // Read the first 150 bytes from the file\n    const size = 150;\n    const buffer = Buffer.alloc(size);\n\n    let fd;\n\n    try {\n        fd = fs.openSync(command, 'r');\n        fs.readSync(fd, buffer, 0, size, 0);\n        fs.closeSync(fd);\n    } catch (e) { /* Empty */ }\n\n    // Attempt to extract shebang (null is returned if not a shebang)\n    return shebangCommand(buffer.toString());\n}\n\nmodule.exports = readShebang;\n", "'use strict';\nconst shebangRegex = require('shebang-regex');\n\nmodule.exports = (string = '') => {\n\tconst match = string.match(shebangRegex);\n\n\tif (!match) {\n\t\treturn null;\n\t}\n\n\tconst [path, argument] = match[0].replace(/#! ?/, '').split(' ');\n\tconst binary = path.split('/').pop();\n\n\tif (binary === 'env') {\n\t\treturn argument;\n\t}\n\n\treturn argument ? `${binary} ${argument}` : binary;\n};\n", "'use strict';\nmodule.exports = /^#!(.*)/;\n", "'use strict';\n\nconst isWin = process.platform === 'win32';\n\nfunction notFoundError(original, syscall) {\n    return Object.assign(new Error(`${syscall} ${original.command} ENOENT`), {\n        code: 'ENOENT',\n        errno: 'ENOENT',\n        syscall: `${syscall} ${original.command}`,\n        path: original.command,\n        spawnargs: original.args,\n    });\n}\n\nfunction hookChildProcess(cp, parsed) {\n    if (!isWin) {\n        return;\n    }\n\n    const originalEmit = cp.emit;\n\n    cp.emit = function (name, arg1) {\n        // If emitting \"exit\" event and exit code is 1, we need to check if\n        // the command exists and emit an \"error\" instead\n        // See https://github.com/IndigoUnited/node-cross-spawn/issues/16\n        if (name === 'exit') {\n            const err = verifyENOENT(arg1, parsed);\n\n            if (err) {\n                return originalEmit.call(cp, 'error', err);\n            }\n        }\n\n        return originalEmit.apply(cp, arguments); // eslint-disable-line prefer-rest-params\n    };\n}\n\nfunction verifyENOENT(status, parsed) {\n    if (isWin && status === 1 && !parsed.file) {\n        return notFoundError(parsed.original, 'spawn');\n    }\n\n    return null;\n}\n\nfunction verifyENOENTSync(status, parsed) {\n    if (isWin && status === 1 && !parsed.file) {\n        return notFoundError(parsed.original, 'spawnSync');\n    }\n\n    return null;\n}\n\nmodule.exports = {\n    hookChildProcess,\n    verifyENOENT,\n    verifyENOENTSync,\n    notFoundError,\n};\n", "// @flow strict-local\n\nimport type {ChildProcess} from 'child_process';\n\nexport default function promiseFromProcess(\n  childProcess: ChildProcess,\n): Promise<void> {\n  return new Promise((resolve, reject) => {\n    childProcess.on('error', reject);\n    childProcess.on('close', code => {\n      if (code !== 0) {\n        reject(new Error('Child process failed'));\n        return;\n      }\n\n      resolve();\n    });\n  });\n}\n", "{\n  \"name\": \"@parcel/package-manager\",\n  \"version\": \"2.15.2\",\n  \"description\": \"Blazing fast, zero configuration web application bundler\",\n  \"license\": \"MIT\",\n  \"publishConfig\": {\n    \"access\": \"public\"\n  },\n  \"funding\": {\n    \"type\": \"opencollective\",\n    \"url\": \"https://opencollective.com/parcel\"\n  },\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/parcel-bundler/parcel.git\"\n  },\n  \"main\": \"lib/index.js\",\n  \"source\": \"src/index.js\",\n  \"types\": \"index.d.ts\",\n  \"engines\": {\n    \"node\": \">= 16.0.0\"\n  },\n  \"scripts\": {\n    \"build-ts\": \"mkdir -p lib && flow-to-ts src/index.js > lib/index.d.ts\",\n    \"check-ts\": \"tsc --noEmit index.d.ts\",\n    \"test\": \"mocha test\"\n  },\n  \"targets\": {\n    \"types\": false,\n    \"main\": {\n      \"includeNodeModules\": {\n        \"@parcel/core\": false,\n        \"@parcel/diagnostic\": false,\n        \"@parcel/fs\": false,\n        \"@parcel/logger\": false,\n        \"@parcel/node-resolver-core\": false,\n        \"@parcel/types\": false,\n        \"@parcel/utils\": false,\n        \"@parcel/workers\": false,\n        \"@swc/core\": false,\n        \"semver\": false\n      }\n    }\n  },\n  \"dependencies\": {\n    \"@parcel/diagnostic\": \"2.15.2\",\n    \"@parcel/fs\": \"2.15.2\",\n    \"@parcel/logger\": \"2.15.2\",\n    \"@parcel/node-resolver-core\": \"3.6.2\",\n    \"@parcel/types\": \"2.15.2\",\n    \"@parcel/utils\": \"2.15.2\",\n    \"@parcel/workers\": \"2.15.2\",\n    \"@swc/core\": \"^1.11.24\",\n    \"semver\": \"^7.7.1\"\n  },\n  \"devDependencies\": {\n    \"command-exists\": \"^1.2.9\",\n    \"cross-spawn\": \"^7.0.6\",\n    \"nullthrows\": \"^1.1.1\",\n    \"split2\": \"^4.2.0\"\n  },\n  \"peerDependencies\": {\n    \"@parcel/core\": \"^2.15.2\"\n  },\n  \"browser\": {\n    \"./src/NodePackageManager.js\": false,\n    \"./src/Npm.js\": false,\n    \"./src/Pnpm.js\": false,\n    \"./src/Yarn.js\": false\n  },\n  \"gitHead\": \"b66f37168d0e830c030d0427bceac90117674cae\"\n}\n", "// @flow strict-local\n\nimport type {PackageInstaller, InstallerOptions} from '@parcel/types';\n\nimport commandExists from 'command-exists';\nimport spawn from 'cross-spawn';\nimport logger from '@parcel/logger';\nimport split from 'split2';\nimport JSONParseStream from './JSONParseStream';\nimport promiseFromProcess from './promiseFromProcess';\nimport {registerSerializableClass} from '@parcel/core';\nimport {exec, npmSpecifierFromModuleRequest} from './utils';\n\n// $FlowFixMe\nimport pkg from '../package.json';\n\nconst YARN_CMD = 'yarn';\n\ntype YarnStdOutMessage =\n  | {|\n      +type: 'step',\n      data: {|\n        message: string,\n        current: number,\n        total: number,\n      |},\n    |}\n  | {|+type: 'success', data: string|}\n  | {|+type: 'info', data: string|}\n  | {|+type: 'tree' | 'progressStart' | 'progressTick'|};\n\ntype YarnStdErrMessage = {|\n  +type: 'error' | 'warning',\n  data: string,\n|};\n\nlet hasYarn: ?boolean;\nlet yarnVersion: ?number;\n\nexport class Yarn implements PackageInstaller {\n  static async exists(): Promise<boolean> {\n    if (hasYarn != null) {\n      return hasYarn;\n    }\n\n    try {\n      hasYarn = Boolean(await commandExists('yarn'));\n    } catch (err) {\n      hasYarn = false;\n    }\n\n    return hasYarn;\n  }\n\n  async install({\n    modules,\n    cwd,\n    saveDev = true,\n  }: InstallerOptions): Promise<void> {\n    if (yarnVersion == null) {\n      let version = await exec('yarn --version');\n      yarnVersion = parseInt(version.stdout, 10);\n    }\n\n    let args = ['add', '--json'].concat(\n      modules.map(npmSpecifierFromModuleRequest),\n    );\n\n    if (saveDev) {\n      args.push('-D');\n      if (yarnVersion < 2) {\n        args.push('-W');\n      }\n    }\n\n    // When Parcel is run by Yarn (e.g. via package.json scripts), several environment variables are\n    // added. When parcel in turn calls Yarn again, these can cause Yarn to behave stragely, so we\n    // filter them out when installing packages.\n    let env = {};\n    for (let key in process.env) {\n      if (\n        !key.startsWith('npm_') &&\n        key !== 'YARN_WRAP_OUTPUT' &&\n        key !== 'INIT_CWD' &&\n        key !== 'NODE_ENV'\n      ) {\n        env[key] = process.env[key];\n      }\n    }\n\n    let installProcess = spawn(YARN_CMD, args, {cwd, env});\n    installProcess.stdout\n      // Invoking yarn with --json provides streaming, newline-delimited JSON output.\n      .pipe(split())\n      .pipe(new JSONParseStream())\n      .on('error', e => {\n        logger.error(e, '@parcel/package-manager');\n      })\n      .on('data', (message: YarnStdOutMessage) => {\n        switch (message.type) {\n          case 'step':\n            logger.progress(\n              prefix(\n                `[${message.data.current}/${message.data.total}] ${message.data.message}`,\n              ),\n            );\n            return;\n          case 'success':\n          case 'info':\n            logger.info({\n              origin: '@parcel/package-manager',\n              message: prefix(message.data),\n            });\n            return;\n          default:\n          // ignore\n        }\n      });\n\n    installProcess.stderr\n      .pipe(split())\n      .pipe(new JSONParseStream())\n      .on('error', e => {\n        logger.error(e, '@parcel/package-manager');\n      })\n      .on('data', (message: YarnStdErrMessage) => {\n        switch (message.type) {\n          case 'warning':\n            logger.warn({\n              origin: '@parcel/package-manager',\n              message: prefix(message.data),\n            });\n            return;\n          case 'error':\n            logger.error({\n              origin: '@parcel/package-manager',\n              message: prefix(message.data),\n            });\n            return;\n          default:\n          // ignore\n        }\n      });\n\n    try {\n      return await promiseFromProcess(installProcess);\n    } catch (e) {\n      throw new Error('Yarn failed to install modules:' + e.message);\n    }\n  }\n}\n\nfunction prefix(message: string): string {\n  return 'yarn: ' + message;\n}\n\nregisterSerializableClass(`${pkg.version}:Yarn`, Yarn);\n", "module.exports = require('./lib/command-exists');\n", "/*\nCopyright (c) 2014-2021, <PERSON> <<EMAIL>>\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted, provided that the above\ncopyright notice and this permission notice appear in all copies.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES\nWITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF\nMERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR\nANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES\nWHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN\nACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF OR\nIN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.\n*/\n\n'use strict'\n\nconst { Transform } = require('stream')\nconst { StringDecoder } = require('string_decoder')\nconst kLast = Symbol('last')\nconst kDecoder = Symbol('decoder')\n\nfunction transform (chunk, enc, cb) {\n  let list\n  if (this.overflow) { // Line buffer is full. Skip to start of next line.\n    const buf = this[kDecoder].write(chunk)\n    list = buf.split(this.matcher)\n\n    if (list.length === 1) return cb() // Line ending not found. Discard entire chunk.\n\n    // Line ending found. Discard trailing fragment of previous line and reset overflow state.\n    list.shift()\n    this.overflow = false\n  } else {\n    this[kLast] += this[kDecoder].write(chunk)\n    list = this[kLast].split(this.matcher)\n  }\n\n  this[kLast] = list.pop()\n\n  for (let i = 0; i < list.length; i++) {\n    try {\n      push(this, this.mapper(list[i]))\n    } catch (error) {\n      return cb(error)\n    }\n  }\n\n  this.overflow = this[kLast].length > this.maxLength\n  if (this.overflow && !this.skipOverflow) {\n    cb(new Error('maximum buffer reached'))\n    return\n  }\n\n  cb()\n}\n\nfunction flush (cb) {\n  // forward any gibberish left in there\n  this[kLast] += this[kDecoder].end()\n\n  if (this[kLast]) {\n    try {\n      push(this, this.mapper(this[kLast]))\n    } catch (error) {\n      return cb(error)\n    }\n  }\n\n  cb()\n}\n\nfunction push (self, val) {\n  if (val !== undefined) {\n    self.push(val)\n  }\n}\n\nfunction noop (incoming) {\n  return incoming\n}\n\nfunction split (matcher, mapper, options) {\n  // Set defaults for any arguments not supplied.\n  matcher = matcher || /\\r?\\n/\n  mapper = mapper || noop\n  options = options || {}\n\n  // Test arguments explicitly.\n  switch (arguments.length) {\n    case 1:\n      // If mapper is only argument.\n      if (typeof matcher === 'function') {\n        mapper = matcher\n        matcher = /\\r?\\n/\n      // If options is only argument.\n      } else if (typeof matcher === 'object' && !(matcher instanceof RegExp) && !matcher[Symbol.split]) {\n        options = matcher\n        matcher = /\\r?\\n/\n      }\n      break\n\n    case 2:\n      // If mapper and options are arguments.\n      if (typeof matcher === 'function') {\n        options = mapper\n        mapper = matcher\n        matcher = /\\r?\\n/\n      // If matcher and options are arguments.\n      } else if (typeof mapper === 'object') {\n        options = mapper\n        mapper = noop\n      }\n  }\n\n  options = Object.assign({}, options)\n  options.autoDestroy = true\n  options.transform = transform\n  options.flush = flush\n  options.readableObjectMode = true\n\n  const stream = new Transform(options)\n\n  stream[kLast] = ''\n  stream[kDecoder] = new StringDecoder('utf8')\n  stream.matcher = matcher\n  stream.mapper = mapper\n  stream.maxLength = options.maxLength\n  stream.skipOverflow = options.skipOverflow || false\n  stream.overflow = false\n  stream._destroy = function (err, cb) {\n    // Weird Node v12 bug that we need to work around\n    this._writableState.errorEmitted = false\n    cb(err)\n  }\n\n  return stream\n}\n\nmodule.exports = split\n", "// @flow strict-local\n\nimport type {JSONObject} from '@parcel/types';\n\nimport logger from '@parcel/logger';\nimport {Transform} from 'stream';\n\n// Transforms chunks of json strings to parsed objects.\n// Pair with split2 to parse stream of newline-delimited text.\nexport default class JSONParseStream extends Transform {\n  constructor(options: mixed) {\n    super({...options, objectMode: true});\n  }\n\n  // $FlowFixMe We are in object mode, so we emit objects, not strings\n  _transform(\n    chunk: Buffer | string,\n    encoding: string,\n    callback: (err: ?Error, parsed: ?JSONObject) => mixed,\n  ) {\n    try {\n      let parsed;\n      try {\n        parsed = JSON.parse(chunk.toString());\n      } catch (e) {\n        // Be permissive and ignoreJSON parse errors in case there was\n        // a non-JSON line in the package manager's stdout.\n        logger.verbose({\n          message: 'Ignored invalid JSON message: ' + chunk.toString(),\n          origin: '@parcel/package-manager',\n        });\n        return;\n      }\n      callback(null, parsed);\n    } catch (err) {\n      callback(err);\n    }\n  }\n}\n", "// @flow strict-local\n\nimport type {PackageInstaller, InstallerOptions} from '@parcel/types';\n\nimport path from 'path';\nimport fs from 'fs';\nimport commandExists from 'command-exists';\nimport spawn from 'cross-spawn';\nimport logger from '@parcel/logger';\nimport split from 'split2';\nimport JSONParseStream from './JSONParseStream';\nimport promiseFromProcess from './promiseFromProcess';\nimport {registerSerializableClass} from '@parcel/core';\nimport {exec, npmSpecifierFromModuleRequest} from './utils';\n\n// $FlowFixMe\nimport pkg from '../package.json';\n\nconst PNPM_CMD = 'pnpm';\n\ntype LogLevel = 'error' | 'warn' | 'info' | 'debug';\n\ntype ErrorLog = {|\n  err: {|\n    message: string,\n    code: string,\n    stack: string,\n  |},\n|};\n\ntype PNPMLog =\n  | {|\n      +name: 'pnpm:progress',\n      packageId: string,\n      status: 'fetched' | 'found_in_store' | 'resolved',\n    |}\n  | {|\n      +name: 'pnpm:root',\n      added?: {|\n        id?: string,\n        name: string,\n        realName: string,\n        version?: string,\n        dependencyType?: 'prod' | 'dev' | 'optional',\n        latest?: string,\n        linkedFrom?: string,\n      |},\n      removed?: {|\n        name: string,\n        version?: string,\n        dependencyType?: 'prod' | 'dev' | 'optional',\n      |},\n    |}\n  | {|+name: 'pnpm:importing', from: string, method: string, to: string|}\n  | {|+name: 'pnpm:link', target: string, link: string|}\n  | {|+name: 'pnpm:stats', prefix: string, removed?: number, added?: number|};\n\ntype PNPMResults = {|\n  level: LogLevel,\n  prefix?: string,\n  message?: string,\n  ...ErrorLog,\n  ...PNPMLog,\n|};\n\nlet hasPnpm: ?boolean;\nlet pnpmVersion: ?number;\n\nexport class Pnpm implements PackageInstaller {\n  static async exists(): Promise<boolean> {\n    if (hasPnpm != null) {\n      return hasPnpm;\n    }\n\n    try {\n      hasPnpm = Boolean(await commandExists('pnpm'));\n    } catch (err) {\n      hasPnpm = false;\n    }\n\n    return hasPnpm;\n  }\n\n  async install({\n    modules,\n    cwd,\n    saveDev = true,\n  }: InstallerOptions): Promise<void> {\n    if (pnpmVersion == null) {\n      let version = await exec('pnpm --version');\n      pnpmVersion = parseInt(version.stdout, 10);\n    }\n\n    let args = ['add', '--reporter', 'ndjson'];\n    if (saveDev) {\n      args.push('-D');\n    }\n    if (pnpmVersion >= 7) {\n      if (fs.existsSync(path.join(cwd, 'pnpm-workspace.yaml'))) {\n        // installs in workspace root (regardless of cwd)\n        args.push('-w');\n      }\n    } else {\n      // ignores workspace root check\n      args.push('-W');\n    }\n    args = args.concat(modules.map(npmSpecifierFromModuleRequest));\n\n    let env = {};\n    for (let key in process.env) {\n      if (!key.startsWith('npm_') && key !== 'INIT_CWD' && key !== 'NODE_ENV') {\n        env[key] = process.env[key];\n      }\n    }\n\n    let addedCount = 0,\n      removedCount = 0;\n\n    let installProcess = spawn(PNPM_CMD, args, {\n      cwd,\n      env,\n    });\n    installProcess.stdout\n      .pipe(split())\n      .pipe(new JSONParseStream())\n      .on('error', e => {\n        logger.warn({\n          origin: '@parcel/package-manager',\n          message: e.chunk,\n          stack: e.stack,\n        });\n      })\n      .on('data', (json: PNPMResults) => {\n        if (json.level === 'error') {\n          logger.error({\n            origin: '@parcel/package-manager',\n            message: json.err.message,\n            stack: json.err.stack,\n          });\n        } else if (json.level === 'info' && typeof json.message === 'string') {\n          logger.info({\n            origin: '@parcel/package-manager',\n            message: prefix(json.message),\n          });\n        } else if (json.name === 'pnpm:stats') {\n          addedCount += json.added ?? 0;\n          removedCount += json.removed ?? 0;\n        }\n      });\n\n    let stderr = [];\n    installProcess.stderr\n      .on('data', str => {\n        stderr.push(str.toString());\n      })\n      .on('error', e => {\n        logger.warn({\n          origin: '@parcel/package-manager',\n          message: e.message,\n        });\n      });\n\n    try {\n      await promiseFromProcess(installProcess);\n\n      if (addedCount > 0 || removedCount > 0) {\n        logger.log({\n          origin: '@parcel/package-manager',\n          message: `Added ${addedCount} ${\n            removedCount > 0 ? `and removed ${removedCount} ` : ''\n          }packages via pnpm`,\n        });\n      }\n\n      // Since we succeeded, stderr might have useful information not included\n      // in the json written to stdout. It's also not necessary to log these as\n      // errors as they often aren't.\n      for (let message of stderr) {\n        logger.log({\n          origin: '@parcel/package-manager',\n          message,\n        });\n      }\n    } catch (e) {\n      throw new Error('pnpm failed to install modules');\n    }\n  }\n}\n\nfunction prefix(message: string): string {\n  return 'pnpm: ' + message;\n}\n\nregisterSerializableClass(`${pkg.version}:Pnpm`, Pnpm);\n", "// @flow\n\nexport default function getCurrentPackageManager(\n  userAgent: ?string = process.env.npm_config_user_agent,\n): ?{|name: string, version: string|} {\n  if (!userAgent) {\n    return undefined;\n  }\n\n  const pmSpec = userAgent.split(' ')[0];\n  const separatorPos = pmSpec.lastIndexOf('/');\n  const name = pmSpec.substring(0, separatorPos);\n  return {\n    name: name,\n    version: pmSpec.substring(separatorPos + 1),\n  };\n}\n", "// @flow\n\nconst MODULE_REGEX = /^((@[^/\\s]+\\/){0,1}([^/\\s.~]+[^/\\s]*)){1}(@[^/\\s]+){0,1}/;\n\nexport default function validateModuleSpecifier(moduleName: string): string {\n  let matches = MODULE_REGEX.exec(moduleName);\n  if (matches) {\n    return matches[0];\n  }\n\n  return '';\n}\n", "// @flow\n\nimport type {PackageManagerResolveResult} from '@parcel/types';\n\nexport type {\n  PackageManager,\n  Invalidations,\n  PackageInstaller,\n  ModuleRequest,\n} from '@parcel/types';\nexport * from './Npm';\nexport * from './Pnpm';\nexport * from './Yarn';\nexport * from './MockPackageInstaller';\nexport * from './NodePackageManager';\nexport {_addToInstallQueue} from './installPackage';\n\nexport type {PackageManagerResolveResult};\nexport type {PackageManagerResolveResult as ResolveResult};\n", "// @flow\n\nimport type {\n  ModuleRequest,\n  PackageInstaller,\n  InstallerOptions,\n} from '@parcel/types';\nimport type {FileSystem} from '@parcel/fs';\nimport type {FilePath} from '@parcel/types';\n\nimport path from 'path';\nimport {ncp} from '@parcel/fs';\nimport {registerSerializableClass} from '@parcel/core';\nimport pkg from '../package.json';\nimport {moduleRequestsFromDependencyMap} from './utils';\n\ntype Package = {|\n  fs: FileSystem,\n  packagePath: FilePath,\n|};\n\n// This PackageInstaller implementation simply copies files from one filesystem to another.\n// Mostly useful for testing purposes.\nexport class MockPackageInstaller implements PackageInstaller {\n  packages: Map<string, Package> = new Map<string, Package>();\n\n  register(packageName: string, fs: FileSystem, packagePath: FilePath) {\n    this.packages.set(packageName, {fs, packagePath});\n  }\n\n  async install({\n    modules,\n    fs,\n    cwd,\n    packagePath,\n    saveDev = true,\n  }: InstallerOptions): Promise<void> {\n    if (packagePath == null) {\n      packagePath = path.join(cwd, 'package.json');\n      await fs.writeFile(packagePath, '{}');\n    }\n\n    let pkg = JSON.parse(await fs.readFile(packagePath, 'utf8'));\n    let key = saveDev ? 'devDependencies' : 'dependencies';\n\n    if (!pkg[key]) {\n      pkg[key] = {};\n    }\n\n    for (let module of modules) {\n      pkg[key][module.name] =\n        '^' + (await this.installPackage(module, fs, packagePath));\n    }\n\n    await fs.writeFile(packagePath, JSON.stringify(pkg));\n  }\n\n  async installPackage(\n    moduleRequest: ModuleRequest,\n    fs: FileSystem,\n    packagePath: FilePath,\n  ): Promise<any> {\n    let pkg = this.packages.get(moduleRequest.name);\n    if (!pkg) {\n      throw new Error('Unknown package ' + moduleRequest.name);\n    }\n\n    let dest = path.join(\n      path.dirname(packagePath),\n      'node_modules',\n      moduleRequest.name,\n    );\n    await ncp(pkg.fs, pkg.packagePath, fs, dest);\n\n    let packageJSON = JSON.parse(\n      await fs.readFile(path.join(dest, 'package.json'), 'utf8'),\n    );\n\n    if (packageJSON.dependencies != null) {\n      for (let dep of moduleRequestsFromDependencyMap(\n        packageJSON.dependencies,\n      )) {\n        await this.installPackage(dep, fs, packagePath);\n      }\n    }\n\n    return packageJSON.version;\n  }\n}\n\nregisterSerializableClass(\n  `${pkg.version}:MockPackageInstaller`,\n  MockPackageInstaller,\n);\n"], "names": ["registerSerializableClass", "ThrowableDiagnostic", "encodeJSONKeyComponent", "escapeMarkdown", "generateJSONCodeHighlights", "md", "NodeFS", "nativeFS", "<PERSON><PERSON><PERSON>", "path", "semver", "logger", "getModuleParts", "getConflictingLocalDependencies", "installPackage", "pkg", "ResolverBase", "pathToFileURL", "transformSync", "MAIN", "SOURCE", "NODE_CONDITION", "SOURCE_CONDITION", "ENTRIES", "CONDITIONS", "NODE_MODULES", "sep", "IS_FILE", "IS_DIR", "IS_SYMLINK", "cache", "Map", "children", "invalidationsCache", "NodePackageManager", "constructor", "fs", "projectRoot", "installer", "currentExtensions", "Object", "keys", "_extensions", "map", "e", "substring", "_createResolver", "process", "versions", "pnp", "undefined", "read", "readFileSync", "kind", "flags", "stat", "lstatSync", "isSymbolicLink", "statSync", "isFile", "isDirectory", "err", "readLink", "readlinkSync", "mode", "entries", "conditions", "packageExports", "moduleDirResolver", "module", "from", "findPnpApi", "dirname", "resolveToUnqualified", "extensions", "typescript", "deserialize", "opts", "serialize", "$$raw", "require", "name", "resolved", "type", "resolve", "warn", "message", "origin", "codeFrames", "filePath", "codeHighlights", "platform", "isAbsolute", "load", "requireSync", "resolveSync", "cachedModule", "_cache", "exports", "m", "parent", "length", "resolver", "id", "filename", "encoding", "includes", "extname", "compile", "_compile", "code", "out", "ignoreDynamic", "call", "options", "basedir", "key", "get", "resolveInternal", "shouldAutoInstall", "startsWith", "diagnostic", "hints", "conflicts", "invalidate", "install", "range", "saveDev", "fields", "field", "language", "json", "satisfies", "version", "set", "clear", "module<PERSON><PERSON><PERSON><PERSON>", "Set", "add", "modules", "packageInstaller", "getInvalidations", "cached", "res", "invalidateOnFileCreate", "invalidateOnFileChange", "invalidateOnStartup", "seen", "<PERSON><PERSON><PERSON>", "has", "push", "file", "specifier", "invalidations", "for<PERSON>ach", "i", "relative", "delete", "specifierType", "error", "Error", "resolution", "self", "value", "moduleType", "pkgPath", "findAncestorFile", "JSON", "parse", "scheme", "invariant", "resolveConfig", "exec", "_exec", "promisify", "npmSpecifierFromModuleRequest", "moduleRequest", "join", "moduleRequestsFromDependencyMap", "dependencyMap", "local", "pkgStr", "readFile", "nullthrows", "loadConfig", "PromiseQueue", "WorkerFarm", "Npm", "Yarn", "Pnpm", "getCurrentPackageManager", "validateModuleSpecifier", "packageManager", "installPeers", "moduleNames", "progress", "fromPkgPath", "cwd", "determinePackageInstaller", "packagePath", "Promise", "all", "installPeerDependencies", "modulePkg", "config", "peers", "peerDependencies", "assign", "filepath", "configFile", "config<PERSON><PERSON>", "basename", "currentPackageManager", "exists", "queue", "maxConcurrent", "modulesInstalling", "_addToInstallQueue", "request", "modulesToInstall", "filter", "getModuleRequestKey", "then", "run", "isWorker", "workerApi", "getWorkerApi", "bundlePath", "env", "PARCEL_SELF_BUILD", "__dirname", "__filename", "callMaster", "location", "args", "method", "spawn", "promiseFromProcess", "NPM_CMD", "writeFile", "concat", "installProcess", "stdout", "on", "buf", "toString", "stderr", "trim", "results", "addedCount", "added", "log", "childProcess", "reject", "commandExists", "split", "JSONParseStream", "YARN_CMD", "<PERSON><PERSON><PERSON><PERSON>", "yarnVersion", "Boolean", "parseInt", "pipe", "prefix", "data", "current", "total", "info", "Transform", "objectMode", "_transform", "chunk", "callback", "parsed", "verbose", "PNPM_CMD", "hasPnpm", "pnpmVersion", "existsSync", "removedCount", "stack", "level", "removed", "str", "userAgent", "npm_config_user_agent", "pmSpec", "separatorPos", "lastIndexOf", "MODULE_REGEX", "moduleName", "matches", "ncp", "MockPackageInstaller", "packages", "register", "packageName", "stringify", "dest", "packageJSON", "dependencies", "dep"], "version": 3, "file": "index.js.map", "sourceRoot": "../../../../"}