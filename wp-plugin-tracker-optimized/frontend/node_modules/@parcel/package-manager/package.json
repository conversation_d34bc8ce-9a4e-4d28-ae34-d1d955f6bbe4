{"name": "@parcel/package-manager", "version": "2.15.2", "description": "Blazing fast, zero configuration web application bundler", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/index.js", "source": "src/index.js", "types": "index.d.ts", "engines": {"node": ">= 16.0.0"}, "scripts": {"build-ts": "mkdir -p lib && flow-to-ts src/index.js > lib/index.d.ts", "check-ts": "tsc --noEmit index.d.ts", "test": "mocha test"}, "targets": {"types": false, "main": {"includeNodeModules": {"@parcel/core": false, "@parcel/diagnostic": false, "@parcel/fs": false, "@parcel/logger": false, "@parcel/node-resolver-core": false, "@parcel/types": false, "@parcel/utils": false, "@parcel/workers": false, "@swc/core": false, "semver": false}}}, "dependencies": {"@parcel/diagnostic": "2.15.2", "@parcel/fs": "2.15.2", "@parcel/logger": "2.15.2", "@parcel/node-resolver-core": "3.6.2", "@parcel/types": "2.15.2", "@parcel/utils": "2.15.2", "@parcel/workers": "2.15.2", "@swc/core": "^1.11.24", "semver": "^7.7.1"}, "devDependencies": {"command-exists": "^1.2.9", "cross-spawn": "^7.0.6", "nullthrows": "^1.1.1", "split2": "^4.2.0"}, "peerDependencies": {"@parcel/core": "^2.15.2"}, "browser": {"./src/NodePackageManager.js": false, "./src/Npm.js": false, "./src/Pnpm.js": false, "./src/Yarn.js": false}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}