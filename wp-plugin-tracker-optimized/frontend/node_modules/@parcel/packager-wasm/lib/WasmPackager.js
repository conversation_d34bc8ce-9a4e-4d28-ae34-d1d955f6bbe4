"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
function _assert() {
  const data = _interopRequireDefault(require("assert"));
  _assert = function () {
    return data;
  };
  return data;
}
function _plugin() {
  const data = require("@parcel/plugin");
  _plugin = function () {
    return data;
  };
  return data;
}
var wasmmap = _interopRequireWildcard(require("./wasm-sourcemap"));
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
var _default = exports.default = new (_plugin().Packager)({
  async package({
    bundle,
    getSourceMapReference
  }) {
    let assets = [];
    bundle.traverseAssets(asset => {
      assets.push(asset);
    });
    _assert().default.equal(assets.length, 1, 'Wasm bundles must only contain one asset');
    let [contents, map] = await Promise.all([assets[0].getBuffer(), assets[0].getMap()]);
    let sourcemapReference = await getSourceMapReference(map);
    if (sourcemapReference != null) {
      return {
        contents: Buffer.from(wasmmap.SetSourceMapURL(contents, sourcemapReference, sourcemapReference.includes('HASH_REF_') ?
        // HASH_REF_\w{16} -> \w{8}
        sourcemapReference.length - (9 + 16 - 8) : undefined)),
        map
      };
    } else {
      return {
        contents,
        map
      };
    }
  }
});