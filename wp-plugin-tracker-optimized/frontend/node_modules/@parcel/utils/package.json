{"name": "@parcel/utils", "version": "2.15.2", "description": "Blazing fast, zero configuration web application bundler", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/index.js", "source": "src/index.js", "engines": {"node": ">= 16.0.0"}, "targets": {"main": {"includeNodeModules": {"@parcel/codeframe": false, "@parcel/diagnostic": false, "@parcel/rust": false, "@parcel/logger": false, "@parcel/markdown-ansi": false, "@parcel/source-map": false, "chalk": false}}}, "dependencies": {"@parcel/codeframe": "2.15.2", "@parcel/diagnostic": "2.15.2", "@parcel/logger": "2.15.2", "@parcel/markdown-ansi": "2.15.2", "@parcel/rust": "2.15.2", "@parcel/source-map": "^2.1.1", "chalk": "^4.1.2", "nullthrows": "^1.1.1"}, "devDependencies": {"@iarna/toml": "^2.2.5", "ansi-html-community": "0.0.8", "clone": "^2.1.2", "fast-glob": "^3.3.3", "fastest-levenshtein": "^1.0.16", "is-glob": "^4.0.3", "is-url": "^1.2.4", "json5": "^2.2.3", "lru-cache": "^10.4.3", "micromatch": "^4.0.8", "node-forge": "^1.3.1", "nullthrows": "^1.1.1", "open": "^7.4.2", "snarkdown": "^2.0.0", "strip-ansi": "^6.0.1", "terminal-link": "^2.1.1"}, "browser": {"./src/generateCertificate.js": false, "./src/http-server.js": false, "./src/openInBrowser.js": false, "@parcel/markdown-ansi": false}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}