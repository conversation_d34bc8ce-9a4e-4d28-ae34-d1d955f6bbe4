{"name": "@parcel/resolver-default", "version": "2.15.2", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/DefaultResolver.js", "source": "src/DefaultResolver.js", "engines": {"node": ">= 16.0.0", "parcel": "^2.15.2"}, "dependencies": {"@parcel/node-resolver-core": "3.6.2", "@parcel/plugin": "2.15.2"}, "devDependencies": {"@babel/core": "^7.22.11"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}