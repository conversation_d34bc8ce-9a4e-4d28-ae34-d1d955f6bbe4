{"name": "@parcel/plugin", "version": "2.15.2", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/PluginAPI.js", "source": "src/PluginAPI.js", "types": "src/PluginAPI.d.ts", "engines": {"node": ">= 16.0.0"}, "scripts": {"check-ts": "tsc --noEmit src/PluginAPI.d.ts"}, "dependencies": {"@parcel/types": "2.15.2"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}