[package]
authors = ["<PERSON> <jasper<PERSON><EMAIL>>"]
edition = "2018"
name = "parcel_sourcemap_node"
version = "2.1.1"

[lib]
crate-type = ["cdylib"]

[dependencies]
napi = {version = "1.7.3", features = ["napi4", "serde-json"]}
napi-derive = "1.1.0"
parcel_sourcemap = {path = "../parcel_sourcemap"}
serde = "1"
serde_json = "1"
rkyv = "0.7.38"

[target.'cfg(target_os = "macos")'.dependencies]
jemallocator = {version = "0.3.2", features = ["disable_initial_exec_tls"]}

[build-dependencies]
napi-build = "1.0.2"
