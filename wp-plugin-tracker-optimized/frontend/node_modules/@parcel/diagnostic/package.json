{"name": "@parcel/diagnostic", "version": "2.15.2", "description": "Types and utilities for printing source-code located errors, warning and information messages.", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/diagnostic.js", "source": "src/diagnostic.js", "types": "lib/diagnostic.d.ts", "engines": {"node": ">= 16.0.0"}, "scripts": {"build-ts": "flow-to-ts src/*.js --write && tsc --emitDeclarationOnly --declaration --esModuleInterop src/*.ts && mkdir -p lib && mv src/*.d.ts lib/. && rm src/*.ts", "check-ts": "tsc --noEmit lib/diagnostic.d.ts"}, "dependencies": {"@mischnic/json-sourcemap": "^0.1.1", "nullthrows": "^1.1.1"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}