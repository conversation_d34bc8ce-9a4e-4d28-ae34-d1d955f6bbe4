{"name": "@parcel/feature-flags", "version": "2.15.2", "description": "Provides internal feature-flags for the parcel codebase.", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/index.js", "source": "src/index.js", "types": "lib/types.d.ts", "scripts": {"build-ts": "mkdir -p lib && flow-to-ts src/types.js > lib/types.d.ts", "check-ts": "tsc --noEmit lib/types.d.ts"}, "engines": {"node": ">= 16.0.0"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}