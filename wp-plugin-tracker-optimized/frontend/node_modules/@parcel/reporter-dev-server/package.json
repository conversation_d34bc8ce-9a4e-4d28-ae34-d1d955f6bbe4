{"name": "@parcel/reporter-dev-server", "version": "2.15.2", "description": "Blazing fast, zero configuration web application bundler", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/ServerReporter.js", "source": "src/ServerReporter.js", "engines": {"node": ">= 16.0.0", "parcel": "^2.15.2"}, "targets": {"main": {"includeNodeModules": {"@parcel/codeframe": false, "@parcel/plugin": false, "@parcel/source-map": false, "@parcel/utils": false}}}, "dependencies": {"@parcel/codeframe": "2.15.2", "@parcel/plugin": "2.15.2", "@parcel/source-map": "^2.1.1", "@parcel/utils": "2.15.2"}, "devDependencies": {"@parcel/babel-preset": "2.15.2", "@parcel/diagnostic": "2.15.2", "@parcel/types": "2.15.2", "connect": "^3.7.0", "ejs": "^3.1.10", "fresh": "^0.5.2", "http-proxy-middleware": "^2.0.9", "launch-editor": "^2.10.0", "mime-types": "2.1.18", "nullthrows": "^1.1.1", "serve-handler": "^6.1.6", "ws": "^8.18.2"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}