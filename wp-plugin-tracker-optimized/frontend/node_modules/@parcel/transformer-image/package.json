{"name": "@parcel/transformer-image", "version": "2.15.2", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/ImageTransformer.js", "source": "src/ImageTransformer.js", "engines": {"node": ">= 16.0.0", "parcel": "^2.15.2"}, "dependencies": {"@parcel/plugin": "2.15.2", "@parcel/utils": "2.15.2", "@parcel/workers": "2.15.2", "nullthrows": "^1.1.1"}, "devDependencies": {"sharp": "^0.33.5"}, "peerDependencies": {"@parcel/core": "^2.15.2"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}