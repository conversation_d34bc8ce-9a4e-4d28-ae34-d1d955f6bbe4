"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _validateConfig = require("./validateConfig");
function _plugin() {
  const data = require("@parcel/plugin");
  _plugin = function () {
    return data;
  };
  return data;
}
function _nullthrows() {
  const data = _interopRequireDefault(require("nullthrows"));
  _nullthrows = function () {
    return data;
  };
  return data;
}
function _workers() {
  const data = _interopRequireDefault(require("@parcel/workers"));
  _workers = function () {
    return data;
  };
  return data;
}
var _loadSharp = _interopRequireDefault(require("./loadSharp"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
// from https://github.com/lovell/sharp/blob/df7b8ba73808fc494be413e88cfb621b6279218c/lib/output.js#L6-L17
const FORMATS = new Map([['jpeg', 'jpeg'], ['jpg', 'jpeg'], ['png', 'png'], ['webp', 'webp'], ['gif', 'gif'], ['tiff', 'tiff'], ['avif', 'avif'], ['heic', 'heif'], ['heif', 'heif']]);
let isSharpLoadedOnMainThread = false;
var _default = exports.default = new (_plugin().Transformer)({
  async loadConfig({
    config
  }) {
    let configFile = await config.getConfig(['sharp.config.json'],
    // '.sharprc', '.sharprc.json'
    {
      packageKey: 'sharp'
    });
    if (configFile !== null && configFile !== void 0 && configFile.contents) {
      (0, _validateConfig.validateConfig)(configFile.contents, configFile.filePath);
      return configFile.contents;
    } else {
      return {};
    }
  },
  async transform({
    config,
    asset,
    options
  }) {
    var _asset$query$get;
    asset.bundleBehavior = 'isolated';
    const originalFormat = FORMATS.get(asset.type);
    if (!originalFormat) {
      throw new Error(`The image transformer does not support ${asset.type} images.`);
    }
    const width = asset.query.has('width') ? parseInt(asset.query.get('width'), 10) : null;
    const height = asset.query.has('height') ? parseInt(asset.query.get('height'), 10) : null;
    const quality = asset.query.has('quality') ? parseInt(asset.query.get('quality'), 10) : config.quality;
    let targetFormat = (_asset$query$get = asset.query.get('as')) === null || _asset$query$get === void 0 ? void 0 : _asset$query$get.toLowerCase().trim();
    if (targetFormat && !FORMATS.has(targetFormat)) {
      throw new Error(`The image transformer does not support ${targetFormat} images.`);
    }
    const format = (0, _nullthrows().default)(FORMATS.get(targetFormat || originalFormat));
    const outputOptions = config[format];
    if (width || height || quality || targetFormat || outputOptions) {
      // Sharp must be required from the main thread as well to prevent errors when workers exit
      // See https://sharp.pixelplumbing.com/install#worker-threads and https://github.com/lovell/sharp/issues/2263
      if (_workers().default.isWorker() && !isSharpLoadedOnMainThread) {
        let api = _workers().default.getWorkerApi();
        await api.callMaster({
          location: __dirname + '/loadSharp.js',
          args: [options.packageManager, asset.filePath, options.shouldAutoInstall]
        });
        isSharpLoadedOnMainThread = true;
      }
      let inputBuffer = await asset.getBuffer();
      let sharp = await (0, _loadSharp.default)(options.packageManager, asset.filePath, options.shouldAutoInstall, true);
      let imagePipeline = sharp(inputBuffer, {
        animated: true
      });
      imagePipeline.withMetadata();
      if (width || height) {
        imagePipeline.resize(width, height);
      }
      imagePipeline.rotate();
      const normalizedOutputOptions = outputOptions || {};
      if (format === 'jpeg') {
        normalizedOutputOptions.mozjpeg = normalizedOutputOptions.mozjpeg ?? true;
      }
      imagePipeline[format]({
        quality,
        ...normalizedOutputOptions
      });
      asset.type = format;
      let buffer = await imagePipeline.toBuffer();
      asset.setBuffer(buffer);
    }
    return [asset];
  }
});