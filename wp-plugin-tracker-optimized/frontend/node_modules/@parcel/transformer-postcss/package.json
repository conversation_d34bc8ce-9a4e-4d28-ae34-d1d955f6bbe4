{"name": "@parcel/transformer-postcss", "version": "2.15.2", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/PostCSSTransformer.js", "source": "src/PostCSSTransformer.js", "engines": {"node": ">= 16.0.0", "parcel": "^2.15.2"}, "dependencies": {"@parcel/diagnostic": "2.15.2", "@parcel/plugin": "2.15.2", "@parcel/rust": "2.15.2", "@parcel/utils": "2.15.2", "clone": "^2.1.2", "nullthrows": "^1.1.1", "postcss-value-parser": "^4.2.0", "semver": "^7.7.1"}, "devDependencies": {"postcss": "^8.5.3", "postcss-modules": "^6.0.1"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}