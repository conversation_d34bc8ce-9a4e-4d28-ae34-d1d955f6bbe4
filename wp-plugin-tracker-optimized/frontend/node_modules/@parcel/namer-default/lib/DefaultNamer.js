"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
function _plugin() {
  const data = require("@parcel/plugin");
  _plugin = function () {
    return data;
  };
  return data;
}
function _diagnostic() {
  const data = _interopRequireWildcard(require("@parcel/diagnostic"));
  _diagnostic = function () {
    return data;
  };
  return data;
}
function _assert() {
  const data = _interopRequireDefault(require("assert"));
  _assert = function () {
    return data;
  };
  return data;
}
function _path() {
  const data = _interopRequireDefault(require("path"));
  _path = function () {
    return data;
  };
  return data;
}
function _nullthrows() {
  const data = _interopRequireDefault(require("nullthrows"));
  _nullthrows = function () {
    return data;
  };
  return data;
}
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
function _getRequireWildcardCache(e) { if ("function" != typeof WeakMap) return null; var r = new WeakMap(), t = new WeakMap(); return (_getRequireWildcardCache = function (e) { return e ? t : r; })(e); }
function _interopRequireWildcard(e, r) { if (!r && e && e.__esModule) return e; if (null === e || "object" != typeof e && "function" != typeof e) return { default: e }; var t = _getRequireWildcardCache(r); if (t && t.has(e)) return t.get(e); var n = { __proto__: null }, a = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var u in e) if ("default" !== u && {}.hasOwnProperty.call(e, u)) { var i = a ? Object.getOwnPropertyDescriptor(e, u) : null; i && (i.get || i.set) ? Object.defineProperty(n, u, i) : n[u] = e[u]; } return n.default = e, t && t.set(e, n), n; }
const COMMON_NAMES = new Set(['index', 'src', 'lib']);
const ALLOWED_EXTENSIONS = {
  js: ['js', 'mjs', 'cjs']
};
var _default = exports.default = new (_plugin().Namer)({
  name({
    bundle,
    bundleGraph
  }) {
    var _entry$meta;
    let bundleGroup = bundleGraph.getBundleGroupsContainingBundle(bundle)[0];
    let bundleGroupBundles = bundleGraph.getBundlesInBundleGroup(bundleGroup, {
      includeInline: true
    });
    let isEntry = bundleGraph.isEntryBundleGroup(bundleGroup);
    if (bundle.needsStableName) {
      let entryBundlesOfType = bundleGroupBundles.filter(b => b.needsStableName && b.type === bundle.type);
      (0, _assert().default)(entryBundlesOfType.length === 1,
      // Otherwise, we'd end up naming two bundles the same thing.
      `Bundle group cannot have more than one entry bundle of the same type. The offending bundle type is ${entryBundlesOfType[0].type}`);
    }
    let mainBundle = (0, _nullthrows().default)(bundleGroupBundles.find(b => b.getEntryAssets().some(a => a.id === bundleGroup.entryAssetId)));
    if (bundle.id === mainBundle.id && isEntry && bundle.target && bundle.target.distEntry != null) {
      let loc = bundle.target.loc;
      let distEntry = bundle.target.distEntry;
      let distExtension = _path().default.extname(bundle.target.distEntry).slice(1);
      let allowedExtensions = ALLOWED_EXTENSIONS[bundle.type] || [bundle.type];
      if (!allowedExtensions.includes(distExtension) && loc) {
        let fullName = _path().default.relative(_path().default.dirname(loc.filePath), _path().default.join(bundle.target.distDir, distEntry));
        let err = new (_diagnostic().default)({
          diagnostic: {
            message: (0, _diagnostic().md)`Target "${bundle.target.name}" declares an output file path of "${fullName}" which does not match the compiled bundle type "${bundle.type}".`,
            codeFrames: [{
              filePath: loc.filePath,
              codeHighlights: [(0, _diagnostic().convertSourceLocationToHighlight)(loc, (0, _diagnostic().md)`Did you mean "${fullName.slice(0, -_path().default.extname(fullName).length) + '.' + bundle.type}"?`)]
            }],
            hints: [`Try changing the file extension of "${bundle.target.name}" in ${_path().default.relative(process.cwd(), loc.filePath)}.`]
          }
        });
        throw err;
      }
      return bundle.target.distEntry;
    }

    // Base split bundle names on the first bundle in their group.
    // e.g. if `index.js` imports `foo.css`, the css bundle should be called
    //      `index.css`.
    let name = nameFromContent(mainBundle, bundle, isEntry, bundleGroup.entryAssetId, bundleGraph.getEntryRoot(bundle.target));
    if (!bundle.needsStableName) {
      name += '.' + bundle.hashReference;
    }

    // Allow bundle extension to be overridden.
    let extension = bundle.type;
    let entry = bundle.getMainEntry();
    if (entry && typeof entry.meta.bundleExtension === 'string') {
      extension = entry.meta.bundleExtension;
    }

    // Group server and client bundles into separate folders.
    // This allows users to easily upload to different places, and avoid exposing
    // server code on public servers.
    // If bundleExtension is set, assume everything is static (rendering at build time).
    if (bundle.env.context === 'react-server' && (entry === null || entry === void 0 || (_entry$meta = entry.meta) === null || _entry$meta === void 0 ? void 0 : _entry$meta.bundleExtension) == null || bundle.env.context === 'react-client' && hasReactServerEntries(bundleGraph)) {
      name = bundle.env.context.slice(6) + '/' + name;
    }
    return name + '.' + extension;
  }
});
function nameFromContent(mainBundle, bundle, isEntry, entryAssetId, entryRoot) {
  let entryFilePath = (0, _nullthrows().default)(mainBundle.getEntryAssets().find(a => a.id === entryAssetId)).filePath;
  let name = basenameWithoutExtension(entryFilePath);

  // If this is an entry bundle, use the original relative path.
  if (bundle.needsStableName) {
    // Match name of target entry if possible, but with a different extension.
    if (isEntry && bundle.target.distEntry != null) {
      return basenameWithoutExtension(bundle.target.distEntry);
    }
    return _path().default.join(_path().default.relative(entryRoot, _path().default.dirname(entryFilePath)), name).replace(/\.\.(\/|\\)/g, 'up_$1');
  } else {
    // If this is an index file or common directory name, use the parent
    // directory name instead, which is probably more descriptive.
    while (COMMON_NAMES.has(name)) {
      entryFilePath = _path().default.dirname(entryFilePath);
      name = _path().default.basename(entryFilePath);
      if (name.startsWith('.')) {
        name = name.replace('.', '');
      }
    }
    return name || 'bundle';
  }
}
function basenameWithoutExtension(file) {
  return _path().default.basename(file, _path().default.extname(file));
}
let rscEntryCache = new WeakMap();
function hasReactServerEntries(bundleGraph) {
  let res = rscEntryCache.get(bundleGraph);
  if (res == null) {
    res = bundleGraph.getEntryBundles().some(b => {
      var _b$getMainEntry;
      return b.env.context === 'react-server' && ((_b$getMainEntry = b.getMainEntry()) === null || _b$getMainEntry === void 0 || (_b$getMainEntry = _b$getMainEntry.meta) === null || _b$getMainEntry === void 0 ? void 0 : _b$getMainEntry.bundleExtension) == null;
    });
    rscEntryCache.set(bundleGraph, res);
  }
  return res;
}