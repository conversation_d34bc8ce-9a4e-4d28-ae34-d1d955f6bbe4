{
  "bundler": "@parcel/bundler-default",
  "transformers": {
    "types:*.{ts,tsx}": ["@parcel/transformer-typescript-types"],
    "bundle-text:*": ["...", "@parcel/transformer-inline-string"],
    "data-url:*": ["...", "@parcel/transformer-inline-string"],
    "worklet:*.{js,mjs,jsm,jsx,es6,cjs,ts,tsx}": [
      "@parcel/transformer-worklet",
      "..."
    ],
    "react-static:*": ["@parcel/transformer-react-static", "..."],
    "*.mdx": [
      // For backward compatibility, include the old transformer
      // so it is used if already installed in the project.
      // Otherwise, the JS transformer will handle MDX.
      "@parcel/transformer-mdx",
      "@parcel/transformer-js"
    ],
    "*.{js,mjs,jsm,jsx,es6,cjs,ts,tsx}": [
      "@parcel/transformer-babel",
      "@parcel/transformer-js",
      "@parcel/transformer-react-refresh-wrap"
    ],
    "*.{json,json5}": ["@parcel/transformer-json"],
    "*.jsonld": ["@parcel/transformer-jsonld"],
    "*.toml": ["@parcel/transformer-toml"],
    "*.webmanifest": ["@parcel/transformer-webmanifest"],
    "webmanifest:*.{json,webmanifest}": ["@parcel/transformer-webmanifest"],
    "*.{yaml,yml}": ["@parcel/transformer-yaml"],
    "*.{glsl,vert,frag}": ["@parcel/transformer-glsl"],
    "*.{gql,graphql}": ["@parcel/transformer-graphql"],
    "*.{styl,stylus}": ["@parcel/transformer-stylus"],
    "*.{sass,scss}": ["@parcel/transformer-sass"],
    "*.less": ["@parcel/transformer-less"],
    "*.{css,pcss}": ["@parcel/transformer-postcss", "@parcel/transformer-css"],
    "*.sss": ["@parcel/transformer-sugarss"],
    "*.{htm,html,xhtml}": [
      "@parcel/transformer-posthtml",
      "@parcel/transformer-html"
    ],
    "*.pug": ["@parcel/transformer-pug"],
    "*.coffee": ["@parcel/transformer-coffeescript"],
    "*.elm": ["@parcel/transformer-elm"],
    "*.vue": ["@parcel/transformer-vue"],
    "template:*.vue": ["@parcel/transformer-vue"],
    "script:*.vue": ["@parcel/transformer-vue"],
    "style:*.vue": ["@parcel/transformer-vue"],
    "custom:*.vue": ["@parcel/transformer-vue"],
    "*.{png,jpg,jpeg,webp,gif,tiff,avif,heic,heif}": [
      "@parcel/transformer-image"
    ],
    "*.svg": ["@parcel/transformer-svg"],
    "*.{xml,rss,atom}": ["@parcel/transformer-xml"],
    "*.node": ["@parcel/transformer-node"],
    "url:*": ["...", "@parcel/transformer-raw"]
  },
  "namers": ["@parcel/namer-default"],
  "runtimes": [
    "@parcel/runtime-rsc",
    "@parcel/runtime-js",
    "@parcel/runtime-browser-hmr",
    "@parcel/runtime-service-worker"
  ],
  "optimizers": {
    "data-url:*": ["...", "@parcel/optimizer-data-url"],
    "*.css": ["@parcel/optimizer-css"],
    "*.{html,xhtml}": ["@parcel/optimizer-html"],
    "*.{js,mjs,cjs}": ["@parcel/optimizer-swc"],
    "*.svg": ["@parcel/optimizer-svg"],
    "*.{jpg,jpeg,png}": ["@parcel/optimizer-image"]
  },
  "packagers": {
    "react-static:*.html": "@parcel/packager-react-static",
    "*.{html,xhtml}": "@parcel/packager-html",
    "*.css": "@parcel/packager-css",
    "*.{js,mjs,cjs}": "@parcel/packager-js",
    "*.svg": "@parcel/packager-svg",
    "*.{xml,rss,atom}": "@parcel/packager-xml",
    "*.ts": "@parcel/packager-ts",
    "*.wasm": "@parcel/packager-wasm",
    "*.{jsonld,svg,webmanifest}": "@parcel/packager-raw-url",
    "*": "@parcel/packager-raw"
  },
  "compressors": {
    "*": ["@parcel/compressor-raw"]
  },
  "resolvers": ["@parcel/resolver-default"],
  "reporters": ["@parcel/reporter-dev-server"]
}
