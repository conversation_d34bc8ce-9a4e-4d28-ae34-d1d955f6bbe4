{"name": "@parcel/config-default", "version": "2.15.2", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "index.json", "scripts": {"test-ci": "mocha"}, "dependencies": {"@parcel/bundler-default": "2.15.2", "@parcel/compressor-raw": "2.15.2", "@parcel/namer-default": "2.15.2", "@parcel/optimizer-css": "2.15.2", "@parcel/optimizer-html": "2.15.2", "@parcel/optimizer-image": "2.15.2", "@parcel/optimizer-svg": "2.15.2", "@parcel/optimizer-swc": "2.15.2", "@parcel/packager-css": "2.15.2", "@parcel/packager-html": "2.15.2", "@parcel/packager-js": "2.15.2", "@parcel/packager-raw": "2.15.2", "@parcel/packager-svg": "2.15.2", "@parcel/packager-wasm": "2.15.2", "@parcel/reporter-dev-server": "2.15.2", "@parcel/resolver-default": "2.15.2", "@parcel/runtime-browser-hmr": "2.15.2", "@parcel/runtime-js": "2.15.2", "@parcel/runtime-rsc": "2.15.2", "@parcel/runtime-service-worker": "2.15.2", "@parcel/transformer-babel": "2.15.2", "@parcel/transformer-css": "2.15.2", "@parcel/transformer-html": "2.15.2", "@parcel/transformer-image": "2.15.2", "@parcel/transformer-js": "2.15.2", "@parcel/transformer-json": "2.15.2", "@parcel/transformer-node": "2.15.2", "@parcel/transformer-postcss": "2.15.2", "@parcel/transformer-posthtml": "2.15.2", "@parcel/transformer-raw": "2.15.2", "@parcel/transformer-react-refresh-wrap": "2.15.2", "@parcel/transformer-svg": "2.15.2"}, "parcelDependencies": {"@parcel/optimizer-data-url": "2.15.2", "@parcel/packager-raw-url": "2.15.2", "@parcel/packager-react-static": "2.15.2", "@parcel/packager-ts": "2.15.2", "@parcel/packager-xml": "2.15.2", "@parcel/transformer-coffeescript": "2.15.2", "@parcel/transformer-elm": "2.15.2", "@parcel/transformer-glsl": "2.15.2", "@parcel/transformer-graphql": "2.15.2", "@parcel/transformer-inline-string": "2.15.2", "@parcel/transformer-jsonld": "2.15.2", "@parcel/transformer-less": "2.15.2", "@parcel/transformer-pug": "2.15.2", "@parcel/transformer-react-static": "2.15.2", "@parcel/transformer-sass": "2.15.2", "@parcel/transformer-stylus": "2.15.2", "@parcel/transformer-sugarss": "2.15.2", "@parcel/transformer-toml": "2.15.2", "@parcel/transformer-typescript-types": "2.15.2", "@parcel/transformer-vue": "2.15.2", "@parcel/transformer-webmanifest": "2.15.2", "@parcel/transformer-worklet": "2.15.2", "@parcel/transformer-xml": "2.15.2", "@parcel/transformer-yaml": "2.15.2"}, "optionalParcelDependencies": ["@parcel/transformer-mdx"], "peerDependencies": {"@parcel/core": "^2.15.2"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}