{"name": "@parcel/logger", "version": "2.15.2", "description": "Blazing fast, zero configuration web application bundler", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/Logger.js", "source": "src/Logger.js", "engines": {"node": ">= 16.0.0"}, "dependencies": {"@parcel/diagnostic": "2.15.2", "@parcel/events": "2.15.2"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}