{"name": "@parcel/runtime-rsc", "version": "2.15.2", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/RSCRuntime.js", "exports": {".": {"source": "./src/RSCRuntime.js", "default": "./lib/RSCRuntime.js"}, "./rsc-helpers": "./rsc-helpers.jsx", "./jsx-dev-runtime": "./jsx-dev-runtime.js", "./jsx-runtime": "./jsx-runtime"}, "engines": {"node": ">= 12.0.0", "parcel": "^2.15.2"}, "dependencies": {"@parcel/plugin": "2.15.2", "@parcel/rust": "2.15.2", "@parcel/utils": "2.15.2", "nullthrows": "^1.1.1"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}