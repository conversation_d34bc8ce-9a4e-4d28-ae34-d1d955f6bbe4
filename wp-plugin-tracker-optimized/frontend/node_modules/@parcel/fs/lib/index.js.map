{"mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AGEA,IAAI,gCAAU,QAAQ,GAAG;AACzB,IAAI,4BAAM;AAEV,IAAI,iCAAW,QAAQ,GAAG,CAAC,oBAAoB,IAAI,QAAQ,QAAQ;AAEnE,QAAQ,GAAG,GAAG;IACZ,IAAI,CAAC,2BACH,4BAAM,8BAAQ,IAAI,CAAC;IACrB,OAAO;AACT;AACA,IAAI;IACF,QAAQ,GAAG;AACb,EAAE,OAAO,IAAI,CAAC;AAEd,oDAAoD;AACpD,IAAI,OAAO,QAAQ,KAAK,KAAK,YAAY;IACvC,IAAI,8BAAQ,QAAQ,KAAK;IACzB,QAAQ,KAAK,GAAG,SAAU,CAAC;QACzB,4BAAM;QACN,4BAAM,IAAI,CAAC,SAAS;IACtB;IACA,IAAI,OAAO,cAAc,EAAE,OAAO,cAAc,CAAC,QAAQ,KAAK,EAAE;AAClE;AAEA,4BAAiB;AAEjB,SAAS,4BAAO,EAAE;IAChB,+DAA+D;IAE/D,gCAAgC;IAChC,0BAA0B;IAC1B,IAAI,gCAAyB,gBACzB,QAAQ,OAAO,CAAC,KAAK,CAAC,2BACxB,YAAY;IAGd,mCAAmC;IACnC,IAAI,CAAC,GAAG,OAAO,EACb,aAAa;IAGf,sDAAsD;IACtD,wDAAwD;IACxD,4DAA4D;IAC5D,oDAAoD;IAEpD,GAAG,KAAK,GAAG,SAAS,GAAG,KAAK;IAC5B,GAAG,MAAM,GAAG,SAAS,GAAG,MAAM;IAC9B,GAAG,MAAM,GAAG,SAAS,GAAG,MAAM;IAE9B,GAAG,KAAK,GAAG,SAAS,GAAG,KAAK;IAC5B,GAAG,MAAM,GAAG,SAAS,GAAG,MAAM;IAC9B,GAAG,MAAM,GAAG,SAAS,GAAG,MAAM;IAE9B,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS;IACxC,GAAG,UAAU,GAAG,aAAa,GAAG,UAAU;IAC1C,GAAG,UAAU,GAAG,aAAa,GAAG,UAAU;IAE1C,GAAG,SAAS,GAAG,aAAa,GAAG,SAAS;IACxC,GAAG,UAAU,GAAG,aAAa,GAAG,UAAU;IAC1C,GAAG,UAAU,GAAG,aAAa,GAAG,UAAU;IAE1C,GAAG,IAAI,GAAG,QAAQ,GAAG,IAAI;IACzB,GAAG,KAAK,GAAG,QAAQ,GAAG,KAAK;IAC3B,GAAG,KAAK,GAAG,QAAQ,GAAG,KAAK;IAE3B,GAAG,QAAQ,GAAG,YAAY,GAAG,QAAQ;IACrC,GAAG,SAAS,GAAG,YAAY,GAAG,SAAS;IACvC,GAAG,SAAS,GAAG,YAAY,GAAG,SAAS;IAEvC,uDAAuD;IACvD,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,MAAM,EAAE;QAC1B,GAAG,MAAM,GAAG,SAAU,IAAI,EAAE,IAAI,EAAE,EAAE;YAClC,IAAI,IAAI,QAAQ,QAAQ,CAAC;QAC3B;QACA,GAAG,UAAU,GAAG,YAAa;IAC/B;IACA,IAAI,GAAG,KAAK,IAAI,CAAC,GAAG,MAAM,EAAE;QAC1B,GAAG,MAAM,GAAG,SAAU,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;YACtC,IAAI,IAAI,QAAQ,QAAQ,CAAC;QAC3B;QACA,GAAG,UAAU,GAAG,YAAa;IAC/B;IAEA,gEAAgE;IAChE,kEAAkE;IAClE,8DAA8D;IAE9D,4EAA4E;IAC5E,uEAAuE;IACvE,6EAA6E;IAC7E,8EAA8E;IAC9E,8EAA8E;IAC9E,IAAI,mCAAa,SACf,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,KAAK,aAAa,GAAG,MAAM,GACrD,AAAC,SAAU,SAAS;QACpB,SAAS,OAAQ,IAAI,EAAE,EAAE,EAAE,EAAE;YAC3B,IAAI,QAAQ,KAAK,GAAG;YACpB,IAAI,UAAU;YACd,UAAU,MAAM,IAAI,SAAS,GAAI,EAAE;gBACjC,IAAI,MACI,CAAA,GAAG,IAAI,KAAK,YAAY,GAAG,IAAI,KAAK,WAAW,GAAG,IAAI,KAAK,OAAM,KAClE,KAAK,GAAG,KAAK,QAAQ,OAAO;oBACjC,WAAW;wBACT,GAAG,IAAI,CAAC,IAAI,SAAU,MAAM,EAAE,EAAE;4BAC9B,IAAI,UAAU,OAAO,IAAI,KAAK,UAC5B,UAAU,MAAM,IAAI;iCAEpB,GAAG;wBACP;oBACF,GAAG;oBACH,IAAI,UAAU,KACZ,WAAW;oBACb;gBACF;gBACA,IAAI,IAAI,GAAG;YACb;QACF;QACA,IAAI,OAAO,cAAc,EAAE,OAAO,cAAc,CAAC,QAAQ;QACzD,OAAO;IACT,EAAG,GAAG,MAAM;IAGd,oDAAoD;IACpD,GAAG,IAAI,GAAG,OAAO,GAAG,IAAI,KAAK,aAAa,GAAG,IAAI,GAC/C,AAAC,SAAU,OAAO;QAClB,SAAS,KAAM,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS;YAC5D,IAAI;YACJ,IAAI,aAAa,OAAO,cAAc,YAAY;gBAChD,IAAI,aAAa;gBACjB,WAAW,SAAU,EAAE,EAAE,CAAC,EAAE,EAAE;oBAC5B,IAAI,MAAM,GAAG,IAAI,KAAK,YAAY,aAAa,IAAI;wBACjD;wBACA,OAAO,QAAQ,IAAI,CAAC,IAAI,IAAI,QAAQ,QAAQ,QAAQ,UAAU;oBAChE;oBACA,UAAU,KAAK,CAAC,IAAI,EAAE;gBACxB;YACF;YACA,OAAO,QAAQ,IAAI,CAAC,IAAI,IAAI,QAAQ,QAAQ,QAAQ,UAAU;QAChE;QAEA,uEAAuE;QACvE,IAAI,OAAO,cAAc,EAAE,OAAO,cAAc,CAAC,MAAM;QACvD,OAAO;IACT,EAAG,GAAG,IAAI;IAEV,GAAG,QAAQ,GAAG,OAAO,GAAG,QAAQ,KAAK,aAAa,GAAG,QAAQ,GAC3D,AAAC,SAAU,WAAW;QAAI,OAAO,SAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,QAAQ;YAC/E,IAAI,aAAa;YACjB,MAAO,KACL,IAAI;gBACF,OAAO,YAAY,IAAI,CAAC,IAAI,IAAI,QAAQ,QAAQ,QAAQ;YAC1D,EAAE,OAAO,IAAI;gBACX,IAAI,GAAG,IAAI,KAAK,YAAY,aAAa,IAAI;oBAC3C;oBACA;gBACF;gBACA,MAAM;YACR;QAEJ;IAAC,EAAG,GAAG,QAAQ;IAEf,SAAS,YAAa,EAAE;QACtB,GAAG,MAAM,GAAG,SAAU,IAAI,EAAE,IAAI,EAAE,QAAQ;YACxC,GAAG,IAAI,CAAE,MACA,4BAAqB,4BACrB,MACA,SAAU,GAAG,EAAE,EAAE;gBACxB,IAAI,KAAK;oBACP,IAAI,UAAU,SAAS;oBACvB;gBACF;gBACA,mDAAmD;gBACnD,mEAAmE;gBACnE,GAAG,MAAM,CAAC,IAAI,MAAM,SAAU,GAAG;oBAC/B,GAAG,KAAK,CAAC,IAAI,SAAS,IAAI;wBACxB,IAAI,UAAU,SAAS,OAAO;oBAChC;gBACF;YACF;QACF;QAEA,GAAG,UAAU,GAAG,SAAU,IAAI,EAAE,IAAI;YAClC,IAAI,KAAK,GAAG,QAAQ,CAAC,MAAM,4BAAqB,4BAAqB;YAErE,mDAAmD;YACnD,mEAAmE;YACnE,IAAI,QAAQ;YACZ,IAAI;YACJ,IAAI;gBACF,MAAM,GAAG,UAAU,CAAC,IAAI;gBACxB,QAAQ;YACV,SAAU;gBACR,IAAI,OACF,IAAI;oBACF,GAAG,SAAS,CAAC;gBACf,EAAE,OAAO,IAAI,CAAC;qBAEd,GAAG,SAAS,CAAC;YAEjB;YACA,OAAO;QACT;IACF;IAEA,SAAS,aAAc,EAAE;QACvB,IAAI,gCAAyB,gBAAgB,GAAG,OAAO,EAAE;YACvD,GAAG,OAAO,GAAG,SAAU,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBACrC,GAAG,IAAI,CAAC,MAAM,4BAAqB,SAAU,EAAE,EAAE,EAAE;oBACjD,IAAI,IAAI;wBACN,IAAI,IAAI,GAAG;wBACX;oBACF;oBACA,GAAG,OAAO,CAAC,IAAI,IAAI,IAAI,SAAU,EAAE;wBACjC,GAAG,KAAK,CAAC,IAAI,SAAU,GAAG;4BACxB,IAAI,IAAI,GAAG,MAAM;wBACnB;oBACF;gBACF;YACF;YAEA,GAAG,WAAW,GAAG,SAAU,IAAI,EAAE,EAAE,EAAE,EAAE;gBACrC,IAAI,KAAK,GAAG,QAAQ,CAAC,MAAM;gBAC3B,IAAI;gBACJ,IAAI,QAAQ;gBACZ,IAAI;oBACF,MAAM,GAAG,WAAW,CAAC,IAAI,IAAI;oBAC7B,QAAQ;gBACV,SAAU;oBACR,IAAI,OACF,IAAI;wBACF,GAAG,SAAS,CAAC;oBACf,EAAE,OAAO,IAAI,CAAC;yBAEd,GAAG,SAAS,CAAC;gBAEjB;gBACA,OAAO;YACT;QAEF,OAAO,IAAI,GAAG,OAAO,EAAE;YACrB,GAAG,OAAO,GAAG,SAAU,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;gBAAI,IAAI,IAAI,QAAQ,QAAQ,CAAC;YAAI;YACtE,GAAG,WAAW,GAAG,YAAa;QAChC;IACF;IAEA,SAAS,SAAU,IAAI;QACrB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,SAAU,MAAM,EAAE,IAAI,EAAE,EAAE;YAC/B,OAAO,KAAK,IAAI,CAAC,IAAI,QAAQ,MAAM,SAAU,EAAE;gBAC7C,IAAI,UAAU,KAAK,KAAK;gBACxB,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE;YACzB;QACF;IACF;IAEA,SAAS,aAAc,IAAI;QACzB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,SAAU,MAAM,EAAE,IAAI;YAC3B,IAAI;gBACF,OAAO,KAAK,IAAI,CAAC,IAAI,QAAQ;YAC/B,EAAE,OAAO,IAAI;gBACX,IAAI,CAAC,UAAU,KAAK,MAAM;YAC5B;QACF;IACF;IAGA,SAAS,SAAU,IAAI;QACrB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,SAAU,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;YACnC,OAAO,KAAK,IAAI,CAAC,IAAI,QAAQ,KAAK,KAAK,SAAU,EAAE;gBACjD,IAAI,UAAU,KAAK,KAAK;gBACxB,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE;YACzB;QACF;IACF;IAEA,SAAS,aAAc,IAAI;QACzB,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,SAAU,MAAM,EAAE,GAAG,EAAE,GAAG;YAC/B,IAAI;gBACF,OAAO,KAAK,IAAI,CAAC,IAAI,QAAQ,KAAK;YACpC,EAAE,OAAO,IAAI;gBACX,IAAI,CAAC,UAAU,KAAK,MAAM;YAC5B;QACF;IACF;IAEA,SAAS,QAAS,IAAI;QACpB,IAAI,CAAC,MAAM,OAAO;QAClB,kEAAkE;QAClE,aAAa;QACb,OAAO,SAAU,MAAM,EAAE,OAAO,EAAE,EAAE;YAClC,IAAI,OAAO,YAAY,YAAY;gBACjC,KAAK;gBACL,UAAU;YACZ;YACA,SAAS,SAAU,EAAE,EAAE,KAAK;gBAC1B,IAAI,OAAO;oBACT,IAAI,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI;oBAChC,IAAI,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI;gBAClC;gBACA,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE;YACzB;YACA,OAAO,UAAU,KAAK,IAAI,CAAC,IAAI,QAAQ,SAAS,YAC5C,KAAK,IAAI,CAAC,IAAI,QAAQ;QAC5B;IACF;IAEA,SAAS,YAAa,IAAI;QACxB,IAAI,CAAC,MAAM,OAAO;QAClB,kEAAkE;QAClE,aAAa;QACb,OAAO,SAAU,MAAM,EAAE,OAAO;YAC9B,IAAI,QAAQ,UAAU,KAAK,IAAI,CAAC,IAAI,QAAQ,WACxC,KAAK,IAAI,CAAC,IAAI;YAClB,IAAI,OAAO;gBACT,IAAI,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI;gBAChC,IAAI,MAAM,GAAG,GAAG,GAAG,MAAM,GAAG,IAAI;YAClC;YACA,OAAO;QACT;IACF;IAEA,+DAA+D;IAC/D,mCAAmC;IACnC,EAAE;IACF,0DAA0D;IAC1D,6DAA6D;IAC7D,MAAM;IACN,EAAE;IACF,8DAA8D;IAC9D,qDAAqD;IACrD,EAAE;IACF,wDAAwD;IACxD,iCAAiC;IACjC,SAAS,UAAW,EAAE;QACpB,IAAI,CAAC,IACH,OAAO;QAET,IAAI,GAAG,IAAI,KAAK,UACd,OAAO;QAET,IAAI,UAAU,CAAC,QAAQ,MAAM,IAAI,QAAQ,MAAM,OAAO;QACtD,IAAI,SAAS;YACX,IAAI,GAAG,IAAI,KAAK,YAAY,GAAG,IAAI,KAAK,SACtC,OAAO;QACX;QAEA,OAAO;IACT;AACF;;;;;uCClWI;AAEJ,4BAAiB;AAEjB,SAAS,6BAAQ,EAAE;IACjB,OAAO;QACL,YAAY;QACZ,aAAa;IACf;IAEA,SAAS,WAAY,IAAI,EAAE,OAAO;QAChC,IAAI,CAAE,CAAA,IAAI,YAAY,UAAS,GAAI,OAAO,IAAI,WAAW,MAAM;QAE/D,iCAAO,IAAI,CAAC,IAAI;QAEhB,IAAI,OAAO,IAAI;QAEf,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;QAEd,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,IAAI,GAAG,KAAK,OAAO;QACxB,IAAI,CAAC,UAAU,GAAG;QAElB,UAAU,WAAW,CAAC;QAEtB,0BAA0B;QAC1B,IAAI,OAAO,OAAO,IAAI,CAAC;QACvB,IAAK,IAAI,QAAQ,GAAG,SAAS,KAAK,MAAM,EAAE,QAAQ,QAAQ,QAAS;YACjE,IAAI,MAAM,IAAI,CAAC,MAAM;YACrB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI;QAC1B;QAEA,IAAI,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ;QAEjD,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW;YAC5B,IAAI,aAAa,OAAO,IAAI,CAAC,KAAK,EAChC,MAAM,UAAU;YAElB,IAAI,IAAI,CAAC,GAAG,KAAK,WACf,IAAI,CAAC,GAAG,GAAG;iBACN,IAAI,aAAa,OAAO,IAAI,CAAC,GAAG,EACrC,MAAM,UAAU;YAGlB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,EACvB,MAAM,IAAI,MAAM;YAGlB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK;QACvB;QAEA,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM;YACpB,QAAQ,QAAQ,CAAC;gBACf,KAAK,KAAK;YACZ;YACA;QACF;QAEA,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,SAAU,GAAG,EAAE,EAAE;YACzD,IAAI,KAAK;gBACP,KAAK,IAAI,CAAC,SAAS;gBACnB,KAAK,QAAQ,GAAG;gBAChB;YACF;YAEA,KAAK,EAAE,GAAG;YACV,KAAK,IAAI,CAAC,QAAQ;YAClB,KAAK,KAAK;QACZ;IACF;IAEA,SAAS,YAAa,IAAI,EAAE,OAAO;QACjC,IAAI,CAAE,CAAA,IAAI,YAAY,WAAU,GAAI,OAAO,IAAI,YAAY,MAAM;QAEjE,iCAAO,IAAI,CAAC,IAAI;QAEhB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,CAAC,QAAQ,GAAG;QAEhB,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,IAAI,GAAG,KAAK,OAAO;QACxB,IAAI,CAAC,YAAY,GAAG;QAEpB,UAAU,WAAW,CAAC;QAEtB,0BAA0B;QAC1B,IAAI,OAAO,OAAO,IAAI,CAAC;QACvB,IAAK,IAAI,QAAQ,GAAG,SAAS,KAAK,MAAM,EAAE,QAAQ,QAAQ,QAAS;YACjE,IAAI,MAAM,IAAI,CAAC,MAAM;YACrB,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI;QAC1B;QAEA,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW;YAC5B,IAAI,aAAa,OAAO,IAAI,CAAC,KAAK,EAChC,MAAM,UAAU;YAElB,IAAI,IAAI,CAAC,KAAK,GAAG,GACf,MAAM,IAAI,MAAM;YAGlB,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,KAAK;QACvB;QAEA,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG,EAAE;QAEhB,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM;YACpB,IAAI,CAAC,KAAK,GAAG,GAAG,IAAI;YACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;gBAAC,IAAI,CAAC,KAAK;gBAAE,IAAI,CAAC,IAAI;gBAAE,IAAI,CAAC,KAAK;gBAAE,IAAI,CAAC,IAAI;gBAAE;aAAU;YAC1E,IAAI,CAAC,KAAK;QACZ;IACF;AACF;;;;ACrHA;AAEA,4BAAiB;AAEjB,IAAI,uCAAiB,OAAO,cAAc,IAAI,SAAU,GAAG;IACzD,OAAO,IAAI,SAAS;AACtB;AAEA,SAAS,4BAAO,GAAG;IACjB,IAAI,QAAQ,QAAQ,OAAO,QAAQ,UACjC,OAAO;IAET,IAAI,eAAe,QACjB,IAAI,OAAO;QAAE,WAAW,qCAAe;IAAK;SAE5C,IAAI,OAAO,OAAO,MAAM,CAAC;IAE3B,OAAO,mBAAmB,CAAC,KAAK,OAAO,CAAC,SAAU,GAAG;QACnD,OAAO,cAAc,CAAC,MAAM,KAAK,OAAO,wBAAwB,CAAC,KAAK;IACxE;IAEA,OAAO;AACT;;;;AHfA,4CAA4C,GAC5C,IAAI;AACJ,IAAI;AAEJ,4CAA4C,GAC5C,IAAI,OAAO,WAAW,cAAc,OAAO,OAAO,GAAG,KAAK,YAAY;IACpE,sCAAgB,OAAO,GAAG,CAAC;IAC3B,6CAA6C;IAC7C,uCAAiB,OAAO,GAAG,CAAC;AAC9B,OAAO;IACL,sCAAgB;IAChB,uCAAiB;AACnB;AAEA,SAAS,8BAAS;AAElB,SAAS,mCAAa,OAAO,EAAE,KAAK;IAClC,OAAO,cAAc,CAAC,SAAS,qCAAe;QAC5C,KAAK;YACH,OAAO;QACT;IACF;AACF;AAEA,IAAI,8BAAQ;AACZ,IAAI,YAAK,QAAQ,EACf,8BAAQ,YAAK,QAAQ,CAAC;KACnB,IAAI,YAAY,IAAI,CAAC,QAAQ,GAAG,CAAC,UAAU,IAAI,KAClD,8BAAQ;IACN,IAAI,IAAI,YAAK,MAAM,CAAC,KAAK,CAAC,aAAM;IAChC,IAAI,WAAW,EAAE,KAAK,CAAC,MAAM,IAAI,CAAC;IAClC,QAAQ,KAAK,CAAC;AAChB;;AAEF,2BAA2B;AAC3B,IAAI,CAAC,SAAE,CAAC,oCAAc,EAAE;IACtB,wDAAwD;IACxD,IAAI,8BAAQ,cAAM,CAAC,oCAAc,IAAI,EAAE;IACvC,mCAAa,WAAI;IAEjB,oEAAoE;IACpE,iEAAiE;IACjE,4DAA4D;IAC5D,4BAA4B;IAC5B,UAAG,KAAK,GAAG,AAAC,SAAU,QAAQ;QAC5B,SAAS,MAAO,EAAE,EAAE,EAAE;YACpB,OAAO,SAAS,IAAI,CAAC,WAAI,IAAI,SAAU,GAAG;gBACxC,kDAAkD;gBAClD,IAAI,CAAC,KACH;gBAGF,IAAI,OAAO,OAAO,YAChB,GAAG,KAAK,CAAC,IAAI,EAAE;YACnB;QACF;QAEA,OAAO,cAAc,CAAC,OAAO,sCAAgB;YAC3C,OAAO;QACT;QACA,OAAO;IACT,EAAG,UAAG,KAAK;IAEX,UAAG,SAAS,GAAG,AAAC,SAAU,YAAY;QACpC,SAAS,UAAW,EAAE;YACpB,kDAAkD;YAClD,aAAa,KAAK,CAAC,WAAI;YACvB;QACF;QAEA,OAAO,cAAc,CAAC,WAAW,sCAAgB;YAC/C,OAAO;QACT;QACA,OAAO;IACT,EAAG,UAAG,SAAS;IAEf,IAAI,YAAY,IAAI,CAAC,QAAQ,GAAG,CAAC,UAAU,IAAI,KAC7C,QAAQ,EAAE,CAAC,QAAQ;QACjB,4BAAM,SAAE,CAAC,oCAAc;QACvB,oBAAwB,SAAE,CAAC,oCAAc,CAAC,MAAM,EAAE;IACpD;AAEJ;AAEA,IAAI,CAAC,cAAM,CAAC,oCAAc,EACxB,mCAAa,gBAAQ,SAAE,CAAC,oCAAc;AAGxC,4BAAiB,4BAAM,0BAAM;AAC7B,IAAI,QAAQ,GAAG,CAAC,6BAA6B,IAAI,CAAC,UAAG,SAAS,EAAE;IAC5D,4BAAiB,4BAAM;IACvB,UAAG,SAAS,GAAG;AACnB;AAEA,SAAS,4BAAO,EAAE;IAChB,qEAAqE;IACrE,0BAAU;IACV,GAAG,WAAW,GAAG;IAEjB,GAAG,gBAAgB,GAAG;IACtB,GAAG,iBAAiB,GAAG;IACvB,IAAI,cAAc,GAAG,QAAQ;IAC7B,GAAG,QAAQ,GAAG;IACd,SAAS,SAAU,IAAI,EAAE,OAAO,EAAE,EAAE;QAClC,IAAI,OAAO,YAAY,YACrB,KAAK,SAAS,UAAU;QAE1B,OAAO,YAAY,MAAM,SAAS;QAElC,SAAS,YAAa,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS;YAChD,OAAO,YAAY,MAAM,SAAS,SAAU,GAAG;gBAC7C,IAAI,OAAQ,CAAA,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,QAAO,GACvD,8BAAQ;oBAAC;oBAAa;wBAAC;wBAAM;wBAAS;qBAAG;oBAAE;oBAAK,aAAa,KAAK,GAAG;oBAAI,KAAK,GAAG;iBAAG;qBAEpF,IAAI,OAAO,OAAO,YAChB,GAAG,KAAK,CAAC,IAAI,EAAE;YAErB;QACF;IACF;IAEA,IAAI,eAAe,GAAG,SAAS;IAC/B,GAAG,SAAS,GAAG;IACf,SAAS,UAAW,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;QACzC,IAAI,OAAO,YAAY,YACrB,KAAK,SAAS,UAAU;QAE1B,OAAO,aAAa,MAAM,MAAM,SAAS;QAEzC,SAAS,aAAc,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS;YACvD,OAAO,aAAa,MAAM,MAAM,SAAS,SAAU,GAAG;gBACpD,IAAI,OAAQ,CAAA,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,QAAO,GACvD,8BAAQ;oBAAC;oBAAc;wBAAC;wBAAM;wBAAM;wBAAS;qBAAG;oBAAE;oBAAK,aAAa,KAAK,GAAG;oBAAI,KAAK,GAAG;iBAAG;qBAE3F,IAAI,OAAO,OAAO,YAChB,GAAG,KAAK,CAAC,IAAI,EAAE;YAErB;QACF;IACF;IAEA,IAAI,gBAAgB,GAAG,UAAU;IACjC,IAAI,eACF,GAAG,UAAU,GAAG;IAClB,SAAS,WAAY,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;QAC1C,IAAI,OAAO,YAAY,YACrB,KAAK,SAAS,UAAU;QAE1B,OAAO,cAAc,MAAM,MAAM,SAAS;QAE1C,SAAS,cAAe,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS;YACxD,OAAO,cAAc,MAAM,MAAM,SAAS,SAAU,GAAG;gBACrD,IAAI,OAAQ,CAAA,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,QAAO,GACvD,8BAAQ;oBAAC;oBAAe;wBAAC;wBAAM;wBAAM;wBAAS;qBAAG;oBAAE;oBAAK,aAAa,KAAK,GAAG;oBAAI,KAAK,GAAG;iBAAG;qBAE5F,IAAI,OAAO,OAAO,YAChB,GAAG,KAAK,CAAC,IAAI,EAAE;YAErB;QACF;IACF;IAEA,IAAI,cAAc,GAAG,QAAQ;IAC7B,IAAI,aACF,GAAG,QAAQ,GAAG;IAChB,SAAS,SAAU,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE;QACrC,IAAI,OAAO,UAAU,YAAY;YAC/B,KAAK;YACL,QAAQ;QACV;QACA,OAAO,YAAY,KAAK,MAAM,OAAO;QAErC,SAAS,YAAa,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,SAAS;YACnD,OAAO,YAAY,KAAK,MAAM,OAAO,SAAU,GAAG;gBAChD,IAAI,OAAQ,CAAA,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,QAAO,GACvD,8BAAQ;oBAAC;oBAAa;wBAAC;wBAAK;wBAAM;wBAAO;qBAAG;oBAAE;oBAAK,aAAa,KAAK,GAAG;oBAAI,KAAK,GAAG;iBAAG;qBAEvF,IAAI,OAAO,OAAO,YAChB,GAAG,KAAK,CAAC,IAAI,EAAE;YAErB;QACF;IACF;IAEA,IAAI,aAAa,GAAG,OAAO;IAC3B,GAAG,OAAO,GAAG;IACb,IAAI,0BAA0B;IAC9B,SAAS,QAAS,IAAI,EAAE,OAAO,EAAE,EAAE;QACjC,IAAI,OAAO,YAAY,YACrB,KAAK,SAAS,UAAU;QAE1B,IAAI,aAAa,wBAAwB,IAAI,CAAC,QAAQ,OAAO,IACzD,SAAS,WAAY,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS;YACjD,OAAO,WAAW,MAAM,mBACtB,MAAM,SAAS,IAAI;QAEvB,IACE,SAAS,WAAY,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS;YACjD,OAAO,WAAW,MAAM,SAAS,mBAC/B,MAAM,SAAS,IAAI;QAEvB;QAEF,OAAO,WAAW,MAAM,SAAS;QAEjC,SAAS,mBAAoB,IAAI,EAAE,OAAO,EAAE,EAAE,EAAE,SAAS;YACvD,OAAO,SAAU,GAAG,EAAE,KAAK;gBACzB,IAAI,OAAQ,CAAA,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,QAAO,GACvD,8BAAQ;oBACN;oBACA;wBAAC;wBAAM;wBAAS;qBAAG;oBACnB;oBACA,aAAa,KAAK,GAAG;oBACrB,KAAK,GAAG;iBACT;qBACE;oBACH,IAAI,SAAS,MAAM,IAAI,EACrB,MAAM,IAAI;oBAEZ,IAAI,OAAO,OAAO,YAChB,GAAG,IAAI,CAAC,IAAI,EAAE,KAAK;gBACvB;YACF;QACF;IACF;IAEA,IAAI,QAAQ,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,QAAQ;QAC3C,IAAI,aAAa,0BAAO;QACxB,aAAa,WAAW,UAAU;QAClC,cAAc,WAAW,WAAW;IACtC;IAEA,IAAI,gBAAgB,GAAG,UAAU;IACjC,IAAI,eAAe;QACjB,WAAW,SAAS,GAAG,OAAO,MAAM,CAAC,cAAc,SAAS;QAC5D,WAAW,SAAS,CAAC,IAAI,GAAG;IAC9B;IAEA,IAAI,iBAAiB,GAAG,WAAW;IACnC,IAAI,gBAAgB;QAClB,YAAY,SAAS,GAAG,OAAO,MAAM,CAAC,eAAe,SAAS;QAC9D,YAAY,SAAS,CAAC,IAAI,GAAG;IAC/B;IAEA,OAAO,cAAc,CAAC,IAAI,cAAc;QACtC,KAAK;YACH,OAAO;QACT;QACA,KAAK,SAAU,GAAG;YAChB,aAAa;QACf;QACA,YAAY;QACZ,cAAc;IAChB;IACA,OAAO,cAAc,CAAC,IAAI,eAAe;QACvC,KAAK;YACH,OAAO;QACT;QACA,KAAK,SAAU,GAAG;YAChB,cAAc;QAChB;QACA,YAAY;QACZ,cAAc;IAChB;IAEA,eAAe;IACf,IAAI,iBAAiB;IACrB,OAAO,cAAc,CAAC,IAAI,kBAAkB;QAC1C,KAAK;YACH,OAAO;QACT;QACA,KAAK,SAAU,GAAG;YAChB,iBAAiB;QACnB;QACA,YAAY;QACZ,cAAc;IAChB;IACA,IAAI,kBAAkB;IACtB,OAAO,cAAc,CAAC,IAAI,mBAAmB;QAC3C,KAAK;YACH,OAAO;QACT;QACA,KAAK,SAAU,GAAG;YAChB,kBAAkB;QACpB;QACA,YAAY;QACZ,cAAc;IAChB;IAEA,SAAS,WAAY,IAAI,EAAE,OAAO;QAChC,IAAI,IAAI,YAAY,YAClB,OAAO,cAAc,KAAK,CAAC,IAAI,EAAE,YAAY,IAAI;aAEjD,OAAO,WAAW,KAAK,CAAC,OAAO,MAAM,CAAC,WAAW,SAAS,GAAG;IACjE;IAEA,SAAS;QACP,IAAI,OAAO,IAAI;QACf,KAAK,KAAK,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,SAAU,GAAG,EAAE,EAAE;YACtD,IAAI,KAAK;gBACP,IAAI,KAAK,SAAS,EAChB,KAAK,OAAO;gBAEd,KAAK,IAAI,CAAC,SAAS;YACrB,OAAO;gBACL,KAAK,EAAE,GAAG;gBACV,KAAK,IAAI,CAAC,QAAQ;gBAClB,KAAK,IAAI;YACX;QACF;IACF;IAEA,SAAS,YAAa,IAAI,EAAE,OAAO;QACjC,IAAI,IAAI,YAAY,aAClB,OAAO,eAAe,KAAK,CAAC,IAAI,EAAE,YAAY,IAAI;aAElD,OAAO,YAAY,KAAK,CAAC,OAAO,MAAM,CAAC,YAAY,SAAS,GAAG;IACnE;IAEA,SAAS;QACP,IAAI,OAAO,IAAI;QACf,KAAK,KAAK,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,SAAU,GAAG,EAAE,EAAE;YACtD,IAAI,KAAK;gBACP,KAAK,OAAO;gBACZ,KAAK,IAAI,CAAC,SAAS;YACrB,OAAO;gBACL,KAAK,EAAE,GAAG;gBACV,KAAK,IAAI,CAAC,QAAQ;YACpB;QACF;IACF;IAEA,SAAS,iBAAkB,IAAI,EAAE,OAAO;QACtC,OAAO,IAAI,GAAG,UAAU,CAAC,MAAM;IACjC;IAEA,SAAS,kBAAmB,IAAI,EAAE,OAAO;QACvC,OAAO,IAAI,GAAG,WAAW,CAAC,MAAM;IAClC;IAEA,IAAI,UAAU,GAAG,IAAI;IACrB,GAAG,IAAI,GAAG;IACV,SAAS,KAAM,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;QAClC,IAAI,OAAO,SAAS,YAClB,KAAK,MAAM,OAAO;QAEpB,OAAO,QAAQ,MAAM,OAAO,MAAM;QAElC,SAAS,QAAS,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,SAAS;YAChD,OAAO,QAAQ,MAAM,OAAO,MAAM,SAAU,GAAG,EAAE,EAAE;gBACjD,IAAI,OAAQ,CAAA,IAAI,IAAI,KAAK,YAAY,IAAI,IAAI,KAAK,QAAO,GACvD,8BAAQ;oBAAC;oBAAS;wBAAC;wBAAM;wBAAO;wBAAM;qBAAG;oBAAE;oBAAK,aAAa,KAAK,GAAG;oBAAI,KAAK,GAAG;iBAAG;qBAEpF,IAAI,OAAO,OAAO,YAChB,GAAG,KAAK,CAAC,IAAI,EAAE;YAErB;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS,8BAAS,IAAI;IACpB,4BAAM,WAAW,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;IACtC,SAAE,CAAC,oCAAc,CAAC,IAAI,CAAC;IACvB;AACF;AAEA,kDAAkD;AAClD,IAAI;AAEJ,0CAA0C;AAC1C,wEAAwE;AACxE,+DAA+D;AAC/D,SAAS;IACP,IAAI,MAAM,KAAK,GAAG;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,SAAE,CAAC,oCAAc,CAAC,MAAM,EAAE,EAAE,EAC9C,uEAAuE;IACvE,0DAA0D;IAC1D,IAAI,SAAE,CAAC,oCAAc,CAAC,EAAE,CAAC,MAAM,GAAG,GAAG;QACnC,SAAE,CAAC,oCAAc,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,YAAY;;QAC1C,SAAE,CAAC,oCAAc,CAAC,EAAE,CAAC,EAAE,GAAG,IAAI,WAAW;;IAC3C;IAEF,8DAA8D;IAC9D;AACF;AAEA,SAAS;IACP,uEAAuE;IACvE,aAAa;IACb,mCAAa;IAEb,IAAI,SAAE,CAAC,oCAAc,CAAC,MAAM,KAAK,GAC/B;IAEF,IAAI,OAAO,SAAE,CAAC,oCAAc,CAAC,KAAK;IAClC,IAAI,KAAK,IAAI,CAAC,EAAE;IAChB,IAAI,OAAO,IAAI,CAAC,EAAE;IAClB,sEAAsE;IACtE,IAAI,MAAM,IAAI,CAAC,EAAE;IACjB,IAAI,YAAY,IAAI,CAAC,EAAE;IACvB,IAAI,WAAW,IAAI,CAAC,EAAE;IAEtB,yEAAyE;IACzE,mDAAmD;IACnD,IAAI,cAAc,WAAW;QAC3B,4BAAM,SAAS,GAAG,IAAI,EAAE;QACxB,GAAG,KAAK,CAAC,MAAM;IACjB,OAAO,IAAI,KAAK,GAAG,KAAK,aAAa,OAAO;QAC1C,iDAAiD;QACjD,4BAAM,WAAW,GAAG,IAAI,EAAE;QAC1B,IAAI,KAAK,KAAK,GAAG;QACjB,IAAI,OAAO,OAAO,YAChB,GAAG,IAAI,CAAC,MAAM;IAClB,OAAO;QACL,4DAA4D;QAC5D,IAAI,eAAe,KAAK,GAAG,KAAK;QAChC,yEAAyE;QACzE,2BAA2B;QAC3B,IAAI,aAAa,KAAK,GAAG,CAAC,WAAW,WAAW;QAChD,yEAAyE;QACzE,2BAA2B;QAC3B,IAAI,eAAe,KAAK,GAAG,CAAC,aAAa,KAAK;QAC9C,0DAA0D;QAC1D,IAAI,gBAAgB,cAAc;YAChC,4BAAM,SAAS,GAAG,IAAI,EAAE;YACxB,GAAG,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC;gBAAC;aAAU;QACxC,OACE,+DAA+D;QAC/D,yCAAyC;QACzC,SAAE,CAAC,oCAAc,CAAC,IAAI,CAAC;IAE3B;IAEA,uDAAuD;IACvD,IAAI,qCAAe,WACjB,mCAAa,WAAW,6BAAO;AAEnC;;;;;;;AI5bA,4BAAiB;AACjB,0BAAI,GAAG,GAAG;AAEV,SAAS,0BAAK,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ;IAC3C,IAAI,QAAQ;IAEZ,IAAI,CAAC,UAAU;QACb,QAAQ;QACR,UAAU,CAAC;IACb;IAEA,IAAI,WAAW,QAAQ,GAAG,IACtB,cAAc,oBAAa,UAAU,SACrC,aAAa,oBAAa,UAAU,OACpC,SAAS,QAAQ,MAAM,EACvB,SAAS,QAAQ,MAAM,EACvB,YAAY,QAAQ,SAAS,EAC7B,UAAU,QAAQ,OAAO,KAAK,OAC9B,WAAW,QAAQ,QAAQ,EAC3B,cAAc,QAAQ,WAAW,EACjC,OAAO,MACP,UAAU,GACV,WAAW,GACX,UAAU,GACV,QAAQ,QAAQ,KAAK,IAAI,0BAAI,KAAK,IAAI;IAE1C,QAAQ,AAAC,QAAQ,IAAK,IAAI,AAAC,QAAQ,MAAO,MAAM;IAEhD,UAAU;IAEV,SAAS,UAAU,MAAM;QACvB;QACA,IAAI,QAAQ;YACV,IAAI,kBAAkB,QAAQ;gBAC5B,IAAI,CAAC,OAAO,IAAI,CAAC,SACf,OAAO,GAAG;YAEd,OACK,IAAI,OAAO,WAAW,YAAY;gBACrC,IAAI,CAAC,OAAO,SACV,OAAO,GAAG;YAEd;QACF;QACA,OAAO,SAAS;IAClB;IAEA,SAAS,SAAS,MAAM;QACtB,IAAI,OAAO,cAAc,iBAAU;QACnC,IAAI,WAAW,OACb,OAAO,aAAa;YAClB,SAAS;QACX;QAEF;QACA,KAAK,QAAQ,SAAU,GAAG,EAAE,KAAK;YAC/B,IAAI,OAAO,CAAC;YACZ,IAAI,KACF,OAAO,QAAQ;YAGjB,iEAAiE;YACjE,KAAK,IAAI,GAAG;YACZ,KAAK,IAAI,GAAG,MAAM,IAAI;YACtB,KAAK,KAAK,GAAG,MAAM,KAAK,EAAE,eAAe;YACzC,KAAK,KAAK,GAAG,MAAM,KAAK,EAAE,aAAa;YAEvC,IAAI,MAAM,WAAW,IACnB,OAAO,MAAM;iBAEV,IAAI,MAAM,MAAM,IACnB,OAAO,OAAO;iBAEX,IAAI,MAAM,cAAc,IAC3B,qDAAqD;YACrD,OAAO,OAAO;QAElB;IACF;IAEA,SAAS,OAAO,IAAI;QAClB,IAAI,SAAS,KAAK,IAAI,CAAC,OAAO,CAAC,aAAa;QAC5C,IAAG,QACD,SAAU,OAAO;QAEnB,WAAW,QAAQ,SAAU,QAAQ;YACnC,IAAI,UACF,OAAO,SAAS,MAAM;YAExB,IAAG,SACD,OAAO,QAAQ;gBACb,SAAS,MAAM;YACjB;YAEF,IAAI,UAAU;gBACZ,IAAI,OAAO,cAAc,iBAAU;gBACnC,KAAK,QAAQ,SAAS,GAAG,EAAE,KAAK;oBAC5B,kEAAkE;oBAClE,IAAI,KAAK,KAAK,CAAC,OAAO,KAAG,MAAM,KAAK,CAAC,OAAO,IACxC,SAAS,MAAM;yBACd,OAAO;gBAChB;YACF,OAEE,OAAO;QAEX;IACF;IAEA,SAAS,SAAS,IAAI,EAAE,MAAM;QAC5B,IAAI,aAAa,2BAAoB,KAAK,IAAI,GAC1C,cAAc,4BAAqB,QAAQ;YAAE,MAAM,KAAK,IAAI;QAAC;QAEjE,WAAW,EAAE,CAAC,SAAS;QACvB,YAAY,EAAE,CAAC,SAAS;QAExB,IAAG,WACD,UAAU,YAAY,aAAa;aAEnC,YAAY,EAAE,CAAC,QAAQ;YACrB,WAAW,IAAI,CAAC;QAClB;QAEF,YAAY,IAAI,CAAC,UAAU;YACvB,IAAI,UAAU;gBACV,iCAAiC;gBACjC,qBAAc,QAAQ,KAAK,KAAK,EAAE,KAAK,KAAK;gBAC5C;YACJ,OACK;QACT;IACF;IAEA,SAAS,OAAO,IAAI,EAAE,IAAI;QACxB,iBAAU,MAAM,SAAU,GAAG;YAC3B,IAAI,KACF,OAAO,QAAQ;YAEjB,OAAO;QACT;IACF;IAEA,SAAS,MAAM,GAAG;QAChB,IAAI,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa;QAC3C,WAAW,QAAQ,SAAU,QAAQ;YACnC,IAAI,UACF,OAAO,MAAM,KAAK;YAEpB,QAAQ,IAAI,IAAI;QAClB;IACF;IAEA,SAAS,MAAM,GAAG,EAAE,MAAM;QACxB,gBAAS,QAAQ,IAAI,IAAI,EAAE,SAAU,GAAG;YACtC,IAAI,KACF,OAAO,QAAQ;YAEjB,QAAQ,IAAI,IAAI;QAClB;IACF;IAEA,SAAS,QAAQ,GAAG;QAClB,kBAAW,KAAK,SAAU,GAAG,EAAE,KAAK;YAClC,IAAI,KACF,OAAO,QAAQ;YAEjB,MAAM,OAAO,CAAC,SAAU,IAAI;gBAC1B,UAAU,iBAAU,KAAK;YAC3B;YACA,OAAO;QACT;IACF;IAEA,SAAS,OAAO,IAAI;QAClB,IAAI,SAAS,KAAK,OAAO,CAAC,aAAa;QACvC,mBAAY,MAAM,SAAU,GAAG,EAAE,YAAY;YAC3C,IAAI,KACF,OAAO,QAAQ;YAEjB,UAAU,cAAc;QAC1B;IACF;IAEA,SAAS,UAAU,YAAY,EAAE,MAAM;QACrC,IAAI,aACF,eAAe,oBAAa,UAAU;QAExC,WAAW,QAAQ,SAAU,QAAQ;YACnC,IAAI,UACF,OAAO,SAAS,cAAc;YAEhC,mBAAY,QAAQ,SAAU,GAAG,EAAE,UAAU;gBAC3C,IAAI,KACF,OAAO,QAAQ;gBAEjB,IAAI,aACF,aAAa,oBAAa,UAAU;gBAEtC,IAAI,eAAe,cACjB,OAAO;gBAET,OAAO,OAAO,QAAQ;oBACpB,SAAS,cAAc;gBACzB;YACF;QACF;IACF;IAEA,SAAS,SAAS,QAAQ,EAAE,MAAM;QAChC,kBAAW,UAAU,QAAQ,SAAU,GAAG;YACxC,IAAI,KACF,OAAO,QAAQ;YAEjB,OAAO;QACT;IACF;IAEA,SAAS,WAAW,IAAI,EAAE,IAAI;QAC5B,gBAAS,MAAM,SAAU,GAAG;YAC1B,IAAI,KAAK;gBACP,IAAI,IAAI,IAAI,KAAK,UAAU,OAAO,KAAK;gBACvC,OAAO,KAAK;YACd;YACA,OAAO,KAAK;QACd;IACF;IAEA,SAAS,QAAQ,GAAG;QAClB,IAAI,QAAQ,WAAW,EACrB,OAAO,MAAM;aAEV,IAAI,CAAC,QAAQ,QAAQ,IAAI,EAC5B,OAAO,4BAAqB,QAAQ,IAAI;aAErC,IAAI,CAAC,MACR,OAAO,EAAE;QAEX,IAAI,OAAO,KAAK,KAAK,KAAK,aACxB,KAAK,IAAI,CAAC;aAGV,KAAK,KAAK,CAAC,IAAI,KAAK,GAAG;QAEzB,OAAO;IACT;IAEA,SAAS,GAAG,OAAO;QACjB,IAAI,CAAC,SAAS;QACd;QACA,IAAI,AAAC,YAAY,YAAc,YAAY,GAAI;YAC7C,IAAI,UAAU,WACZ,OAAO,OAAO,MAAM,QAAQ,MAAM;QAEtC;IACF;AACF;;;;;;;;;;;AClQA+H,4BAAiBE,KAAKC,KAAK,CAAC;;;;;ACIrB,SAASrE,0CACdvC,EAAc,EACd6G,UAAkB,EAClBrC,GAAa;IAEb,IAAI,QAACsC,IAAAA,EAAK,GAAGpI,CAAAA,GAAAA,qCAAAA,EAAKkI,KAAK,CAACpC;IACxB,MAAOA,QAAQsC,KAAM;QACnB,gCAAA;QACA,IAAIpI,CAAAA,GAAAA,qCAAAA,EAAK4H,QAAQ,CAAC9B,SAAS,gBACzBA,MAAM9F,CAAAA,GAAAA,qCAAAA,EAAKqI,OAAO,CAACvC;QAGrB,IAAI;YACF,IAAIwC,YAAYtI,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACgF,KAAK,gBAAgBqC;YAC/C,IAAInH,QAAQM,GAAG+B,QAAQ,CAACiF;YACxB,IAAItH,MAAMK,WAAW,IACnB,OAAOiH;QAEX,EAAE,OAAOpD,KAAK;QACZ,SAAA;QAAA;QAGF,sBAAA;QACAY,MAAM9F,CAAAA,GAAAA,qCAAAA,EAAKqI,OAAO,CAACvC;IACrB;IAEA,OAAO;AACT;AAEO,SAASnC,0CACdrC,EAAc,EACdiH,SAAwB,EACxBzC,GAAa,EACbsC,IAAc;IAEd,IAAI,EAACA,MAAMI,QAANJ,EAAe,GAAGpI,CAAAA,GAAAA,qCAAAA,EAAKkI,KAAK,CAACpC;IAClC,iDAAA;IACA,MAAO,KAAM;QACX,IAAI9F,CAAAA,GAAAA,qCAAAA,EAAK4H,QAAQ,CAAC9B,SAAS,gBACzB,OAAO;QAGT,KAAK,MAAM2C,YAAYF,UAAW;YAChC,IAAIxE,WAAW/D,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACgF,KAAK2C;YAC9B,IAAI;gBACF,IAAInH,GAAG+B,QAAQ,CAACU,UAAU7C,MAAM,IAC9B,OAAO6C;YAEX,EAAE,OAAOmB,KAAK;YACZ,SAAA;YAAA;QAEJ;QAEA,IAAIY,QAAQsC,QAAQtC,QAAQ0C,UAC1B;QAGF1C,MAAM9F,CAAAA,GAAAA,qCAAAA,EAAKqI,OAAO,CAACvC;IACrB;IAEA,OAAO;AACT;AAEO,SAAShC,0CACdxC,EAAc,EACdoH,SAA0B;IAE1B,KAAK,IAAI3E,YAAY2E,UACnB,IAAI;QACF,IAAIpH,GAAG+B,QAAQ,CAACU,UAAU7C,MAAM,IAC9B,OAAO6C;IAEX,EAAE,OAAOmB,KAAK;IACZ,SAAA;IAAA;AAGN;;;APlDA,yEAAA;AACA,yBAAA;AAEA,MAAMlD,iCAAW9B,CAAAA,GAAAA,qBAAAA,EACf+B,QAAQC,QAAQ,KAAK,UAAUZ,CAAAA,GAAAA,gEAAAA,EAAGU,QAAQ,GAAGV,CAAAA,GAAAA,gEAAAA,EAAGU,QAAQ,CAACG,MAC3D;AACA,MAAMC,8BAAQH,QAAQI,QAAQ,CAACC,GAAG,IAAI;AAEtC,SAASC;IACP,6DAAA;IACA,MAAMC,cAAc;QAAC;QAAW;KAAsB,CAAC1B,IAAI,CAAC;IAE5D,aAAA;IACA,OAAO2B,QAAQD;AACjB;AAEO,MAAME;IACXC,WAAgBzC,CAAAA,GAAAA,qBAAAA,EAAUoB,CAAAA,GAAAA,gEAAAA,EAAGqB,QAAQ,EAArCA;IACAC,WAAgB1C,CAAAA,GAAAA,qBAAAA,EAAUoB,CAAAA,GAAAA,gEAAAA,EAAGsB,QAAQ,EAArCA;IACA3B,OAAYf,CAAAA,GAAAA,qBAAAA,EAAUoB,CAAAA,GAAAA,gEAAAA,EAAGL,IAAI,EAA7BA;IACA4B,QAAa3C,CAAAA,GAAAA,qBAAAA,EAAUoB,CAAAA,GAAAA,gEAAAA,EAAGuB,KAAK,EAA/BA;IACAlC,UAAeT,CAAAA,GAAAA,qBAAAA,EAAUoB,CAAAA,GAAAA,gEAAAA,EAAGX,OAAO,EAAnCA;IACAmC,UAAe5C,CAAAA,GAAAA,qBAAAA,EAAUoB,CAAAA,GAAAA,gEAAAA,EAAGwB,OAAO,EAAnCA;IACAC,WAAgB7C,CAAAA,GAAAA,qBAAAA,EAAUoB,CAAAA,GAAAA,gEAAAA,EAAGyB,QAAQ,EAArCA;IACAC,SAAc9C,CAAAA,GAAAA,qBAAAA,EAAUoB,CAAAA,GAAAA,gEAAAA,EAAG0B,MAAM,EAAjCA;IACAC,SAAc/C,CAAAA,GAAAA,qBAAAA,EAAUoB,CAAAA,GAAAA,gEAAAA,EAAG2B,MAAM,EAAjCA;IACA7C,MAAWF,CAAAA,GAAAA,qBAAAA,EAAUE,CAAAA,GAAAA,gEAAAA,GAArBA;IACAe,mBACEG,CAAAA,GAAAA,gEAAAA,EAAGH,gBAAgB,CADrBA;IAEA+B,MAAoBA,IAAMjB,QAAQiB,GAAG,GAArCA;IACAC,QAAqCC,CAAAA,YAAanB,QAAQkB,KAAK,CAACC,WAAhED;IAEAE,WAAoCrD,CAAAA,OAAQsB,CAAAA,GAAAA,gEAAAA,EAAG+B,QAAQ,CAACrD,MAAxDqD;IACAC,YAAqCtD,CAAAA,OAAQsB,CAAAA,GAAAA,gEAAAA,EAAGgC,SAAS,CAACtD,MAA1DsD;IACAC,eACEtB,QAAQC,QAAQ,KAAK,UAAUZ,CAAAA,GAAAA,gEAAAA,EAAGiC,YAAY,GAAGjC,CAAAA,GAAAA,gEAAAA,EAAGiC,YAAY,CAACpB,MAAM,CADzEoB;IAEAC,eAAqBlC,CAAAA,GAAAA,gEAAAA,EAAGkC,YAAY,CAApCA;IACAC,aAAwCnC,CAAAA,GAAAA,gEAAAA,EAAGmC,UAAU,CAArDA;IACAC,cAAoBpC,CAAAA,GAAAA,gEAAAA,EAAGoC,WAAW,CAAlCA;IACAC,mBAAwBvB,8BACpB,CAAC,GAAGwB,OAAS7B,0CAA0B,IAAI,KAAK6B,QAChD9B,mCAFJ6B;IAGAE,iBAAsBzB,8BAClB,CAAC,GAAGwB,OAAS7B,0CAAwB,IAAI,KAAK6B,QAC9C9B,iCAFJ+B;IAGAC,gBAAqB1B,8BACjB,CAAC,GAAGwB,OAAS7B,0CAAuB,IAAI,KAAK6B,QAC7C9B,gCAFJgC;IAIAlC,UAA0B;QACxB,OAAOD,CAAAA,GAAAA,wCAAAA,EAAe,wBAClBY,6CACAX,CAAAA,GAAAA,8CAAAA;IACN;IAEAR,kBAAkB2C,QAAgB,EAAEC,OAAY,EAAY;QAC1D,gCAAA;QACA,IAAIC,cAAcC,sCAAgBH;QAClC,IAAII,SAAS;QAEb,MAAMC,OAAO;YACX,IAAI,CAACD,QACH,IAAI;gBACF,MAAM7C,CAAAA,GAAAA,gEAAAA,EAAG+C,QAAQ,CAACC,MAAM,CAACL,aAAaF;YACxC,EAAE,OAAOQ,GAAG;gBACV,0DAAA;gBACA,gEAAA;gBACA,IACEtC,QAAQC,QAAQ,KAAK,WACrBqC,EAAEC,OAAO,IACTD,EAAEC,OAAO,KAAK,YACdD,EAAEE,IAAI,IACNF,EAAEE,IAAI,KAAK,SACX;oBACA,IAAI,CAACC,SAASC,WAAW,GAAG,MAAMC,QAAQC,GAAG,CAAC;wBAC5CnD,CAAAA,GAAAA,2BAAAA,EAAS,IAAI,EAAEuC;wBACfvC,CAAAA,GAAAA,2BAAAA,EAAS,IAAI,EAAEqC;qBAChB;oBAED,MAAM,IAAI,CAACf,MAAM,CAACiB;oBAElB,IAAIS,WAAWC,YACb,MAAMJ;gBAEV;YACF;QAEJ;QAEA,IAAIO,cAAcxD,CAAAA,GAAAA,gEAAAA,EAAGF,iBAAiB,CAAC6C,aAAa;YAClD,GAAGD,OAAO;YACV1C,IAAI;gBACF,GAAGA,CAAAA,GAAAA,gEAAAA,CAAE;gBACLyD,OAAOA,CAACC,IAAIC;oBACV3D,CAAAA,GAAAA,gEAAAA,EAAGyD,KAAK,CAACC,IAAIE,CAAAA;wBACX,IAAIA,KACFD,GAAGC;6BAEHd,OAAOe,IAAI,CACT,IAAMF,MACNC,CAAAA,MAAOD,GAAGC;oBAGhB;gBACF;YACF;QACF;QAEAJ,YAAYM,IAAI,CAAC,SAAS;YACxBjB,SAAS;YACT7C,CAAAA,GAAAA,gEAAAA,EAAG+D,UAAU,CAACpB;QAChB;QAEA,OAAOa;IACT;IAEA,MAAMQ,UACJvB,QAAkB,EAClBwB,QAAyB,EACzBvB,OAAqB,EACN;QACf,IAAIC,cAAcC,sCAAgBH;QAClC,MAAMzC,CAAAA,GAAAA,gEAAAA,EAAG+C,QAAQ,CAACiB,SAAS,CAACrB,aAAasB,UAAUvB;QACnD,MAAM1C,CAAAA,GAAAA,gEAAAA,EAAG+C,QAAQ,CAACC,MAAM,CAACL,aAAaF;IACxC;IAEAyB,aAAazB,QAAkB,EAAE0B,QAAmB,EAAO;QACzD,IAAIA,YAAY,MACd,OAAOnE,CAAAA,GAAAA,gEAAAA,EAAGkE,YAAY,CAACzB,UAAU0B;QAEnC,OAAOnE,CAAAA,GAAAA,gEAAAA,EAAGkE,YAAY,CAACzB;IACzB;IAEA,MAAM/B,SAAS0D,YAAoB,EAAmB;QACpD,IAAI;YACF,OAAO,MAAM1D,+BAAS0D,cAAc;QACtC,EAAE,OAAOnB,GAAG;QACV,aAAA;QAAA;QAGF,OAAOmB;IACT;IAEAC,OAAO5B,QAAkB,EAAoB;QAC3C,OAAO,IAAIa,QAAQgB,CAAAA;YACjBtE,CAAAA,GAAAA,gEAAAA,EAAGqE,MAAM,CAAC5B,UAAU6B;QACtB;IACF;IAEAC,MACEC,GAAa,EACbC,EAAgD,EAChDC,IAAoB,EACQ;QAC5B,OAAO,IAAI,CAACpE,OAAO,GAAGqE,SAAS,CAACH,KAAKC,IAAIC;IAC3C;IAEAE,eACEJ,GAAa,EACbK,QAAkB,EAClBH,IAAoB,EACG;QACvB,OAAO,IAAI,CAACpE,OAAO,GAAGsE,cAAc,CAACJ,KAAKK,UAAUH;IACtD;IAEA,MAAMI,cACJN,GAAa,EACbK,QAAkB,EAClBH,IAAoB,EACL;QACf,MAAM,IAAI,CAACpE,OAAO,GAAGwE,aAAa,CAACN,KAAKK,UAAUH;IACpD;IAEA,OAAOK,cAAsB;QAC3B,OAAO,IAAI3D;IACb;IAEA4D,YAAkB;QAChB,OAAO;IACT;IAEA,MAAM7F,OAAOsD,QAAkB,EAAiB;QAC9C,MAAMxC,CAAAA,GAAAA,mCAAAA,EAAS8C,QAAQ,CAACkC,KAAK,CAACxC,UAAU;YAACyC,WAAW;QAAI;IAC1D;IAEA,MAAMC,OAAO1C,QAAkB,EAAiB;QAC9C,IAAIzC,CAAAA,GAAAA,gEAAAA,EAAG+C,QAAQ,CAACqC,EAAE,EAAE;YAClB,MAAMpF,CAAAA,GAAAA,gEAAAA,EAAG+C,QAAQ,CAACqC,EAAE,CAAC3C,UAAU;gBAACyC,WAAW;gBAAMG,OAAO;YAAI;YAC5D;QACF;QAEA,gDAAA;QACA,IAAI1F;QACJ,IAAI;YACFA,OAAO,MAAM,IAAI,CAACA,IAAI,CAAC8C;QACzB,EAAE,OAAOmB,KAAK;YACZ;QACF;QAEA,IAAIjE,KAAKI,WAAW,IAClB,aAAA;QACA,MAAME,CAAAA,GAAAA,mCAAAA,EAAS8C,QAAQ,CAACuC,KAAK,CAAC7C,UAAU;YAACyC,WAAW;QAAI;aAExD,MAAMjF,CAAAA,GAAAA,mCAAAA,EAAS8C,QAAQ,CAACrB,MAAM,CAACe;IAEnC;AACF;AAEAtC,CAAAA,GAAAA,2CAAAA,EAA0B,GAAGI,CAAAA,GAAAA,gEAAAA,EAAYgF,OAAO,CAAA,OAAA,CAAS,EAAEnE;AAE3D,IAAIoE,yCAAmB;AAEvB,IAAIC;;AACJ,IAAI;IACD,CAAA,YAACA,8BAAAA,EAAS,GAAGtE,yCAAwB;AACxC,EAAE,OAAM;AACN,EAAA;AAAA;AAGF,IAAIuE;AAEJ,SAASC,wCAAkBlD,QAAQ;IACjC,IAAIiD,qCAAe,MACjB,OAAOA;IAET,IAAI;QACF,MAAME,SAAS1F,CAAAA,GAAAA,gBAAAA;QACfD,CAAAA,GAAAA,mCAAAA,EAAS4F,UAAU,CACjBD,QACA3F,CAAAA,GAAAA,mCAAAA,EAAS6F,SAAS,CAACC,IAAI,GAAG9F,CAAAA,GAAAA,mCAAAA,EAAS6F,SAAS,CAACE,IAC/C;QACA,MAAMC,cAAchG,CAAAA,GAAAA,mCAAAA,EAAS8B,QAAQ,CAAC6D;QACtC,MAAMM,gBAAgBjG,CAAAA,GAAAA,mCAAAA,EAAS8B,QAAQ,CAACU;QACxC,qEAAA;QACA,8DAAA;QACAiD,oCAAcO,YAAYE,GAAG,KAAKD,cAAcC,GAAG;IACrD,EAAE,OAAOlD,GAAG;QACV,0DAAA;QACAyC,oCAAc;IAChB;IACA,OAAOA;AACT;AAEA,mEAAA;AACA,SAAS9C,sCAAgBH,QAAkB;IACzC+C,yCAAmBA,yCAAmBY,OAAOC,gBAAgB;IAE7D,IAAI1D,cAAcF;IAElB,0DAAA;IACA,qFAAA;IACA,IAAIkD,wCAAkBlD,WACpBE,cAAcjE,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACU,CAAAA,GAAAA,gBAAAA,KAAUxB,CAAAA,GAAAA,qCAAAA,EAAK4H,QAAQ,CAAC7D;IAGlD,OACEE,cACA,MACAhC,QAAQ4F,GAAG,GACVd,CAAAA,kCAAY,OAAO,MAAMA,iCAAW,EAAA,IACrC,MACA,AAACD,CAAAA,wCAAgB,EAAIgB,QAAQ,CAAC;AAElC;;;;;;;;;;;;;;;;ASrSA;AAEA,SAAS,iCAAW,CAAC,EAAE,OAAO;IAC5B,IAAI,KAAK,MACP,OAAO;IAET,IAAI,QAAQ,IAAI,MAAM,YAAY,YAAY,UAAU,oBAAoB;IAC5E,MAAM,WAAW,GAAG,GAAG,qCAAqC;IAC5D,MAAM;AACR;AAEA,4BAAiB;AACjB,0BAAe,OAAO,GAAG;AAEzB,OAAO,cAAc,CAAC,2BAAgB,cAAc;IAAC,OAAO;AAAI;;;;;ADWhE,MAAMoB,kCAAmC,IAAIC;AAC7C,IAAIC,2BAAK;AAqBF,MAAMC;IAYXC,sBAA8B,EAA9BA;IAEAC,0BAAkD,EAAE,CAApDA;IACAC,WAAyB,IAAIP,CAAAA,GAAAA,uCAAAA,IAA7BO;IAEAC,YAAYC,UAAsB,CAAE;QAClC,IAAI,CAACC,IAAI,GAAGD;QACZ,IAAI,CAACE,IAAI,GAAG5J,CAAAA,GAAAA,qCAAAA,EAAK4F,OAAO,CAAC5F,CAAAA,GAAAA,qCAAAA,EAAK6J,GAAG;QACjC,IAAI,CAACC,IAAI,GAAG,IAAIX,IAAI;YAAC;gBAAC,IAAI,CAACS,IAAI;gBAAE,IAAIG;aAAY;SAAC;QAClD,IAAI,CAACrJ,KAAK,GAAG,IAAIyI;QACjB,IAAI,CAACa,QAAQ,GAAG,IAAIb;QACpB,IAAI,CAACc,QAAQ,GAAG,IAAId;QACpB,IAAI,CAACe,MAAM,GAAG,EAAE;QAChB,IAAI,CAACd,EAAE,GAAGA;QACV,IAAI,CAACe,cAAc,GAAG,EAAE;QACxB,IAAI,CAACC,WAAW,GAAG,EAAE;QACrBlB,gCAAUmB,GAAG,CAAC,IAAI,CAACjB,EAAE,EAAE,IAAI;QAC3B,IAAI,CAACI,QAAQ,CAACc,EAAE,CAAC,wBAAwB;YACvC,KAAK,IAAI1E,WAAW,IAAI,CAAC2D,uBAAuB,CAC9C3D;YAEF,IAAI,CAAC2D,uBAAuB,GAAG,EAAE;QACnC;IACF;IAEA,OAAOlD,YAAYL,IAAwB,EAAuB;QAChE,IAAIuE,WAAWrB,gCAAUsB,GAAG,CAACxE,KAAKoD,EAAE;QACpC,IAAImB,YAAY,MAAM;YACpB,8FAAA;YACAzB,CAAAA,GAAAA,8CAAAA,EAAW2B,YAAY,GAAGC,SAAS,CAAC1E,KAAK2E,MAAM,EAAE;gBAC/C;gBACA,EAAE;aACH;YACD,OAAOJ;QACT;QAEA,IAAIjJ,KAAK,IAAIsJ,+BAAS5E,KAAKoD,EAAE,EAAEJ,CAAAA,GAAAA,gEAAAA,EAAWhD,KAAK2E,MAAM;QACrDrJ,GAAGwI,IAAI,GAAG9D,KAAK8D,IAAI;QACnBxI,GAAGZ,KAAK,GAAGsF,KAAKtF,KAAK;QACrBY,GAAG0I,QAAQ,GAAGhE,KAAKgE,QAAQ;QAC3B,OAAO1I;IACT;IAEAgF,YAAgC;QAC9B,IAAI,CAAC,IAAI,CAACqE,MAAM,EACd,IAAI,CAACA,MAAM,GAAG,IAAI,CAAChB,IAAI,CAACkB,mBAAmB,CACzC,CAAC9E,IAAYnC;YACX,aAAA;YACA,OAAO,IAAI,CAACmC,GAAG,IAAInC;QACrB;QAIJ,qEAAA;QACA,IAAI,CAAC0F,mBAAmB;QAExB,OAAO;YACLwB,OAAO;YACP1B,IAAI,IAAI,CAACA,EAAE;YACXuB,QAAQ,IAAI,CAACA,MAAM;YACnBb,MAAM,IAAI,CAACA,IAAI;YACfpJ,OAAO,IAAI,CAACA,KAAK;YACjBsJ,UAAU,IAAI,CAACA,QAAfA;QACF;IACF;IAEAe,0BAA0B;QACxB,IAAI,CAACzB,mBAAmB;QACxB,IAAI,IAAI,CAACA,mBAAmB,KAAK,IAAI,CAACa,cAAc,CAACa,MAAM,EACzD,IAAI,CAACxB,QAAQ,CAACyB,IAAI,CAAC;IAEvB;IAEA/H,MAAgB;QACd,OAAO,IAAI,CAAC0G,IAAI;IAClB;IAEAzG,MAAM2C,GAAa,EAAE;QACnB,IAAI,CAAC8D,IAAI,GAAG9D;IACd;IAEAoF,eAAenH,QAAkB,EAAE/B,WAAoB,IAAI,EAAY;QACrE+B,WAAW/D,CAAAA,GAAAA,qCAAAA,EAAKmL,SAAS,CAACpH;QAC1B,IAAI,CAACA,SAASqH,UAAU,CAAC,IAAI,CAAClI,GAAG,KAC/Ba,WAAW/D,CAAAA,GAAAA,qCAAAA,EAAK4F,OAAO,CAAC,IAAI,CAAC1C,GAAG,IAAIa;QAGtC,qCAAA;QACA,IAAI,QAACqE,IAAI,OAAEtC,GAAG,QAAEuF,IAAAA,EAAK,GAAGrL,CAAAA,GAAAA,qCAAAA,EAAKkI,KAAK,CAACnE;QACnC,IAAIuH,QAAQxF,IAAIyF,KAAK,CAACnD,KAAK4C,MAAM,EAAEQ,KAAK,CAACxL,CAAAA,GAAAA,qCAAAA,EAAK6J,GAAG,EAAE4B,MAAM,CAACJ;QAE1D,kEAAA;QACA,IAAIK;QACJ,IAAI,CAAC1J,UAAU;YACb0J,OAAOJ,KAAK,CAACA,MAAMN,MAAM,GAAG,EAAE;YAC9BM,QAAQA,MAAMC,KAAK,CAAC,GAAG;QACzB;QAEA,IAAII,MAAMvD;QACV,KAAK,IAAIwD,QAAQN,MAAO;YACtBK,MAAM3L,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAAC6K,KAAKC;YACrB,IAAI9I,UAAU,IAAI,CAACkH,QAAQ,CAACQ,GAAG,CAACmB;YAChC,IAAI7I,SACF6I,MAAM7I;QAEV;QAEA,IAAI4I,MACFC,MAAM3L,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAAC6K,KAAKD;QAGvB,OAAOC;IACT;IAEA,MAAMrG,UACJvB,QAAkB,EAClBwB,QAAyB,EACzBvB,OAAsB,EACtB;QACAD,WAAW,IAAI,CAACmH,cAAc,CAACnH;QAC/B,IAAI,IAAI,CAAC+F,IAAI,CAAC+B,GAAG,CAAC9H,WAChB,MAAM,IAAI+H,0CAAQ,UAAU/H,UAAU;QAGxC,IAAI+B,MAAM9F,CAAAA,GAAAA,qCAAAA,EAAKqI,OAAO,CAACtE;QACvB,IAAI,CAAC,IAAI,CAAC+F,IAAI,CAAC+B,GAAG,CAAC/F,MACjB,MAAM,IAAIgG,0CAAQ,UAAUhG,KAAK;QAGnC,IAAIiG,SAASC,0CAAWzG;QACxB,IAAI3E,OAAO,IAAI,CAACF,KAAK,CAAC8J,GAAG,CAACzG;QAC1B,IAAIkI,OAAQjI,WAAWA,QAAQiI,IAAI,IAAK;QACxC,IAAIrL,MAAM;YACRA,KAAKsL,KAAK,CAACH,QAAQE;YACnB,IAAI,CAACvL,KAAK,CAAC2J,GAAG,CAACtG,UAAUnD;QAC3B,OACE,IAAI,CAACF,KAAK,CAAC2J,GAAG,CAACtG,UAAU,IAAIoI,0CAAKJ,QAAQE;QAG5C,MAAM,IAAI,CAACG,gBAAgB,CAAC;YAC1BC,MAAM;YACNrM,MAAM+D;YACNuI,OAAO,IAAI,CAAC5L,KAAK,CAAC8J,GAAG,CAACzG;QACxB;QAEA,IAAI,CAACwI,aAAa,CAAC;YACjBF,MAAMzL,OAAO,WAAW;YACxBZ,MAAM+D;QACR;IACF;IAEA,yCAAA;IACA,MAAMpB,SAASoB,QAAkB,EAAE0B,QAAmB,EAAgB;QACpE,OAAO,IAAI,CAACD,YAAY,CAACzB,UAAU0B;IACrC;IAEAD,aAAazB,QAAkB,EAAE0B,QAAmB,EAAO;QACzD1B,WAAW,IAAI,CAACmH,cAAc,CAACnH;QAC/B,IAAInD,OAAO,IAAI,CAACF,KAAK,CAAC8J,GAAG,CAACzG;QAC1B,IAAInD,QAAQ,MACV,MAAM,IAAIkL,0CAAQ,UAAU/H,UAAU;QAGxC,IAAIgI,SAASnL,KAAK4L,IAAI;QACtB,IAAI/G,UACF,OAAOsG,OAAOjE,QAAQ,CAACrC;QAGzB,OAAOsG;IACT;IAEA,MAAMnJ,SAAStC,MAAgB,EAAEE,WAAqB,EAAE;QACtD,IAAI+E,WAAW,MAAM,IAAI,CAAC5C,QAAQ,CAACrC;QACnC,MAAM,IAAI,CAACgF,SAAS,CAAC9E,aAAa+E;IACpC;IAEAlC,SAASU,QAAkB,EAAQ;QACjCA,WAAW,IAAI,CAACmH,cAAc,CAACnH;QAE/B,IAAI+B,MAAM,IAAI,CAACgE,IAAI,CAACU,GAAG,CAACzG;QACxB,IAAI+B,KACF,OAAOA,IAAI7E,IAAI;QAGjB,IAAIL,OAAO,IAAI,CAACF,KAAK,CAAC8J,GAAG,CAACzG;QAC1B,IAAInD,QAAQ,MACV,MAAM,IAAIkL,0CAAQ,UAAU/H,UAAU;QAGxC,OAAOnD,KAAKK,IAAI;IAClB;IAEA,yCAAA;IACA,MAAMA,KAAK8C,QAAkB,EAAiB;QAC5C,OAAO,IAAI,CAACV,QAAQ,CAACU;IACvB;IAEAT,UAAUS,QAAkB,EAAQ;QAClCA,WAAW,IAAI,CAACmH,cAAc,CAACnH,UAAU;QAEzC,IAAI,IAAI,CAACiG,QAAQ,CAAC6B,GAAG,CAAC9H,WAAW;YAC/B,IAAI9C,OAAO,IAAIwL;YACfxL,KAAKgL,IAAI,GAAGS;YACZ,OAAOzL;QACT;QAEA,IAAI6E,MAAM,IAAI,CAACgE,IAAI,CAACU,GAAG,CAACzG;QACxB,IAAI+B,KACF,OAAOA,IAAI7E,IAAI;QAGjB,IAAIL,OAAO,IAAI,CAACF,KAAK,CAAC8J,GAAG,CAACzG;QAC1B,IAAInD,QAAQ,MACV,MAAM,IAAIkL,0CAAQ,UAAU/H,UAAU;QAGxC,OAAOnD,KAAKK,IAAI;IAClB;IAEA,yCAAA;IACA,MAAM4B,MAAMkB,QAAkB,EAAiB;QAC7C,OAAO,IAAI,CAACT,SAAS,CAACS;IACxB;IAEAL,YAAYoC,GAAa,EAAEE,IAAqB,EAAO;QACrDF,MAAM,IAAI,CAACoF,cAAc,CAACpF;QAC1B,IAAI,CAAC,IAAI,CAACgE,IAAI,CAAC+B,GAAG,CAAC/F,MACjB,MAAM,IAAIgG,0CAAQ,UAAUhG,KAAK;QAGnC,IAAI,CAACA,IAAI6G,QAAQ,CAAC3M,CAAAA,GAAAA,qCAAAA,EAAK6J,GAAG,GACxB/D,OAAO9F,CAAAA,GAAAA,qCAAAA,EAAK6J,GAAG;QAGjB,IAAI8B,MAAM,EAAE;QACZ,KAAK,IAAI,CAAC5H,UAAUuI,MAAM,IAAI,IAAI,CAACxC,IAAI,CAAE;YACvC,IAAI/F,aAAa+B,KACf;YAEF,IACE/B,SAASqH,UAAU,CAACtF,QACpB/B,SAAS6I,OAAO,CAAC5M,CAAAA,GAAAA,qCAAAA,EAAK6J,GAAG,EAAE/D,IAAIkF,MAAM,MAAM,IAC3C;gBACA,IAAI6B,OAAO9I,SAASwH,KAAK,CAACzF,IAAIkF,MAAM;gBACpC,IAAIhF,MAAM8G,eACRnB,IAAIoB,IAAI,CAAC,IAAIC,6BAAOH,MAAMP;qBAE1BX,IAAIoB,IAAI,CAACF;YAEb;QACF;QAEA,KAAK,IAAI,CAAC9I,UAAUuI,MAAM,IAAI,IAAI,CAAC5L,KAAK,CACtC,IACEqD,SAASqH,UAAU,CAACtF,QACpB/B,SAAS6I,OAAO,CAAC5M,CAAAA,GAAAA,qCAAAA,EAAK6J,GAAG,EAAE/D,IAAIkF,MAAM,MAAM,IAC3C;YACA,IAAI6B,OAAO9I,SAASwH,KAAK,CAACzF,IAAIkF,MAAM;YACpC,IAAIhF,MAAM8G,eACRnB,IAAIoB,IAAI,CAAC,IAAIC,6BAAOH,MAAMP;iBAE1BX,IAAIoB,IAAI,CAACF;QAEb;QAGF,KAAK,IAAI,CAACI,KAAK,IAAI,IAAI,CAACjD,QAAQ,CAC9B,IAAIiD,KAAK7B,UAAU,CAACtF,QAAQmH,KAAKL,OAAO,CAAC5M,CAAAA,GAAAA,qCAAAA,EAAK6J,GAAG,EAAE/D,IAAIkF,MAAM,MAAM,IAAI;YACrE,IAAI6B,OAAOI,KAAK1B,KAAK,CAACzF,IAAIkF,MAAM;YAChC,IAAIhF,MAAM8G,eACRnB,IAAIoB,IAAI,CAAC,IAAIC,6BAAOH,MAAM;gBAACZ,MAAMS;YAAO;iBAExCf,IAAIoB,IAAI,CAACF;QAEb;QAGF,OAAOlB;IACT;IAEA,yCAAA;IACA,MAAMhL,QAAQmF,GAAa,EAAEE,IAAqB,EAAgB;QAChE,OAAO,IAAI,CAACtC,WAAW,CAACoC,KAAKE;IAC/B;IAEA,MAAMhD,OAAOe,QAAkB,EAAiB;QAC9CA,WAAW,IAAI,CAACmH,cAAc,CAACnH;QAC/B,IAAI,CAAC,IAAI,CAACrD,KAAK,CAACmL,GAAG,CAAC9H,aAAa,CAAC,IAAI,CAAC+F,IAAI,CAAC+B,GAAG,CAAC9H,WAC9C,MAAM,IAAI+H,0CAAQ,UAAU/H,UAAU;QAGxC,IAAI,CAACrD,KAAK,CAACwM,MAAM,CAACnJ;QAClB,IAAI,CAAC+F,IAAI,CAACoD,MAAM,CAACnJ;QACjB,IAAI,CAACkG,QAAQ,CAACiD,MAAM,CAACnJ;QAErB,MAAM,IAAI,CAACqI,gBAAgB,CAAC;YAC1BC,MAAM;YACNrM,MAAM+D;QACR;QAEA,IAAI,CAACwI,aAAa,CAAC;YACjBF,MAAM;YACNrM,MAAM+D;QACR;QAEA,OAAOa,QAAQgB,OAAO;IACxB;IAEA,MAAMnF,OAAOqF,GAAa,EAAiB;QACzCA,MAAM,IAAI,CAACoF,cAAc,CAACpF;QAC1B,IAAI,IAAI,CAACgE,IAAI,CAAC+B,GAAG,CAAC/F,MAChB,OAAOlB,QAAQgB,OAAO;QAGxB,IAAI,IAAI,CAAClF,KAAK,CAACmL,GAAG,CAAC/F,MACjB,MAAM,IAAIgG,0CAAQ,UAAUhG,KAAK;QAGnC,IAAIsC,OAAOpI,CAAAA,GAAAA,qCAAAA,EAAKkI,KAAK,CAACpC,KAAKsC,IAAI;QAC/B,MAAOtC,QAAQsC,KAAM;YACnB,IAAI,IAAI,CAAC0B,IAAI,CAAC+B,GAAG,CAAC/F,MAChB;YAGF,IAAI,CAACgE,IAAI,CAACO,GAAG,CAACvE,KAAK,IAAIiE;YACvB,MAAM,IAAI,CAACqC,gBAAgB,CAAC;gBAC1BC,MAAM;gBACNrM,MAAM8F;YACR;YAEA,IAAI,CAACyG,aAAa,CAAC;gBACjBF,MAAM;gBACNrM,MAAM8F;YACR;YAEAA,MAAM9F,CAAAA,GAAAA,qCAAAA,EAAKqI,OAAO,CAACvC;QACrB;QAEA,OAAOlB,QAAQgB,OAAO;IACxB;IAEA,MAAMa,OAAO1C,QAAkB,EAAiB;QAC9CA,WAAW,IAAI,CAACmH,cAAc,CAACnH;QAE/B,IAAI,IAAI,CAAC+F,IAAI,CAAC+B,GAAG,CAAC9H,WAAW;YAC3B,IAAI+B,MAAM/B,WAAW/D,CAAAA,GAAAA,qCAAAA,EAAK6J,GAAG;YAC7B,KAAK,IAAI9F,YAAY,IAAI,CAACrD,KAAK,CAACyM,IAAI,GAClC,IAAIpJ,SAASqH,UAAU,CAACtF,MAAM;gBAC5B,IAAI,CAACpF,KAAK,CAACwM,MAAM,CAACnJ;gBAClB,MAAM,IAAI,CAACqI,gBAAgB,CAAC;oBAC1BC,MAAM;oBACNrM,MAAM+D;gBACR;gBAEA,IAAI,CAACwI,aAAa,CAAC;oBACjBF,MAAM;oBACNrM,MAAM+D;gBACR;YACF;YAGF,KAAK,IAAIqJ,WAAW,IAAI,CAACtD,IAAI,CAACqD,IAAI,GAChC,IAAIC,QAAQhC,UAAU,CAACtF,MAAM;gBAC3B,IAAI,CAACgE,IAAI,CAACoD,MAAM,CAACE;gBACjB,IAAI,CAACnD,QAAQ,CAACiD,MAAM,CAACE;gBACrB,MAAM,IAAI,CAAChB,gBAAgB,CAAC;oBAC1BC,MAAM;oBACNrM,MAAM+D;gBACR;gBAEA,IAAI,CAACwI,aAAa,CAAC;oBACjBF,MAAM;oBACNrM,MAAMoN;gBACR;YACF;YAGF,KAAK,IAAIrJ,YAAY,IAAI,CAACiG,QAAQ,CAACmD,IAAI,GACrC,IAAIpJ,SAASqH,UAAU,CAACtF,MAAM;gBAC5B,IAAI,CAACkE,QAAQ,CAACkD,MAAM,CAACnJ;gBACrB,MAAM,IAAI,CAACqI,gBAAgB,CAAC;oBAC1BC,MAAM;oBACNrM,MAAM+D;gBACR;YACF;YAGF,IAAI,CAAC+F,IAAI,CAACoD,MAAM,CAACnJ;YACjB,MAAM,IAAI,CAACqI,gBAAgB,CAAC;gBAC1BC,MAAM;gBACNrM,MAAM+D;YACR;YAEA,IAAI,CAACwI,aAAa,CAAC;gBACjBF,MAAM;gBACNrM,MAAM+D;YACR;QACF,OAAO,IAAI,IAAI,CAACrD,KAAK,CAACmL,GAAG,CAAC9H,WAAW;YACnC,IAAI,CAACrD,KAAK,CAACwM,MAAM,CAACnJ;YAClB,MAAM,IAAI,CAACqI,gBAAgB,CAAC;gBAC1BC,MAAM;gBACNrM,MAAM+D;YACR;YAEA,IAAI,CAACwI,aAAa,CAAC;gBACjBF,MAAM;gBACNrM,MAAM+D;YACR;QACF;QAEA,OAAOa,QAAQgB,OAAO;IACxB;IAEA,MAAMxF,IAAIE,MAAgB,EAAEE,WAAqB,EAAE;QACjDF,SAAS,IAAI,CAAC4K,cAAc,CAAC5K;QAE7B,IAAI,IAAI,CAACwJ,IAAI,CAAC+B,GAAG,CAACvL,SAAS;YACzB,IAAI,CAAC,IAAI,CAACwJ,IAAI,CAAC+B,GAAG,CAACrL,cAAc;gBAC/B,IAAI,CAACsJ,IAAI,CAACO,GAAG,CAAC7J,aAAa,IAAIuJ;gBAC/B,MAAM,IAAI,CAACqC,gBAAgB,CAAC;oBAC1BC,MAAM;oBACNrM,MAAMQ;gBACR;gBAEA,IAAI,CAAC+L,aAAa,CAAC;oBACjBF,MAAM;oBACNrM,MAAMQ;gBACR;YACF;YAEA,IAAIsF,MAAMxF,SAASN,CAAAA,GAAAA,qCAAAA,EAAK6J,GAAG;YAC3B,KAAK,IAAIuD,WAAW,IAAI,CAACtD,IAAI,CAACqD,IAAI,GAChC,IAAIC,QAAQhC,UAAU,CAACtF,MAAM;gBAC3B,IAAIuH,WAAWrN,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACN,aAAa4M,QAAQ7B,KAAK,CAACzF,IAAIkF,MAAM;gBAC9D,IAAI,CAAC,IAAI,CAAClB,IAAI,CAAC+B,GAAG,CAACwB,WAAW;oBAC5B,IAAI,CAACvD,IAAI,CAACO,GAAG,CAACgD,UAAU,IAAItD;oBAC5B,MAAM,IAAI,CAACqC,gBAAgB,CAAC;wBAC1BC,MAAM;wBACNrM,MAAMQ;oBACR;oBACA,IAAI,CAAC+L,aAAa,CAAC;wBACjBF,MAAM;wBACNrM,MAAMqN;oBACR;gBACF;YACF;YAGF,KAAK,IAAI,CAACtJ,UAAUnD,KAAK,IAAI,IAAI,CAACF,KAAK,CACrC,IAAIqD,SAASqH,UAAU,CAACtF,MAAM;gBAC5B,IAAIuH,WAAWrN,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACN,aAAauD,SAASwH,KAAK,CAACzF,IAAIkF,MAAM;gBAC/D,IAAIrF,SAAS,IAAI,CAACjF,KAAK,CAACmL,GAAG,CAACwB;gBAC5B,IAAI,CAAC3M,KAAK,CAAC2J,GAAG,CAACgD,UAAUzM;gBACzB,MAAM,IAAI,CAACwL,gBAAgB,CAAC;oBAC1BC,MAAM;oBACNrM,MAAMqN;oBACNf,OAAO1L;gBACT;gBAEA,IAAI,CAAC2L,aAAa,CAAC;oBACjBF,MAAM1G,SAAS,WAAW;oBAC1B3F,MAAMqN;gBACR;YACF;QAEJ,OACE,MAAM,IAAI,CAACzK,QAAQ,CAACtC,QAAQE;IAEhC;IAEAW,iBAAiB4C,QAAkB,EAAc;QAC/C,OAAO,IAAIuJ,iCAAW,IAAI,EAAEvJ;IAC9B;IAEA3C,kBAAkB2C,QAAkB,EAAEC,OAAqB,EAAe;QACxE,OAAO,IAAIuJ,kCAAY,IAAI,EAAExJ,UAAUC;IACzC;IAEAT,aAAaQ,QAAkB,EAAY;QACzC,OAAO,IAAI,CAACmH,cAAc,CAACnH;IAC7B;IAEA,yCAAA;IACA,MAAM/B,SAAS+B,QAAkB,EAAqB;QACpD,OAAO,IAAI,CAACR,YAAY,CAACQ;IAC3B;IAEAP,aAAaO,QAAkB,EAAY;QACzC,IAAIjB,UAAU,IAAI,CAACkH,QAAQ,CAACQ,GAAG,CAACzG;QAChC,IAAI,CAACjB,SACH,MAAM,IAAIgJ,0CAAQ,UAAU/H,UAAU;QAExC,OAAOjB;IACT;IAEA,yCAAA;IACA,MAAMC,SAASgB,QAAkB,EAAqB;QACpD,OAAO,IAAI,CAACP,YAAY,CAACO;IAC3B;IAEA,MAAMjB,QAAQ0K,MAAgB,EAAExN,IAAc,EAAE;QAC9CwN,SAAS,IAAI,CAACtC,cAAc,CAACsC;QAC7BxN,OAAO,IAAI,CAACkL,cAAc,CAAClL;QAC3B,IAAI,CAACgK,QAAQ,CAACK,GAAG,CAACrK,MAAMwN;QACxB,MAAM,IAAI,CAACpB,gBAAgB,CAAC;YAC1BC,MAAM;kBACNrM;oBACAwN;QACF;IACF;IAEA/J,WAAWM,QAAkB,EAAW;QACtCA,WAAW,IAAI,CAACmH,cAAc,CAACnH;QAC/B,OAAO,IAAI,CAACrD,KAAK,CAACmL,GAAG,CAAC9H,aAAa,IAAI,CAAC+F,IAAI,CAAC+B,GAAG,CAAC9H;IACnD;IAEA,yCAAA;IACA,MAAM4B,OAAO5B,QAAkB,EAAoB;QACjD,OAAO,IAAI,CAACN,UAAU,CAACM;IACzB;IAEAwI,cAAckB,KAAY,EAAE;QAC1B,IAAI,CAACvD,MAAM,CAAC6C,IAAI,CAACU;QACjB,IAAI,IAAI,CAACxD,QAAQ,CAACyD,IAAI,KAAK,GACzB;QAGF,eAAA;QACA,IAAI,CAACtD,WAAW,CAAC2C,IAAI,CAACU;QACtBE,aAAa,IAAI,CAACC,aAAa;QAE/B,IAAI,CAACA,aAAa,GAAGC,WAAW;YAC9B,IAAI3D,SAAS,IAAI,CAACE,WAAW;YAC7B,IAAI,CAACA,WAAW,GAAG,EAAE;YAErB,KAAK,IAAI,CAACtE,KAAKmE,SAAS,IAAI,IAAI,CAACA,QAAQ,CAAE;gBACzC,IAAI,CAACnE,IAAI6G,QAAQ,CAAC3M,CAAAA,GAAAA,qCAAAA,EAAK6J,GAAG,GACxB/D,OAAO9F,CAAAA,GAAAA,qCAAAA,EAAK6J,GAAG;gBAGjB,IAAI4D,MAAMzN,IAAI,CAACoL,UAAU,CAACtF,MACxB,KAAK,IAAIlE,WAAWqI,SAClBrI,QAAQkM,OAAO,CAAC5D;YAGtB;QACF,GAAG;IACL;IAEA6D,gBAAgBpD,MAAc,EAAE;QAC9B,IAAI,CAACR,cAAc,CAAC4C,IAAI,CAACpC;QACzB,IAAI,IAAI,CAACrB,mBAAmB,KAAK,IAAI,CAACa,cAAc,CAACa,MAAM,EACzD,IAAI,CAACxB,QAAQ,CAACyB,IAAI,CAAC;IAEvB;IAEA,MAAMmB,iBAAiBqB,KAAkB,EAAE;QACzC,sDAAA;QACA,MAAO,IAAI,CAACtD,cAAc,CAACa,MAAM,GAAG,IAAI,CAAC1B,mBAAmB,CAC1D,MAAM,IAAI1E,QAAQgB,CAAAA,UAAW,IAAI,CAAC2D,uBAAuB,CAACwD,IAAI,CAACnH;QAGjE,MAAMhB,QAAQC,GAAG,CACf,IAAI,CAACsF,cAAc,CAAC6D,GAAG,CAACC,CAAAA,eACtB,IAAI,CAACtE,IAAI,CAACuE,SAAS,CAACxD,SAAS,CAACuD,cAAc;gBAACR;aAAM;IAGzD;IAEA5H,MACEC,GAAa,EACbC,EAAgD,EAChDC,IAAoB,EACQ;QAC5BF,MAAM,IAAI,CAACoF,cAAc,CAACpF;QAC1B,IAAIlE,UAAU,IAAIuM,8BAAQpI,IAAIC;QAC9B,IAAIiE,WAAW,IAAI,CAACA,QAAQ,CAACO,GAAG,CAAC1E;QACjC,IAAI,CAACmE,UAAU;YACbA,WAAW,IAAImE;YACf,IAAI,CAACnE,QAAQ,CAACI,GAAG,CAACvE,KAAKmE;QACzB;QAEAA,SAASoE,GAAG,CAACzM;QAEb,OAAOgD,QAAQgB,OAAO,CAAC;YACrB0I,aAAaA;gBACXrE,WAAWjB,CAAAA,GAAAA,gEAAAA,EAAWiB;gBACtBA,SAASiD,MAAM,CAACtL;gBAEhB,IAAIqI,SAASyD,IAAI,KAAK,GACpB,IAAI,CAACzD,QAAQ,CAACiD,MAAM,CAACpH;gBAGvB,OAAOlB,QAAQgB,OAAO;YACxB;QACF;IACF;IAEA,MAAMM,eACJJ,GAAa,EACbK,QAAkB,EAClBH,IAAoB,EACG;QACvB,IAAIT,WAAW,MAAM,IAAI,CAAC5C,QAAQ,CAACwD,UAAU;QAC7C,IAAIoI,MAAM7G,OAAOnC;QACjB,IAAI2E,SAAS,IAAI,CAACA,MAAM,CAACqB,KAAK,CAACgD;QAC/B,IAAIC,SAASxI,KAAKwI,MAAM;QACxB,IAAIA,QACFtE,SAASA,OAAOuE,MAAM,CACpBhB,CAAAA,QAAS,CAACe,OAAOE,IAAI,CAACC,CAAAA,IAAKlB,MAAMzN,IAAI,CAACoL,UAAU,CAACuD,IAAI3O,CAAAA,GAAAA,qCAAAA,EAAK6J,GAAG;QAIjE,OAAOK;IACT;IAEA,MAAM9D,cAAcN,GAAa,EAAEK,QAAkB,EAAiB;QACpE,MAAM,IAAI,CAACb,SAAS,CAACa,UAAU,KAAK,IAAI,CAAC+D,MAAM,CAACc,MAAM;IACxD;IAEArH,iBACE4E,SAAwB,EACxBqG,OAAiB,EACjBxG,IAAc,EACH;QACX,OAAOzE,CAAAA,GAAAA,yCAAAA,EAAiB,IAAI,EAAE4E,WAAWqG,SAASxG;IACpD;IAEAvE,eAAesE,UAAkB,EAAEyG,OAAiB,EAAa;QAC/D,OAAO/K,CAAAA,GAAAA,yCAAAA,EAAe,IAAI,EAAEsE,YAAYyG;IAC1C;IAEA9K,cAAc4E,SAA0B,EAAa;QACnD,OAAO5E,CAAAA,GAAAA,yCAAAA,EAAc,IAAI,EAAE4E;IAC7B;AACF;AAEA,MAAMyF;IAIJ1E,YACE1D,EAAgD,EAChD/B,OAAuB,CACvB;QACA,IAAI,CAAC+B,EAAE,GAAGA;QACV,IAAI,CAAC/B,OAAO,GAAGA;IACjB;IAEA8J,QAAQ5D,MAAoB,EAAE;QAC5B,IAAIsE,SAAS,IAAI,CAACxK,OAAO,CAACwK,MAAM;QAChC,IAAIA,QACFtE,SAASA,OAAOuE,MAAM,CACpBhB,CAAAA,QAAS,CAACe,OAAOE,IAAI,CAACC,CAAAA,IAAKlB,MAAMzN,IAAI,CAACoL,UAAU,CAACuD,IAAI3O,CAAAA,GAAAA,qCAAAA,EAAK6J,GAAG;QAIjE,IAAIK,OAAOc,MAAM,GAAG,GAClB,IAAI,CAACjF,EAAE,CAAC,MAAMmE;IAElB;AACF;AAEO,MAAM4B,kDAAgB+C;IAG3BpF,YAAYhF,IAAY,EAAEzE,IAAc,EAAE8O,OAAe,CAAE;QACzD,KAAK,CAAC,GAAGrK,KAAI,EAAA,EAAKzE,KAAI,CAAA,EAAI8O,SAAS;QACnC,IAAI,CAACjC,IAAI,GAAG;QACZ,IAAI,CAACpI,IAAI,GAAGA;QACZ,IAAI,CAACzE,IAAI,GAAGA;QACZ6O,MAAME,iBAAiB,GAAG,IAAI,EAAE,IAAI,CAACtF,WAAW;IAClD;AACF;AAEA,MAAM6D,yCAAmB3E,CAAAA,GAAAA,sBAAAA;IAKvBc,YAAYnI,EAAc,EAAEyC,QAAkB,CAAE;QAC9C,KAAK;QACL,IAAI,CAACzC,EAAE,GAAGA;QACV,IAAI,CAACyC,QAAQ,GAAGA;QAChB,IAAI,CAACiL,OAAO,GAAG;QACf,IAAI,CAACC,SAAS,GAAG;IACnB;IAEAC,QAAQ;QACN,IAAI,IAAI,CAACF,OAAO,EACd;QAGF,IAAI,CAACA,OAAO,GAAG;QACf,IAAI,CAAC1N,EAAE,CAACqB,QAAQ,CAAC,IAAI,CAACoB,QAAQ,EAAEoB,IAAI,CAClCwG,CAAAA;YACE,IAAI,CAACsD,SAAS,IAAItD,IAAIwD,UAAU;YAChC,IAAI,CAACpC,IAAI,CAACpB;YACV,IAAI,CAACoB,IAAI,CAAC;QACZ,GACA7H,CAAAA;YACE,IAAI,CAAC+F,IAAI,CAAC,SAAS/F;QACrB;IAEJ;AACF;AAEA,MAAMqI,0CAAoB3E,CAAAA,GAAAA,sBAAAA;IAMxBa,YAAYnI,EAAc,EAAEyC,QAAkB,EAAEC,OAAqB,CAAE;QACrE,KAAK,CAAC;YAACoL,WAAW;YAAMC,aAAa;QAAI;QACzC,IAAI,CAAC/N,EAAE,GAAGA;QACV,IAAI,CAACyC,QAAQ,GAAGA;QAChB,IAAI,CAACC,OAAO,GAAGA;QACf,IAAI,CAAC+H,MAAM,GAAGuD,OAAOC,KAAK,CAAC;IAC7B;IAEAC,OACEC,KAAsB,EACtBhK,QAAa,EACbiK,QAAiC,EACjC;QACA,IAAIC,IAAI,OAAOF,UAAU,WAAWH,OAAOrC,IAAI,CAACwC,OAAOhK,YAAYgK;QACnE,IAAI,CAAC1D,MAAM,GAAGuD,OAAO7D,MAAM,CAAC;YAAC,IAAI,CAACM,MAAM;YAAE4D;SAAE;QAC5CD;IACF;IAEAE,OAAOF,QAAiC,EAAE;QACxC,IAAI,CAACpO,EAAE,CACJgE,SAAS,CAAC,IAAI,CAACvB,QAAQ,EAAE,IAAI,CAACgI,MAAM,EAAE,IAAI,CAAC/H,OAAO,EAClDmB,IAAI,CAACuK,UACLG,KAAK,CAACH;IACX;AACF;AAEA,MAAMI,gCAAU;AAChB,MAAMC,gCAAU;AAChB,MAAMrD,gCAAU;AAChB,MAAMsD,+BAAS;AAEf,MAAMC;IAMJxG,YAAYwC,IAAY,CAAE;QACxB,IAAI,CAACA,IAAI,GAAGA;QACZ,IAAIiE,MAAMC,KAAKD,GAAG;QAClB,IAAI,CAACE,KAAK,GAAGF;QACb,IAAI,CAACG,KAAK,GAAGH;QACb,IAAI,CAACI,KAAK,GAAGJ;QACb,IAAI,CAACK,SAAS,GAAGL;IACnB;IAEAM,SAAS;QACP,IAAIN,MAAMC,KAAKD,GAAG;QAClB,IAAI,CAACE,KAAK,GAAGF;QACb,IAAI,CAACI,KAAK,GAAGJ;IACf;IAEAO,OAAOxE,IAAY,EAAE;QACnB,IAAIiE,MAAMC,KAAKD,GAAG;QAClB,IAAI,CAACG,KAAK,GAAGH;QACb,IAAI,CAACI,KAAK,GAAGJ;QACb,IAAI,CAACjE,IAAI,GAAGA;IACd;IAEAyE,UAAkB;QAChB,OAAO;IACT;IAEAzP,OAAa;QACX,OAAOwL,2BAAKkE,SAAS,CAAC,IAAI;IAC5B;AACF;AAEA,MAAMlE;IACJhF,MAAc,EAAdA;IACAmJ,MAAc,EAAdA;IACA3E,OAAe,EAAfA;IACA4E,QAAgB,EAAhBA;IACAC,MAAc,EAAdA;IACAC,MAAc,EAAdA;IACAC,OAAe,EAAfA;IACAtD,OAAe,EAAfA;IACAuD,UAAkB,EAAlBA;IACAC,SAAiB,EAAjBA;IACAC,UAAkB,EAAlBA;IACAC,UAAkB,EAAlBA;IACAC,UAAkB,EAAlBA;IACAC,cAAsB,EAAtBA;IACAlB,QAAc,IAAID,OAAlBC;IACAC,QAAc,IAAIF,OAAlBE;IACAC,QAAc,IAAIH,OAAlBG;IACAC,YAAkB,IAAIJ,OAAtBI;IAEA,OAAOI,UAAUrE,KAAY,EAAQ;QACnC,IAAIrL,OAAO,IAAIwL;QACfxL,KAAKgL,IAAI,GAAGK,MAAML,IAAI;QACtBhL,KAAKyM,IAAI,GAAGpB,MAAMoE,OAAO;QACzBzP,KAAKkQ,OAAO,GAAG7E,MAAM8D,KAAK;QAC1BnP,KAAKmQ,OAAO,GAAG9E,MAAM+D,KAAK;QAC1BpP,KAAKoQ,OAAO,GAAG/E,MAAMgE,KAAK;QAC1BrP,KAAKqQ,WAAW,GAAGhF,MAAMiE,SAAS;QAClCtP,KAAKmP,KAAK,GAAG,IAAID,KAAK7D,MAAM8D,KAAK;QACjCnP,KAAKoP,KAAK,GAAG,IAAIF,KAAK7D,MAAM+D,KAAK;QACjCpP,KAAKqP,KAAK,GAAG,IAAIH,KAAK7D,MAAMgE,KAAK;QACjCrP,KAAKsP,SAAS,GAAG,IAAIJ,KAAK7D,MAAMiE,SAAS;QACzC,OAAOtP;IACT;IAEAC,SAAkB;QAChB,OAAO,AAAC,CAAA,IAAI,CAAC+K,IAAI,GAAG6D,6BAAAA,MAAaA;IACnC;IAEAzO,cAAuB;QACrB,OAAO,AAAC,CAAA,IAAI,CAAC4K,IAAI,GAAG8D,6BAAAA,MAAaA;IACnC;IAEAwB,gBAAyB;QACvB,OAAO;IACT;IAEAC,oBAA6B;QAC3B,OAAO;IACT;IAEAC,iBAA0B;QACxB,OAAO,AAAC,CAAA,IAAI,CAACxF,IAAI,GAAG+D,4BAAAA,MAAYtD;IAClC;IAEAgF,SAAkB;QAChB,OAAO;IACT;IAEAC,WAAoB;QAClB,OAAO;IACT;AACF;AAEA,MAAM3E;IAEJ,CAAA,IAAK,CAAL;IAEAvD,YAAYoD,IAAY,EAAEP,KAA+B,CAAE;QACzD,IAAI,CAACO,IAAI,GAAGA;QACZ,IAAI,CAAC,CAAA,IAAK,GAAGP,MAAML,IAAI;IACzB;IAEA/K,SAAkB;QAChB,OAAO,AAAC,CAAA,IAAI,CAAC,CAAA,IAAK,GAAG8O,4BAAAA,MAAYF;IACnC;IAEAzO,cAAuB;QACrB,OAAO,AAAC,CAAA,IAAI,CAAC,CAAA,IAAK,GAAG2O,4BAAAA,MAAYD;IACnC;IAEAwB,gBAAyB;QACvB,OAAO;IACT;IAEAC,oBAA6B;QAC3B,OAAO;IACT;IAEAC,iBAA0B;QACxB,OAAO,AAAC,CAAA,IAAI,CAAC,CAAA,IAAK,GAAGzB,4BAAAA,MAAYtD;IACnC;IAEAgF,SAAkB;QAChB,OAAO;IACT;IAEAC,WAAoB;QAClB,OAAO;IACT;AACF;AAEO,MAAMxF,kDAAa8D;IAExBxG,YAAYsC,MAAc,EAAEE,IAAY,CAAE;QACxC,KAAK,CAAC6D,gCAAU7D;QAChB,IAAI,CAACF,MAAM,GAAGA;IAChB;IAEAS,OAAe;QACb,KAAK,CAACgE;QACN,OAAOlB,OAAOrC,IAAI,CAAC,IAAI,CAAClB,MAAM;IAChC;IAEAG,MAAMH,MAAc,EAAEE,IAAY,EAAE;QAClC,KAAK,CAACwE,OAAOX,gCAAU7D;QACvB,IAAI,CAACF,MAAM,GAAGA;IAChB;IAEA2E,UAAkB;QAChB,OAAO,IAAI,CAAC3E,MAAM,CAACoD,UAAU;IAC/B;AACF;AAEA,MAAMpF,wCAAkBkG;IACtBxG,aAAc;QACZ,KAAK,CAACsG;IACR;AACF;AAEO,SAAS/D,0CAAWzG,QAAyB;IAClD,IAAI,OAAOA,aAAa,YAAYA,SAASwG,MAAM,YAAYlD,CAAAA,GAAAA,+BAAAA,GAC7D,OAAOtD;IAGT,IAAIqM,iBAAkCrM;IACtC,aAAA;IACA,IAAItD,QAAQ4P,OAAO,EACjB,8FAAA;IACA,qEAAA;IACAD,iBACEA,0BAA0BtC,SACtBsC,iBACAtC,OAAOrC,IAAI,CAAC2E;IAGpB,IAAI5G,SAASsE,OAAOH,UAAU,CAACyC;IAC/B,IAAIE,SAAS,IAAIjJ,CAAAA,GAAAA,+BAAAA,EAAamC;IAC9B,IAAIe,SAASuD,OAAOrC,IAAI,CAAC6E;IACzB,IAAI9G,SAAS;QACX,IAAI,OAAO4G,mBAAmB,UAC5B7F,OAAOG,KAAK,CAAC0F;aAEb7F,OAAO1B,GAAG,CAACuH;;IAIf,OAAO7F;AACT;AAEA,MAAMnB,uCAAiBvB;IAIrBI,YAAYL,EAAU,EAAEuB,MAAc,CAAE;QACtC,gCAAA;QACA,aAAA;QACA,KAAK;QACL,IAAI,CAACvB,EAAE,GAAGA;QACV,IAAI,CAAC2I,QAAQ,GAAG,CAACC,YAAYpO,OAC3BkF,CAAAA,GAAAA,8CAAAA,EAAW2B,YAAY,GAAGC,SAAS,CAACC,QAAQ;gBAACqH;gBAAYpO;aAAK;QAEhE,IAAI,CAACmO,QAAQ,CAAC,mBAAmB;YAC/BjJ,CAAAA,GAAAA,8CAAAA,EAAW2B,YAAY,GAAGI,mBAAmB,CAAC4C,CAAAA;gBAC5C,OAAQA,MAAMpB,IAAI;oBAChB,KAAK;wBACH,IAAI,CAAC3L,KAAK,CAAC2J,GAAG,CAACoD,MAAMzN,IAAI,EAAEyN,MAAMnB,KAAK;wBACtC;oBACF,KAAK;wBACH,IAAI,CAAC5L,KAAK,CAACwM,MAAM,CAACO,MAAMzN,IAAI;wBAC5B,IAAI,CAAC8J,IAAI,CAACoD,MAAM,CAACO,MAAMzN,IAAI;wBAC3B,IAAI,CAACgK,QAAQ,CAACkD,MAAM,CAACO,MAAMzN,IAAI;wBAC/B;oBACF,KAAK;wBACH,IAAI,CAAC8J,IAAI,CAACO,GAAG,CAACoD,MAAMzN,IAAI,EAAE,IAAI+J;wBAC9B;oBACF,KAAK;wBACH,IAAI,CAACC,QAAQ,CAACK,GAAG,CAACoD,MAAMzN,IAAI,EAAEyN,MAAMD,MAAM;wBAC1C;gBACJ;YACF;SACD;IACH;IAEA,OAAOnH,YAAYL,IAAwB,EAAY;QACrD,OAAOgD,CAAAA,GAAAA,gEAAAA,EAAWE,gCAAUsB,GAAG,CAACxE,KAAKoD,EAAE;IACzC;IAEA9C,YAAgC;QAC9B,aAAA;QACA,OAAO;YACL8C,IAAI,IAAI,CAACA,EAATA;QACF;IACF;IAEA9D,UACEvB,QAAkB,EAClBwB,QAAyB,EACzBvB,OAAqB,EACN;QACf,KAAK,CAACsB,UAAUvB,UAAUwB,UAAUvB;QACpC,IAAI+H,SAASC,0CAAWzG;QACxB,OAAO,IAAI,CAACwM,QAAQ,CAAC,aAAa;YAAChO;YAAUgI;YAAQ/H;SAAQ;IAC/D;IAEAhB,OAAOe,QAAkB,EAAiB;QACxC,KAAK,CAACf,OAAOe;QACb,OAAO,IAAI,CAACgO,QAAQ,CAAC,UAAU;YAAChO;SAAS;IAC3C;IAEAtD,OAAOqF,GAAa,EAAiB;QACnC,KAAK,CAACrF,OAAOqF;QACb,OAAO,IAAI,CAACiM,QAAQ,CAAC,UAAU;YAACjM;SAAI;IACtC;IAEAW,OAAO1C,QAAkB,EAAiB;QACxC,KAAK,CAAC0C,OAAO1C;QACb,OAAO,IAAI,CAACgO,QAAQ,CAAC,UAAU;YAAChO;SAAS;IAC3C;IAEA3D,IAAIE,MAAgB,EAAEE,WAAqB,EAAiB;QAC1D,KAAK,CAACJ,IAAIE,QAAQE;QAClB,OAAO,IAAI,CAACuR,QAAQ,CAAC,OAAO;YAACzR;YAAQE;SAAY;IACnD;IAEAsC,QAAQ0K,MAAgB,EAAExN,IAAc,EAAiB;QACvD,KAAK,CAAC8C,QAAQ0K,QAAQxN;QACtB,OAAO,IAAI,CAAC+R,QAAQ,CAAC,WAAW;YAACvE;YAAQxN;SAAK;IAChD;AACF;AAEAyB,CAAAA,GAAAA,2CAAAA,EAA0B,GAAGI,CAAAA,GAAAA,gEAAAA,EAAYgF,OAAO,CAAA,SAAA,CAAW,EAAEwC;AAC7D5H,CAAAA,GAAAA,2CAAAA,EAA0B,GAAGI,CAAAA,GAAAA,gEAAAA,EAAYgF,OAAO,CAAA,SAAA,CAAW,EAAE+D;AAC7DnJ,CAAAA,GAAAA,2CAAAA,EAA0B,GAAGI,CAAAA,GAAAA,gEAAAA,EAAYgF,OAAO,CAAA,KAAA,CAAO,EAAE4F;AACzDhL,CAAAA,GAAAA,2CAAAA,EAA0B,GAAGI,CAAAA,GAAAA,gEAAAA,EAAYgF,OAAO,CAAA,KAAA,CAAO,EAAEsF;AACzD1K,CAAAA,GAAAA,2CAAAA,EAA0B,GAAGI,CAAAA,GAAAA,gEAAAA,EAAYgF,OAAO,CAAA,UAAA,CAAY,EAAEkD;;;;;;;;;;;;;AEniCvD,MAAMkI;IACXC,UAAyB,IAAI9D,MAA7B8D;IAKAzI,YAAY0I,cAAuC,EAAEC,QAAoB,CAAE;QACzE,IAAID,0BAA0BrJ,CAAAA,GAAAA,8CAAAA,GAC5B,IAAI,CAACuJ,QAAQ,GAAG,IAAIhJ,CAAAA,GAAAA,yCAAAA,EAAS8I;aAE7B,IAAI,CAACE,QAAQ,GAAGF;QAElB,IAAI,CAACC,QAAQ,GAAGA;QAChB,IAAI,CAACxI,IAAI,GAAGwI,SAASlP,GAAG;IAC1B;IAEA,OAAOmD,YAAYL,IAAS,EAAa;QACvC,IAAI1E,KAAK,IAAI2Q,0CAAUjM,KAAKqM,QAAQ,EAAErM,KAAKoM,QAAQ;QACnD,IAAIpM,KAAKkM,OAAO,IAAI,MAAM5Q,GAAG4Q,OAAO,GAAGlM,KAAKkM,OAAO;QACnD,OAAO5Q;IACT;IAEAgF,YAKG;QACD,OAAO;YACLwE,OAAO;YACPuH,UAAU,IAAI,CAACA,QAAQ;YACvBD,UAAU,IAAI,CAACA,QAAQ;YACvBF,SAAS,IAAI,CAACA,OAAdA;QACF;IACF;IAEAI,eAAevO,QAAkB,EAAY;QAC3CA,WAAW,IAAI,CAACmH,cAAc,CAACnH;QAC/B,IAAI,IAAI,CAACmO,OAAO,CAACrG,GAAG,CAAC9H,WACnB,MAAM,IAAI+H,8BAAQ,UAAU/H,UAAU;QAExC,OAAOA;IACT;IAEAwO,aAAaxO,QAAkB,EAAY;QACzCA,WAAW,IAAI,CAACuO,cAAc,CAACvO;QAC/B,IAAI,CAAC,IAAI,CAACN,UAAU,CAACM,WACnB,MAAM,IAAI+H,8BAAQ,UAAU/H,UAAU;QAExC,OAAOA;IACT;IAEAyO,WAAWzO,QAAkB,EAAW;QACtCA,WAAW,IAAI,CAACmH,cAAc,CAACnH;QAC/B,0DAAA;QACA,IAAI,QAACqE,IAAI,OAAEtC,GAAG,QAAEuF,IAAAA,EAAK,GAAGrL,CAAAA,GAAAA,qCAAAA,EAAKkI,KAAK,CAACnE;QACnC,IAAI0O,WAAW3M,IAAIyF,KAAK,CAACnD,KAAK4C,MAAM,EAAEQ,KAAK,CAACxL,CAAAA,GAAAA,qCAAAA,EAAK6J,GAAG,EAAE4B,MAAM,CAACJ;QAC7D,MAAOoH,SAASzH,MAAM,CAAE;YACtBjH,WAAW/D,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACsH,SAASqK;YAC9B,IAAI5F,OAAO4F,SAASC,GAAG;YACvB,IAAI,IAAI,CAACR,OAAO,CAACrG,GAAG,CAAC9H,WACnB,OAAO;iBACF,IACL,IAAI,CAACsO,QAAQ,YAAYhJ,CAAAA,GAAAA,yCAAAA,KACzB,IAAI,CAACgJ,QAAQ,CAACrI,QAAQ,CAAC6B,GAAG,CAAC9H,WAE3B,OAAO;iBACF;gBACL,gDAAA;gBACA,6DAAA;gBACA,IAAI4O,SAAS3S,CAAAA,GAAAA,qCAAAA,EAAK4F,OAAO,CAAC7B,UAAU;gBACpC,IAAI4O,WAAW5O,UACb,OAAO;gBAET,IAAI;oBACF,KAAK,IAAI6O,UAAU,IAAI,CAAClP,WAAW,CAACiP,QAAQ;wBAAC7F,eAAe;oBAAI,GAAI;wBAClE,IAAI,OAAO8F,WAAW,UACpB,OAAO,sCAAP;6BACK,IAAIA,OAAO/F,IAAI,KAAKA,MAAM;4BAC/B,IAAI+F,OAAOnB,cAAc,IACvB,OAAO;wBAEX;oBACF;gBACF,EAAE,OAAOlN,GAAG;oBACV,IAAIA,EAAEE,IAAI,KAAK,UACb,OAAO;oBAET,MAAMF;gBACR;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAMsO,kBAAkB9O,QAAkB,EAAqB;QAC7DA,WAAW,MAAM,IAAI,CAACmH,cAAc,CAACnH;QACrC,IAAIqJ,UAAUpN,CAAAA,GAAAA,qCAAAA,EAAKqI,OAAO,CAACtE;QAC3B,IAAI,IAAI,CAACN,UAAU,CAAC2J,YAAY,CAAC,IAAI,CAACiF,QAAQ,CAAC5O,UAAU,CAAC2J,UACxD,MAAM,IAAI,CAACiF,QAAQ,CAAC5R,MAAM,CAAC2M;QAE7B,OAAOrJ;IACT;IAEAmH,eAAenH,QAAkB,EAAY;QAC3C,OAAO/D,CAAAA,GAAAA,qCAAAA,EAAK4F,OAAO,CAAC,IAAI,CAAC1C,GAAG,IAAIa;IAClC;IAEA,yCAAA;IACA,MAAMpB,SAASoB,QAAkB,EAAE0B,QAAmB,EAAgB;QACpE,OAAO,IAAI,CAACD,YAAY,CAACzB,UAAU0B;IACrC;IAEA,MAAMH,UACJvB,QAAkB,EAClBwB,QAAyB,EACzBvB,OAAqB,EACN;QACfD,WAAW,MAAM,IAAI,CAAC8O,iBAAiB,CAAC9O;QACxC,MAAM,IAAI,CAACsO,QAAQ,CAAC/M,SAAS,CAACvB,UAAUwB,UAAUvB;QAClD,IAAI,CAACkO,OAAO,CAAChF,MAAM,CAACnJ;IACtB;IAEA,MAAMnB,SAAStC,MAAgB,EAAEE,WAAqB,EAAiB;QACrEF,SAAS,IAAI,CAAC4K,cAAc,CAAC5K;QAC7BE,cAAc,MAAM,IAAI,CAACqS,iBAAiB,CAACrS;QAE3C,IAAI,MAAM,IAAI,CAAC6R,QAAQ,CAAC1M,MAAM,CAACrF,SAC7B,MAAM,IAAI,CAAC+R,QAAQ,CAAC/M,SAAS,CAC3B9E,aACA,MAAM,IAAI,CAAC6R,QAAQ,CAAC1P,QAAQ,CAACrC;aAG/B,MAAM,IAAI,CAAC+R,QAAQ,CAAC/M,SAAS,CAC3B9E,aACA,MAAM,IAAI,CAAC4R,QAAQ,CAACzP,QAAQ,CAACrC;QAIjC,IAAI,CAAC4R,OAAO,CAAChF,MAAM,CAAC1M;IACtB;IAEA,yCAAA;IACA,MAAMS,KAAK8C,QAAkB,EAAsB;QACjD,OAAO,IAAI,CAACV,QAAQ,CAACU;IACvB;IAEA,yCAAA;IACA,MAAMlB,MAAMkB,QAAkB,EAAsB;QAClD,OAAO,IAAI,CAACT,SAAS,CAACS;IACxB;IAEA,MAAMjB,QAAQ0K,MAAgB,EAAEzJ,QAAkB,EAAiB;QACjEyJ,SAAS,IAAI,CAACtC,cAAc,CAACsC;QAC7BzJ,WAAW,IAAI,CAACmH,cAAc,CAACnH;QAC/B,MAAM,IAAI,CAACsO,QAAQ,CAACvP,OAAO,CAAC0K,QAAQzJ;QACpC,IAAI,CAACmO,OAAO,CAAChF,MAAM,CAACnJ;IACtB;IAEA,MAAMf,OAAOe,QAAkB,EAAiB;QAC9CA,WAAW,IAAI,CAACmH,cAAc,CAACnH;QAE/B,IAAI+O,WAAW;YAAC/O;SAAS;QAEzB,IAAI,IAAI,CAACsO,QAAQ,YAAYhJ,CAAAA,GAAAA,yCAAAA,KAAY,IAAI,CAACmJ,UAAU,CAACzO,WACvD,IAAI,CAACsO,QAAQ,CAACrI,QAAQ,CAACkD,MAAM,CAACnJ;aACzB,IAAI,IAAI,CAACV,QAAQ,CAACU,UAAU1C,WAAW,IAAI;YAChD,IAAI0R,QAAQ;gBAAChP;aAAS;YAEtB,oDAAA;YACA,MAAOgP,MAAM/H,MAAM,CAAE;gBACnB,IAAI5C,OAAOY,CAAAA,GAAAA,gEAAAA,EAAW+J,MAAML,GAAG;gBAC/B,KAAK,IAAIM,OAAO,IAAI,CAACtP,WAAW,CAAC0E,MAAM;oBAAC0E,eAAe;gBAAI,GACzD,IAAI,OAAOkG,QAAQ,UAAU;oBAC3B,IAAIC,YAAYjT,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACsH,MAAM4K;oBAChCF,SAAS/F,IAAI,CAACkG;oBACd,IAAI,IAAI,CAAC5P,QAAQ,CAAC4P,WAAW5R,WAAW,IACtC0R,MAAMhG,IAAI,CAACkG;gBAEf,OAAO;oBACL,IAAIA,YAAYjT,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACsH,MAAM4K,IAAInG,IAAI;oBACxCiG,SAAS/F,IAAI,CAACkG;oBACd,IAAID,IAAI3R,WAAW,IACjB0R,MAAMhG,IAAI,CAACkG;gBAEf;YAEJ;QACF;QAEA,IAAI;YACF,MAAM,IAAI,CAACZ,QAAQ,CAACrP,MAAM,CAACe;QAC7B,EAAE,OAAOQ,GAAG;YACV,IAAIA,EAAEE,IAAI,KAAK,YAAY,CAAC,IAAI,CAAC2N,QAAQ,CAAC3O,UAAU,CAACM,WACnD,MAAMQ;QAEV;QAEA,KAAK,IAAI2O,gBAAgBJ,SACvB,IAAI,CAACZ,OAAO,CAAC7D,GAAG,CAAC6E;IAErB;IAEA,MAAMzS,OAAOqF,GAAa,EAAiB;QACzCA,MAAM,IAAI,CAACoF,cAAc,CAACpF;QAC1B,MAAM,IAAI,CAACuM,QAAQ,CAAC5R,MAAM,CAACqF;QAE3B,IAAI,IAAI,CAACoM,OAAO,IAAI,MAAM;YACxB,IAAI9J,OAAOpI,CAAAA,GAAAA,qCAAAA,EAAKkI,KAAK,CAACpC,KAAKsC,IAAI;YAC/B,MAAOtC,QAAQsC,KAAM;gBACnB,IAAI,CAAC8J,OAAO,CAAChF,MAAM,CAACpH;gBACpBA,MAAM9F,CAAAA,GAAAA,qCAAAA,EAAKqI,OAAO,CAACvC;YACrB;QACF;IACF;IAEA,MAAMW,OAAO1C,QAAkB,EAAiB;QAC9C,IAAI;YACF,MAAM,IAAI,CAACf,MAAM,CAACe;QACpB,EAAE,OAAOQ,GAAG;QACV,OAAA;QAAA;IAEJ;IAEA,yCAAA;IACA,MAAMnE,IAAIE,MAAgB,EAAEE,WAAqB,EAAiB;QAChE,kCAAA;QACA,OAAO,IAAI,CAAC6R,QAAQ,CAACjS,GAAG,CAACE,QAAQE;IACnC;IAEAW,iBAAiB4C,QAAkB,EAAEiC,IAAmB,EAAY;QAClEjC,WAAW,IAAI,CAACuO,cAAc,CAACvO;QAC/B,IAAI,IAAI,CAACsO,QAAQ,CAAC5O,UAAU,CAACM,WAC3B,OAAO,IAAI,CAACsO,QAAQ,CAAClR,gBAAgB,CAAC4C,UAAUiC;QAGlD,OAAO,IAAI,CAACoM,QAAQ,CAACjR,gBAAgB,CAAC4C,UAAUiC;IAClD;IAEA5E,kBAAkBpB,IAAc,EAAEgG,IAAmB,EAAY;QAC/DhG,OAAO,IAAI,CAACkL,cAAc,CAAClL;QAC3B,IAAI,CAACkS,OAAO,CAAChF,MAAM,CAAClN;QACpB,OAAO,IAAI,CAACqS,QAAQ,CAACjR,iBAAiB,CAACpB,MAAMgG;IAC/C;IAEA9C,MAAgB;QACd,OAAO,IAAI,CAAC0G,IAAI;IAClB;IAEAzG,MAAMnD,IAAc,EAAQ;QAC1B,IAAI,CAAC4J,IAAI,GAAG,IAAI,CAAC2I,YAAY,CAACvS;IAChC;IAEA,yCAAA;IACA,MAAMgC,SAAS+B,QAAkB,EAAqB;QACpD,OAAO,IAAI,CAACR,YAAY,CAACQ;IAC3B;IAEAyB,aAAazB,QAAkB,EAAE0B,QAAmB,EAAO;QACzD1B,WAAW,IAAI,CAACR,YAAY,CAACQ;QAC7B,IAAI;YACF,gCAAA;YACA,OAAO,IAAI,CAACsO,QAAQ,CAAC7M,YAAY,CAACzB,UAAU0B;QAC9C,EAAE,OAAOP,KAAK;YACZ,gCAAA;YACA,OAAO,IAAI,CAACkN,QAAQ,CAAC5M,YAAY,CAACzB,UAAU0B;QAC9C;IACF;IAEApC,SAASU,QAAkB,EAAa;QACtCA,WAAW,IAAI,CAACmH,cAAc,CAACnH;QAC/B,IAAI;YACF,OAAO,IAAI,CAACsO,QAAQ,CAAChP,QAAQ,CAACU;QAChC,EAAE,OAAOQ,GAAG;YACV,IAAIA,EAAEE,IAAI,KAAK,YAAY,IAAI,CAAChB,UAAU,CAACM,WACzC,OAAO,IAAI,CAACqO,QAAQ,CAAC/O,QAAQ,CAACU;YAEhC,MAAMQ;QACR;IACF;IAEAjB,UAAUS,QAAkB,EAAa;QACvCA,WAAW,IAAI,CAACmH,cAAc,CAACnH;QAC/B,IAAI;YACF,OAAO,IAAI,CAACsO,QAAQ,CAAC/O,SAAS,CAACS;QACjC,EAAE,OAAOQ,GAAG;YACV,IAAIA,EAAEE,IAAI,KAAK,UACb,OAAO,IAAI,CAAC2N,QAAQ,CAAC9O,SAAS,CAACS;YAEjC,MAAMQ;QACR;IACF;IAEAhB,aAAaQ,QAAkB,EAAY;QACzCA,WAAW,IAAI,CAACuO,cAAc,CAACvO;QAC/BA,WAAW,IAAI,CAACuO,cAAc,CAAC,IAAI,CAACD,QAAQ,CAAC9O,YAAY,CAACQ;QAC1D,IAAI,CAAC,IAAI,CAACsO,QAAQ,CAAC5O,UAAU,CAACM,WAC5B,OAAO,IAAI,CAACqO,QAAQ,CAAC7O,YAAY,CAACQ;QAEpC,OAAOA;IACT;IAEAP,aAAaO,QAAkB,EAAY;QACzCA,WAAW,IAAI,CAACuO,cAAc,CAACvO;QAC/B,IAAI;YACF,OAAO,IAAI,CAACsO,QAAQ,CAAC7O,YAAY,CAACO;QACpC,EAAE,OAAOmB,KAAK;YACZ,OAAO,IAAI,CAACkN,QAAQ,CAAC5O,YAAY,CAACO;QACpC;IACF;IAEA,yCAAA;IACA,MAAMhB,SAASgB,QAAkB,EAAqB;QACpD,OAAO,IAAI,CAACP,YAAY,CAACO;IAC3B;IAEA,yCAAA;IACA,MAAM4B,OAAO5B,QAAkB,EAAoB;QACjD,OAAO,IAAI,CAACN,UAAU,CAACM;IACzB;IAEAN,WAAWM,QAAkB,EAAW;QACtCA,WAAW,IAAI,CAACmH,cAAc,CAACnH;QAC/B,IAAI,IAAI,CAACmO,OAAO,CAACrG,GAAG,CAAC9H,WAAW,OAAO;QAEvC,IAAI;YACFA,WAAW,IAAI,CAACR,YAAY,CAACQ;QAC/B,EAAE,OAAOmB,KAAK;YACZ,IAAIA,IAAIT,IAAI,KAAK,UAAU,MAAMS;QACnC;QAEA,IAAI,IAAI,CAACgN,OAAO,CAACrG,GAAG,CAAC9H,WAAW,OAAO;QAEvC,OACE,IAAI,CAACsO,QAAQ,CAAC5O,UAAU,CAACM,aAAa,IAAI,CAACqO,QAAQ,CAAC3O,UAAU,CAACM;IAEnE;IAEA,yCAAA;IACA,MAAMpD,QAAQX,IAAc,EAAEgG,IAAqB,EAAgB;QACjE,OAAO,IAAI,CAACtC,WAAW,CAAC1D,MAAMgG;IAChC;IAEAtC,YAAYoC,GAAa,EAAEE,IAAqB,EAAO;QACrDF,MAAM,IAAI,CAACvC,YAAY,CAACuC;QACxB,mDAAA;QACA,IAAIqN,UAAU,IAAIhK;QAElB,IAAI;YACF,KAAK,IAAImD,SAAc,IAAI,CAAC+F,QAAQ,CAAC3O,WAAW,CAACoC,KAAKE,MAAO;gBAC3D,IAAIjC,WAAW/D,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACgF,KAAKwG,MAAMO,IAAI,IAAIP;gBAC5C,IAAI,IAAI,CAAC4F,OAAO,CAACrG,GAAG,CAAC9H,WAAW;gBAChCoP,QAAQ9I,GAAG,CAACtG,UAAUuI;YACxB;QACF,EAAE,OAAM;QACN,OAAA;QAAA;QAGF,IAAI;YACF,KAAK,IAAIA,SAAc,IAAI,CAAC8F,QAAQ,CAAC1O,WAAW,CAACoC,KAAKE,MAAO;gBAC3D,IAAIjC,WAAW/D,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACgF,KAAKwG,MAAMO,IAAI,IAAIP;gBAC5C,IAAI,IAAI,CAAC4F,OAAO,CAACrG,GAAG,CAAC9H,WAAW;gBAChC,IAAIoP,QAAQtH,GAAG,CAAC9H,WAAW;gBAC3BoP,QAAQ9I,GAAG,CAACtG,UAAUuI;YACxB;QACF,EAAE,OAAM;QACN,OAAA;QAAA;QAGF,OAAO8G,MAAMnG,IAAI,CAACkG,QAAQE,MAAM;IAClC;IAEA,MAAMxN,MACJC,GAAa,EACbC,EAAgD,EAChDC,IAAoB,EACQ;QAC5B,IAAIsN,uBAAuB,MAAM,IAAI,CAACjB,QAAQ,CAACxM,KAAK,CAACC,KAAKC,IAAIC;QAC9D,IAAIuN,uBAAuB,MAAM,IAAI,CAACnB,QAAQ,CAACvM,KAAK,CAACC,KAAKC,IAAIC;QAC9D,OAAO;YACLsI,aAAa;gBACX,MAAMgF,qBAAqBhF,WAAW;gBACtC,MAAMiF,qBAAqBjF,WAAW;YACxC;QACF;IACF;IAEA,MAAMpI,eACJJ,GAAa,EACbK,QAAkB,EAClBH,IAAoB,EACG;QACvB,IAAIwN,iBAAiB,MAAM,IAAI,CAACnB,QAAQ,CAACnM,cAAc,CACrDJ,KACAK,UACAH;QAEF,IAAIyN,iBAAiB,MAAM,IAAI,CAACrB,QAAQ,CAAClM,cAAc,CACrDJ,KACAK,UACAH;QAEF,OAAO;eAAIwN;eAAmBC;SAAe;IAC/C;IAEA,MAAMrN,cACJN,GAAa,EACbK,QAAkB,EAClBH,IAAoB,EACL;QACf,MAAM,IAAI,CAACqM,QAAQ,CAACjM,aAAa,CAACN,KAAKK,UAAUH;IACnD;IAEArC,iBACE4E,SAAwB,EACxBqG,OAAiB,EACjBxG,IAAc,EACH;QACX,OAAOzE,CAAAA,GAAAA,yCAAAA,EAAiB,IAAI,EAAE4E,WAAWqG,SAASxG;IACpD;IAEAvE,eAAesE,UAAkB,EAAEyG,OAAiB,EAAa;QAC/D,OAAO/K,CAAAA,GAAAA,yCAAAA,EAAe,IAAI,EAAEsE,YAAYyG;IAC1C;IAEA9K,cAAc4E,SAA0B,EAAa;QACnD,OAAO5E,CAAAA,GAAAA,yCAAAA,EAAc,IAAI,EAAE4E;IAC7B;AACF;AAEA,MAAMoD,sCAAgB+C;IAGpBpF,YAAYhF,IAAY,EAAEzE,IAAc,EAAE8O,OAAe,CAAE;QACzD,KAAK,CAAC,GAAGrK,KAAI,EAAA,EAAKzE,KAAI,CAAA,EAAI8O,SAAS;QACnC,IAAI,CAACjC,IAAI,GAAG;QACZ,IAAI,CAACpI,IAAI,GAAGA;QACZ,IAAI,CAACzE,IAAI,GAAGA;QACZ6O,MAAME,iBAAiB,GAAG,IAAI,EAAE,IAAI,CAACtF,WAAW;IAClD;AACF;AAEAhI,CAAAA,GAAAA,2CAAAA,EAA0B,GAAGI,CAAAA,GAAAA,gEAAAA,EAAYgF,OAAO,CAAA,UAAA,CAAY,EAAEoL;;;AXvc9D,MAAM9R,iCAAkDD,CAAAA,GAAAA,qBAAAA,EACtDD,CAAAA,GAAAA,uCAAAA,EAAOE,QACT;AAGO,eAAeC,0CACpBC,QAAoB,EACpBC,MAAgB,EAChBC,aAAyB,EACzBC,WAAqB;IAErB,MAAMD,cAAcE,MAAM,CAACD;IAC3B,IAAIE,QAAQ,MAAML,SAASM,OAAO,CAACL;IACnC,KAAK,IAAIM,QAAQF,MAAO;QACtB,IAAIG,aAAab,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACR,QAAQM;QACnC,IAAIG,WAAWf,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACN,aAAaI;QACtC,IAAII,QAAQ,MAAMX,SAASY,IAAI,CAACJ;QAChC,IAAIG,MAAME,MAAM,IACd,MAAMf,+BACJE,SAASc,gBAAgB,CAACN,aAC1BN,cAAca,iBAAiB,CAACL;aAE7B,IAAIC,MAAMK,WAAW,IAC1B,MAAMjB,0CAAIC,UAAUQ,YAAYN,eAAeQ;IAEnD;AACF", "sources": ["packages/core/fs/src/index.js", "packages/core/fs/src/NodeFS.js", "node_modules/graceful-fs/graceful-fs.js", "node_modules/graceful-fs/polyfills.js", "node_modules/graceful-fs/legacy-streams.js", "node_modules/graceful-fs/clone.js", "node_modules/ncp/lib/ncp.js", "packages/core/fs/package.json", "packages/core/fs/src/find.js", "packages/core/fs/src/MemoryFS.js", "node_modules/nullthrows/nullthrows.js", "packages/core/fs/src/OverlayFS.js"], "sourcesContent": ["// @flow strict-local\nimport type {FilePath, FileSystem, FileOptions} from '@parcel/types-internal';\nimport type {Readable, Writable} from 'stream';\n\nimport path from 'path';\nimport stream from 'stream';\nimport {promisify} from 'util';\n\nexport * from './NodeFS';\nexport * from './MemoryFS';\nexport * from './OverlayFS';\n\nexport type {FileSystem, FileOptions};\n\nconst pipeline: (Readable, Writable) => Promise<void> = promisify(\n  stream.pipeline,\n);\n\n// Recursively copies a directory from the sourceFS to the destinationFS\nexport async function ncp(\n  sourceFS: FileSystem,\n  source: FilePath,\n  destinationFS: FileSystem,\n  destination: FilePath,\n) {\n  await destinationFS.mkdirp(destination);\n  let files = await sourceFS.readdir(source);\n  for (let file of files) {\n    let sourcePath = path.join(source, file);\n    let destPath = path.join(destination, file);\n    let stats = await sourceFS.stat(sourcePath);\n    if (stats.isFile()) {\n      await pipeline(\n        sourceFS.createReadStream(sourcePath),\n        destinationFS.createWriteStream(destPath),\n      );\n    } else if (stats.isDirectory()) {\n      await ncp(sourceFS, sourcePath, destinationFS, destPath);\n    }\n  }\n}\n", "// @flow\nimport type {ReadStream, Stats} from 'fs';\nimport type {Writable} from 'stream';\nimport type {\n  FilePath,\n  Encoding,\n  FileOptions,\n  FileSystem,\n} from '@parcel/types-internal';\nimport type {\n  Event,\n  Options as WatcherOptions,\n  AsyncSubscription,\n} from '@parcel/watcher';\n\nimport fs from 'graceful-fs';\nimport nativeFS from 'fs';\nimport ncp from 'ncp';\nimport path from 'path';\nimport {tmpdir} from 'os';\nimport {promisify} from 'util';\nimport {registerSerializableClass} from '@parcel/core';\nimport {hashFile} from '@parcel/utils';\nimport {getFeatureFlag} from '@parcel/feature-flags';\nimport watcher from '@parcel/watcher';\nimport packageJSON from '../package.json';\n\nimport * as searchNative from '@parcel/rust';\nimport * as searchJS from './find';\n\n// Most of this can go away once we only support Node 10+, which includes\n// require('fs').promises\n\nconst realpath = promisify(\n  process.platform === 'win32' ? fs.realpath : fs.realpath.native,\n);\nconst isPnP = process.versions.pnp != null;\n\nfunction getWatchmanWatcher(): typeof watcher {\n  // This is here to trick parcel into ignoring this require...\n  const packageName = ['@parcel', 'watcher-watchman-js'].join('/');\n\n  // $FlowFixMe\n  return require(packageName);\n}\n\nexport class NodeFS implements FileSystem {\n  readFile: any = promisify(fs.readFile);\n  copyFile: any = promisify(fs.copyFile);\n  stat: any = promisify(fs.stat);\n  lstat: any = promisify(fs.lstat);\n  readdir: any = promisify(fs.readdir);\n  symlink: any = promisify(fs.symlink);\n  readlink: any = promisify(fs.readlink);\n  unlink: any = promisify(fs.unlink);\n  utimes: any = promisify(fs.utimes);\n  ncp: any = promisify(ncp);\n  createReadStream: (path: string, options?: any) => ReadStream =\n    fs.createReadStream;\n  cwd: () => string = () => process.cwd();\n  chdir: (directory: string) => void = directory => process.chdir(directory);\n\n  statSync: (path: string) => Stats = path => fs.statSync(path);\n  lstatSync: (path: string) => Stats = path => fs.lstatSync(path);\n  realpathSync: (path: string, cache?: any) => string =\n    process.platform === 'win32' ? fs.realpathSync : fs.realpathSync.native;\n  readlinkSync: any = (fs.readlinkSync: any);\n  existsSync: (path: string) => boolean = fs.existsSync;\n  readdirSync: any = (fs.readdirSync: any);\n  findAncestorFile: any = isPnP\n    ? (...args) => searchJS.findAncestorFile(this, ...args)\n    : searchNative.findAncestorFile;\n  findNodeModule: any = isPnP\n    ? (...args) => searchJS.findNodeModule(this, ...args)\n    : searchNative.findNodeModule;\n  findFirstFile: any = isPnP\n    ? (...args) => searchJS.findFirstFile(this, ...args)\n    : searchNative.findFirstFile;\n\n  watcher(): typeof watcher {\n    return getFeatureFlag('useWatchmanWatcher')\n      ? getWatchmanWatcher()\n      : watcher;\n  }\n\n  createWriteStream(filePath: string, options: any): Writable {\n    // Make createWriteStream atomic\n    let tmpFilePath = getTempFilePath(filePath);\n    let failed = false;\n\n    const move = async () => {\n      if (!failed) {\n        try {\n          await fs.promises.rename(tmpFilePath, filePath);\n        } catch (e) {\n          // This is adapted from fs-write-stream-atomic. Apparently\n          // Windows doesn't like renaming when the target already exists.\n          if (\n            process.platform === 'win32' &&\n            e.syscall &&\n            e.syscall === 'rename' &&\n            e.code &&\n            e.code === 'EPERM'\n          ) {\n            let [hashTmp, hashTarget] = await Promise.all([\n              hashFile(this, tmpFilePath),\n              hashFile(this, filePath),\n            ]);\n\n            await this.unlink(tmpFilePath);\n\n            if (hashTmp != hashTarget) {\n              throw e;\n            }\n          }\n        }\n      }\n    };\n\n    let writeStream = fs.createWriteStream(tmpFilePath, {\n      ...options,\n      fs: {\n        ...fs,\n        close: (fd, cb) => {\n          fs.close(fd, err => {\n            if (err) {\n              cb(err);\n            } else {\n              move().then(\n                () => cb(),\n                err => cb(err),\n              );\n            }\n          });\n        },\n      },\n    });\n\n    writeStream.once('error', () => {\n      failed = true;\n      fs.unlinkSync(tmpFilePath);\n    });\n\n    return writeStream;\n  }\n\n  async writeFile(\n    filePath: FilePath,\n    contents: Buffer | string,\n    options: ?FileOptions,\n  ): Promise<void> {\n    let tmpFilePath = getTempFilePath(filePath);\n    await fs.promises.writeFile(tmpFilePath, contents, options);\n    await fs.promises.rename(tmpFilePath, filePath);\n  }\n\n  readFileSync(filePath: FilePath, encoding?: Encoding): any {\n    if (encoding != null) {\n      return fs.readFileSync(filePath, encoding);\n    }\n    return fs.readFileSync(filePath);\n  }\n\n  async realpath(originalPath: string): Promise<string> {\n    try {\n      return await realpath(originalPath, 'utf8');\n    } catch (e) {\n      // do nothing\n    }\n\n    return originalPath;\n  }\n\n  exists(filePath: FilePath): Promise<boolean> {\n    return new Promise(resolve => {\n      fs.exists(filePath, resolve);\n    });\n  }\n\n  watch(\n    dir: FilePath,\n    fn: (err: ?Error, events: Array<Event>) => mixed,\n    opts: WatcherOptions,\n  ): Promise<AsyncSubscription> {\n    return this.watcher().subscribe(dir, fn, opts);\n  }\n\n  getEventsSince(\n    dir: FilePath,\n    snapshot: FilePath,\n    opts: WatcherOptions,\n  ): Promise<Array<Event>> {\n    return this.watcher().getEventsSince(dir, snapshot, opts);\n  }\n\n  async writeSnapshot(\n    dir: FilePath,\n    snapshot: FilePath,\n    opts: WatcherOptions,\n  ): Promise<void> {\n    await this.watcher().writeSnapshot(dir, snapshot, opts);\n  }\n\n  static deserialize(): NodeFS {\n    return new NodeFS();\n  }\n\n  serialize(): null {\n    return null;\n  }\n\n  async mkdirp(filePath: FilePath): Promise<void> {\n    await nativeFS.promises.mkdir(filePath, {recursive: true});\n  }\n\n  async rimraf(filePath: FilePath): Promise<void> {\n    if (fs.promises.rm) {\n      await fs.promises.rm(filePath, {recursive: true, force: true});\n      return;\n    }\n\n    // fs.promises.rm is not supported in node 12...\n    let stat;\n    try {\n      stat = await this.stat(filePath);\n    } catch (err) {\n      return;\n    }\n\n    if (stat.isDirectory()) {\n      // $FlowFixMe\n      await nativeFS.promises.rmdir(filePath, {recursive: true});\n    } else {\n      await nativeFS.promises.unlink(filePath);\n    }\n  }\n}\n\nregisterSerializableClass(`${packageJSON.version}:NodeFS`, NodeFS);\n\nlet writeStreamCalls = 0;\n\nlet threadId;\ntry {\n  ({threadId} = require('worker_threads'));\n} catch {\n  //\n}\n\nlet useOsTmpDir;\n\nfunction shouldUseOsTmpDir(filePath) {\n  if (useOsTmpDir != null) {\n    return useOsTmpDir;\n  }\n  try {\n    const tmpDir = tmpdir();\n    nativeFS.accessSync(\n      tmpDir,\n      nativeFS.constants.R_OK | nativeFS.constants.W_OK,\n    );\n    const tmpDirStats = nativeFS.statSync(tmpDir);\n    const filePathStats = nativeFS.statSync(filePath);\n    // Check the tmpdir is on the same partition as the target directory.\n    // This is required to ensure renaming is an atomic operation.\n    useOsTmpDir = tmpDirStats.dev === filePathStats.dev;\n  } catch (e) {\n    // We don't have read/write access to the OS tmp directory\n    useOsTmpDir = false;\n  }\n  return useOsTmpDir;\n}\n\n// Generate a temporary file path used for atomic writing of files.\nfunction getTempFilePath(filePath: FilePath) {\n  writeStreamCalls = writeStreamCalls % Number.MAX_SAFE_INTEGER;\n\n  let tmpFilePath = filePath;\n\n  // If possible, write the tmp file to the OS tmp directory\n  // This reduces the amount of FS events the watcher needs to process during the build\n  if (shouldUseOsTmpDir(filePath)) {\n    tmpFilePath = path.join(tmpdir(), path.basename(filePath));\n  }\n\n  return (\n    tmpFilePath +\n    '.' +\n    process.pid +\n    (threadId != null ? '.' + threadId : '') +\n    '.' +\n    (writeStreamCalls++).toString(36)\n  );\n}\n", "var fs = require('fs')\nvar polyfills = require('./polyfills.js')\nvar legacy = require('./legacy-streams.js')\nvar clone = require('./clone.js')\n\nvar util = require('util')\n\n/* istanbul ignore next - node 0.x polyfill */\nvar gracefulQueue\nvar previousSymbol\n\n/* istanbul ignore else - node 0.x polyfill */\nif (typeof Symbol === 'function' && typeof Symbol.for === 'function') {\n  gracefulQueue = Symbol.for('graceful-fs.queue')\n  // This is used in testing by future versions\n  previousSymbol = Symbol.for('graceful-fs.previous')\n} else {\n  gracefulQueue = '___graceful-fs.queue'\n  previousSymbol = '___graceful-fs.previous'\n}\n\nfunction noop () {}\n\nfunction publishQueue(context, queue) {\n  Object.defineProperty(context, gracefulQueue, {\n    get: function() {\n      return queue\n    }\n  })\n}\n\nvar debug = noop\nif (util.debuglog)\n  debug = util.debuglog('gfs4')\nelse if (/\\bgfs4\\b/i.test(process.env.NODE_DEBUG || ''))\n  debug = function() {\n    var m = util.format.apply(util, arguments)\n    m = 'GFS4: ' + m.split(/\\n/).join('\\nGFS4: ')\n    console.error(m)\n  }\n\n// Once time initialization\nif (!fs[gracefulQueue]) {\n  // This queue can be shared by multiple loaded instances\n  var queue = global[gracefulQueue] || []\n  publishQueue(fs, queue)\n\n  // Patch fs.close/closeSync to shared queue version, because we need\n  // to retry() whenever a close happens *anywhere* in the program.\n  // This is essential when multiple graceful-fs instances are\n  // in play at the same time.\n  fs.close = (function (fs$close) {\n    function close (fd, cb) {\n      return fs$close.call(fs, fd, function (err) {\n        // This function uses the graceful-fs shared queue\n        if (!err) {\n          resetQueue()\n        }\n\n        if (typeof cb === 'function')\n          cb.apply(this, arguments)\n      })\n    }\n\n    Object.defineProperty(close, previousSymbol, {\n      value: fs$close\n    })\n    return close\n  })(fs.close)\n\n  fs.closeSync = (function (fs$closeSync) {\n    function closeSync (fd) {\n      // This function uses the graceful-fs shared queue\n      fs$closeSync.apply(fs, arguments)\n      resetQueue()\n    }\n\n    Object.defineProperty(closeSync, previousSymbol, {\n      value: fs$closeSync\n    })\n    return closeSync\n  })(fs.closeSync)\n\n  if (/\\bgfs4\\b/i.test(process.env.NODE_DEBUG || '')) {\n    process.on('exit', function() {\n      debug(fs[gracefulQueue])\n      require('assert').equal(fs[gracefulQueue].length, 0)\n    })\n  }\n}\n\nif (!global[gracefulQueue]) {\n  publishQueue(global, fs[gracefulQueue]);\n}\n\nmodule.exports = patch(clone(fs))\nif (process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH && !fs.__patched) {\n    module.exports = patch(fs)\n    fs.__patched = true;\n}\n\nfunction patch (fs) {\n  // Everything that references the open() function needs to be in here\n  polyfills(fs)\n  fs.gracefulify = patch\n\n  fs.createReadStream = createReadStream\n  fs.createWriteStream = createWriteStream\n  var fs$readFile = fs.readFile\n  fs.readFile = readFile\n  function readFile (path, options, cb) {\n    if (typeof options === 'function')\n      cb = options, options = null\n\n    return go$readFile(path, options, cb)\n\n    function go$readFile (path, options, cb, startTime) {\n      return fs$readFile(path, options, function (err) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([go$readFile, [path, options, cb], err, startTime || Date.now(), Date.now()])\n        else {\n          if (typeof cb === 'function')\n            cb.apply(this, arguments)\n        }\n      })\n    }\n  }\n\n  var fs$writeFile = fs.writeFile\n  fs.writeFile = writeFile\n  function writeFile (path, data, options, cb) {\n    if (typeof options === 'function')\n      cb = options, options = null\n\n    return go$writeFile(path, data, options, cb)\n\n    function go$writeFile (path, data, options, cb, startTime) {\n      return fs$writeFile(path, data, options, function (err) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([go$writeFile, [path, data, options, cb], err, startTime || Date.now(), Date.now()])\n        else {\n          if (typeof cb === 'function')\n            cb.apply(this, arguments)\n        }\n      })\n    }\n  }\n\n  var fs$appendFile = fs.appendFile\n  if (fs$appendFile)\n    fs.appendFile = appendFile\n  function appendFile (path, data, options, cb) {\n    if (typeof options === 'function')\n      cb = options, options = null\n\n    return go$appendFile(path, data, options, cb)\n\n    function go$appendFile (path, data, options, cb, startTime) {\n      return fs$appendFile(path, data, options, function (err) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([go$appendFile, [path, data, options, cb], err, startTime || Date.now(), Date.now()])\n        else {\n          if (typeof cb === 'function')\n            cb.apply(this, arguments)\n        }\n      })\n    }\n  }\n\n  var fs$copyFile = fs.copyFile\n  if (fs$copyFile)\n    fs.copyFile = copyFile\n  function copyFile (src, dest, flags, cb) {\n    if (typeof flags === 'function') {\n      cb = flags\n      flags = 0\n    }\n    return go$copyFile(src, dest, flags, cb)\n\n    function go$copyFile (src, dest, flags, cb, startTime) {\n      return fs$copyFile(src, dest, flags, function (err) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([go$copyFile, [src, dest, flags, cb], err, startTime || Date.now(), Date.now()])\n        else {\n          if (typeof cb === 'function')\n            cb.apply(this, arguments)\n        }\n      })\n    }\n  }\n\n  var fs$readdir = fs.readdir\n  fs.readdir = readdir\n  var noReaddirOptionVersions = /^v[0-5]\\./\n  function readdir (path, options, cb) {\n    if (typeof options === 'function')\n      cb = options, options = null\n\n    var go$readdir = noReaddirOptionVersions.test(process.version)\n      ? function go$readdir (path, options, cb, startTime) {\n        return fs$readdir(path, fs$readdirCallback(\n          path, options, cb, startTime\n        ))\n      }\n      : function go$readdir (path, options, cb, startTime) {\n        return fs$readdir(path, options, fs$readdirCallback(\n          path, options, cb, startTime\n        ))\n      }\n\n    return go$readdir(path, options, cb)\n\n    function fs$readdirCallback (path, options, cb, startTime) {\n      return function (err, files) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([\n            go$readdir,\n            [path, options, cb],\n            err,\n            startTime || Date.now(),\n            Date.now()\n          ])\n        else {\n          if (files && files.sort)\n            files.sort()\n\n          if (typeof cb === 'function')\n            cb.call(this, err, files)\n        }\n      }\n    }\n  }\n\n  if (process.version.substr(0, 4) === 'v0.8') {\n    var legStreams = legacy(fs)\n    ReadStream = legStreams.ReadStream\n    WriteStream = legStreams.WriteStream\n  }\n\n  var fs$ReadStream = fs.ReadStream\n  if (fs$ReadStream) {\n    ReadStream.prototype = Object.create(fs$ReadStream.prototype)\n    ReadStream.prototype.open = ReadStream$open\n  }\n\n  var fs$WriteStream = fs.WriteStream\n  if (fs$WriteStream) {\n    WriteStream.prototype = Object.create(fs$WriteStream.prototype)\n    WriteStream.prototype.open = WriteStream$open\n  }\n\n  Object.defineProperty(fs, 'ReadStream', {\n    get: function () {\n      return ReadStream\n    },\n    set: function (val) {\n      ReadStream = val\n    },\n    enumerable: true,\n    configurable: true\n  })\n  Object.defineProperty(fs, 'WriteStream', {\n    get: function () {\n      return WriteStream\n    },\n    set: function (val) {\n      WriteStream = val\n    },\n    enumerable: true,\n    configurable: true\n  })\n\n  // legacy names\n  var FileReadStream = ReadStream\n  Object.defineProperty(fs, 'FileReadStream', {\n    get: function () {\n      return FileReadStream\n    },\n    set: function (val) {\n      FileReadStream = val\n    },\n    enumerable: true,\n    configurable: true\n  })\n  var FileWriteStream = WriteStream\n  Object.defineProperty(fs, 'FileWriteStream', {\n    get: function () {\n      return FileWriteStream\n    },\n    set: function (val) {\n      FileWriteStream = val\n    },\n    enumerable: true,\n    configurable: true\n  })\n\n  function ReadStream (path, options) {\n    if (this instanceof ReadStream)\n      return fs$ReadStream.apply(this, arguments), this\n    else\n      return ReadStream.apply(Object.create(ReadStream.prototype), arguments)\n  }\n\n  function ReadStream$open () {\n    var that = this\n    open(that.path, that.flags, that.mode, function (err, fd) {\n      if (err) {\n        if (that.autoClose)\n          that.destroy()\n\n        that.emit('error', err)\n      } else {\n        that.fd = fd\n        that.emit('open', fd)\n        that.read()\n      }\n    })\n  }\n\n  function WriteStream (path, options) {\n    if (this instanceof WriteStream)\n      return fs$WriteStream.apply(this, arguments), this\n    else\n      return WriteStream.apply(Object.create(WriteStream.prototype), arguments)\n  }\n\n  function WriteStream$open () {\n    var that = this\n    open(that.path, that.flags, that.mode, function (err, fd) {\n      if (err) {\n        that.destroy()\n        that.emit('error', err)\n      } else {\n        that.fd = fd\n        that.emit('open', fd)\n      }\n    })\n  }\n\n  function createReadStream (path, options) {\n    return new fs.ReadStream(path, options)\n  }\n\n  function createWriteStream (path, options) {\n    return new fs.WriteStream(path, options)\n  }\n\n  var fs$open = fs.open\n  fs.open = open\n  function open (path, flags, mode, cb) {\n    if (typeof mode === 'function')\n      cb = mode, mode = null\n\n    return go$open(path, flags, mode, cb)\n\n    function go$open (path, flags, mode, cb, startTime) {\n      return fs$open(path, flags, mode, function (err, fd) {\n        if (err && (err.code === 'EMFILE' || err.code === 'ENFILE'))\n          enqueue([go$open, [path, flags, mode, cb], err, startTime || Date.now(), Date.now()])\n        else {\n          if (typeof cb === 'function')\n            cb.apply(this, arguments)\n        }\n      })\n    }\n  }\n\n  return fs\n}\n\nfunction enqueue (elem) {\n  debug('ENQUEUE', elem[0].name, elem[1])\n  fs[gracefulQueue].push(elem)\n  retry()\n}\n\n// keep track of the timeout between retry() calls\nvar retryTimer\n\n// reset the startTime and lastTime to now\n// this resets the start of the 60 second overall timeout as well as the\n// delay between attempts so that we'll retry these jobs sooner\nfunction resetQueue () {\n  var now = Date.now()\n  for (var i = 0; i < fs[gracefulQueue].length; ++i) {\n    // entries that are only a length of 2 are from an older version, don't\n    // bother modifying those since they'll be retried anyway.\n    if (fs[gracefulQueue][i].length > 2) {\n      fs[gracefulQueue][i][3] = now // startTime\n      fs[gracefulQueue][i][4] = now // lastTime\n    }\n  }\n  // call retry to make sure we're actively processing the queue\n  retry()\n}\n\nfunction retry () {\n  // clear the timer and remove it to help prevent unintended concurrency\n  clearTimeout(retryTimer)\n  retryTimer = undefined\n\n  if (fs[gracefulQueue].length === 0)\n    return\n\n  var elem = fs[gracefulQueue].shift()\n  var fn = elem[0]\n  var args = elem[1]\n  // these items may be unset if they were added by an older graceful-fs\n  var err = elem[2]\n  var startTime = elem[3]\n  var lastTime = elem[4]\n\n  // if we don't have a startTime we have no way of knowing if we've waited\n  // long enough, so go ahead and retry this item now\n  if (startTime === undefined) {\n    debug('RETRY', fn.name, args)\n    fn.apply(null, args)\n  } else if (Date.now() - startTime >= 60000) {\n    // it's been more than 60 seconds total, bail now\n    debug('TIMEOUT', fn.name, args)\n    var cb = args.pop()\n    if (typeof cb === 'function')\n      cb.call(null, err)\n  } else {\n    // the amount of time between the last attempt and right now\n    var sinceAttempt = Date.now() - lastTime\n    // the amount of time between when we first tried, and when we last tried\n    // rounded up to at least 1\n    var sinceStart = Math.max(lastTime - startTime, 1)\n    // backoff. wait longer than the total time we've been retrying, but only\n    // up to a maximum of 100ms\n    var desiredDelay = Math.min(sinceStart * 1.2, 100)\n    // it's been long enough since the last retry, do it again\n    if (sinceAttempt >= desiredDelay) {\n      debug('RETRY', fn.name, args)\n      fn.apply(null, args.concat([startTime]))\n    } else {\n      // if we can't do this job yet, push it to the end of the queue\n      // and let the next iteration check again\n      fs[gracefulQueue].push(elem)\n    }\n  }\n\n  // schedule our next run if one isn't already scheduled\n  if (retryTimer === undefined) {\n    retryTimer = setTimeout(retry, 0)\n  }\n}\n", "var constants = require('constants')\n\nvar origCwd = process.cwd\nvar cwd = null\n\nvar platform = process.env.GRACEFUL_FS_PLATFORM || process.platform\n\nprocess.cwd = function() {\n  if (!cwd)\n    cwd = origCwd.call(process)\n  return cwd\n}\ntry {\n  process.cwd()\n} catch (er) {}\n\n// This check is needed until node.js 12 is required\nif (typeof process.chdir === 'function') {\n  var chdir = process.chdir\n  process.chdir = function (d) {\n    cwd = null\n    chdir.call(process, d)\n  }\n  if (Object.setPrototypeOf) Object.setPrototypeOf(process.chdir, chdir)\n}\n\nmodule.exports = patch\n\nfunction patch (fs) {\n  // (re-)implement some things that are known busted or missing.\n\n  // lchmod, broken prior to 0.6.2\n  // back-port the fix here.\n  if (constants.hasOwnProperty('O_SYMLINK') &&\n      process.version.match(/^v0\\.6\\.[0-2]|^v0\\.5\\./)) {\n    patchLchmod(fs)\n  }\n\n  // lutimes implementation, or no-op\n  if (!fs.lutimes) {\n    patchLutimes(fs)\n  }\n\n  // https://github.com/isaacs/node-graceful-fs/issues/4\n  // Chown should not fail on einval or eperm if non-root.\n  // It should not fail on enosys ever, as this just indicates\n  // that a fs doesn't support the intended operation.\n\n  fs.chown = chownFix(fs.chown)\n  fs.fchown = chownFix(fs.fchown)\n  fs.lchown = chownFix(fs.lchown)\n\n  fs.chmod = chmodFix(fs.chmod)\n  fs.fchmod = chmodFix(fs.fchmod)\n  fs.lchmod = chmodFix(fs.lchmod)\n\n  fs.chownSync = chownFixSync(fs.chownSync)\n  fs.fchownSync = chownFixSync(fs.fchownSync)\n  fs.lchownSync = chownFixSync(fs.lchownSync)\n\n  fs.chmodSync = chmodFixSync(fs.chmodSync)\n  fs.fchmodSync = chmodFixSync(fs.fchmodSync)\n  fs.lchmodSync = chmodFixSync(fs.lchmodSync)\n\n  fs.stat = statFix(fs.stat)\n  fs.fstat = statFix(fs.fstat)\n  fs.lstat = statFix(fs.lstat)\n\n  fs.statSync = statFixSync(fs.statSync)\n  fs.fstatSync = statFixSync(fs.fstatSync)\n  fs.lstatSync = statFixSync(fs.lstatSync)\n\n  // if lchmod/lchown do not exist, then make them no-ops\n  if (fs.chmod && !fs.lchmod) {\n    fs.lchmod = function (path, mode, cb) {\n      if (cb) process.nextTick(cb)\n    }\n    fs.lchmodSync = function () {}\n  }\n  if (fs.chown && !fs.lchown) {\n    fs.lchown = function (path, uid, gid, cb) {\n      if (cb) process.nextTick(cb)\n    }\n    fs.lchownSync = function () {}\n  }\n\n  // on Windows, A/V software can lock the directory, causing this\n  // to fail with an EACCES or EPERM if the directory contains newly\n  // created files.  Try again on failure, for up to 60 seconds.\n\n  // Set the timeout this long because some Windows Anti-Virus, such as Parity\n  // bit9, may lock files for up to a minute, causing npm package install\n  // failures. Also, take care to yield the scheduler. Windows scheduling gives\n  // CPU to a busy looping process, which can cause the program causing the lock\n  // contention to be starved of CPU by node, so the contention doesn't resolve.\n  if (platform === \"win32\") {\n    fs.rename = typeof fs.rename !== 'function' ? fs.rename\n    : (function (fs$rename) {\n      function rename (from, to, cb) {\n        var start = Date.now()\n        var backoff = 0;\n        fs$rename(from, to, function CB (er) {\n          if (er\n              && (er.code === \"EACCES\" || er.code === \"EPERM\" || er.code === \"EBUSY\")\n              && Date.now() - start < 60000) {\n            setTimeout(function() {\n              fs.stat(to, function (stater, st) {\n                if (stater && stater.code === \"ENOENT\")\n                  fs$rename(from, to, CB);\n                else\n                  cb(er)\n              })\n            }, backoff)\n            if (backoff < 100)\n              backoff += 10;\n            return;\n          }\n          if (cb) cb(er)\n        })\n      }\n      if (Object.setPrototypeOf) Object.setPrototypeOf(rename, fs$rename)\n      return rename\n    })(fs.rename)\n  }\n\n  // if read() returns EAGAIN, then just try it again.\n  fs.read = typeof fs.read !== 'function' ? fs.read\n  : (function (fs$read) {\n    function read (fd, buffer, offset, length, position, callback_) {\n      var callback\n      if (callback_ && typeof callback_ === 'function') {\n        var eagCounter = 0\n        callback = function (er, _, __) {\n          if (er && er.code === 'EAGAIN' && eagCounter < 10) {\n            eagCounter ++\n            return fs$read.call(fs, fd, buffer, offset, length, position, callback)\n          }\n          callback_.apply(this, arguments)\n        }\n      }\n      return fs$read.call(fs, fd, buffer, offset, length, position, callback)\n    }\n\n    // This ensures `util.promisify` works as it does for native `fs.read`.\n    if (Object.setPrototypeOf) Object.setPrototypeOf(read, fs$read)\n    return read\n  })(fs.read)\n\n  fs.readSync = typeof fs.readSync !== 'function' ? fs.readSync\n  : (function (fs$readSync) { return function (fd, buffer, offset, length, position) {\n    var eagCounter = 0\n    while (true) {\n      try {\n        return fs$readSync.call(fs, fd, buffer, offset, length, position)\n      } catch (er) {\n        if (er.code === 'EAGAIN' && eagCounter < 10) {\n          eagCounter ++\n          continue\n        }\n        throw er\n      }\n    }\n  }})(fs.readSync)\n\n  function patchLchmod (fs) {\n    fs.lchmod = function (path, mode, callback) {\n      fs.open( path\n             , constants.O_WRONLY | constants.O_SYMLINK\n             , mode\n             , function (err, fd) {\n        if (err) {\n          if (callback) callback(err)\n          return\n        }\n        // prefer to return the chmod error, if one occurs,\n        // but still try to close, and report closing errors if they occur.\n        fs.fchmod(fd, mode, function (err) {\n          fs.close(fd, function(err2) {\n            if (callback) callback(err || err2)\n          })\n        })\n      })\n    }\n\n    fs.lchmodSync = function (path, mode) {\n      var fd = fs.openSync(path, constants.O_WRONLY | constants.O_SYMLINK, mode)\n\n      // prefer to return the chmod error, if one occurs,\n      // but still try to close, and report closing errors if they occur.\n      var threw = true\n      var ret\n      try {\n        ret = fs.fchmodSync(fd, mode)\n        threw = false\n      } finally {\n        if (threw) {\n          try {\n            fs.closeSync(fd)\n          } catch (er) {}\n        } else {\n          fs.closeSync(fd)\n        }\n      }\n      return ret\n    }\n  }\n\n  function patchLutimes (fs) {\n    if (constants.hasOwnProperty(\"O_SYMLINK\") && fs.futimes) {\n      fs.lutimes = function (path, at, mt, cb) {\n        fs.open(path, constants.O_SYMLINK, function (er, fd) {\n          if (er) {\n            if (cb) cb(er)\n            return\n          }\n          fs.futimes(fd, at, mt, function (er) {\n            fs.close(fd, function (er2) {\n              if (cb) cb(er || er2)\n            })\n          })\n        })\n      }\n\n      fs.lutimesSync = function (path, at, mt) {\n        var fd = fs.openSync(path, constants.O_SYMLINK)\n        var ret\n        var threw = true\n        try {\n          ret = fs.futimesSync(fd, at, mt)\n          threw = false\n        } finally {\n          if (threw) {\n            try {\n              fs.closeSync(fd)\n            } catch (er) {}\n          } else {\n            fs.closeSync(fd)\n          }\n        }\n        return ret\n      }\n\n    } else if (fs.futimes) {\n      fs.lutimes = function (_a, _b, _c, cb) { if (cb) process.nextTick(cb) }\n      fs.lutimesSync = function () {}\n    }\n  }\n\n  function chmodFix (orig) {\n    if (!orig) return orig\n    return function (target, mode, cb) {\n      return orig.call(fs, target, mode, function (er) {\n        if (chownErOk(er)) er = null\n        if (cb) cb.apply(this, arguments)\n      })\n    }\n  }\n\n  function chmodFixSync (orig) {\n    if (!orig) return orig\n    return function (target, mode) {\n      try {\n        return orig.call(fs, target, mode)\n      } catch (er) {\n        if (!chownErOk(er)) throw er\n      }\n    }\n  }\n\n\n  function chownFix (orig) {\n    if (!orig) return orig\n    return function (target, uid, gid, cb) {\n      return orig.call(fs, target, uid, gid, function (er) {\n        if (chownErOk(er)) er = null\n        if (cb) cb.apply(this, arguments)\n      })\n    }\n  }\n\n  function chownFixSync (orig) {\n    if (!orig) return orig\n    return function (target, uid, gid) {\n      try {\n        return orig.call(fs, target, uid, gid)\n      } catch (er) {\n        if (!chownErOk(er)) throw er\n      }\n    }\n  }\n\n  function statFix (orig) {\n    if (!orig) return orig\n    // Older versions of Node erroneously returned signed integers for\n    // uid + gid.\n    return function (target, options, cb) {\n      if (typeof options === 'function') {\n        cb = options\n        options = null\n      }\n      function callback (er, stats) {\n        if (stats) {\n          if (stats.uid < 0) stats.uid += 0x100000000\n          if (stats.gid < 0) stats.gid += 0x100000000\n        }\n        if (cb) cb.apply(this, arguments)\n      }\n      return options ? orig.call(fs, target, options, callback)\n        : orig.call(fs, target, callback)\n    }\n  }\n\n  function statFixSync (orig) {\n    if (!orig) return orig\n    // Older versions of Node erroneously returned signed integers for\n    // uid + gid.\n    return function (target, options) {\n      var stats = options ? orig.call(fs, target, options)\n        : orig.call(fs, target)\n      if (stats) {\n        if (stats.uid < 0) stats.uid += 0x100000000\n        if (stats.gid < 0) stats.gid += 0x100000000\n      }\n      return stats;\n    }\n  }\n\n  // ENOSYS means that the fs doesn't support the op. Just ignore\n  // that, because it doesn't matter.\n  //\n  // if there's no getuid, or if getuid() is something other\n  // than 0, and the error is EINVAL or EPERM, then just ignore\n  // it.\n  //\n  // This specific case is a silent failure in cp, install, tar,\n  // and most other unix tools that manage permissions.\n  //\n  // When running as root, or if other types of errors are\n  // encountered, then it's strict.\n  function chownErOk (er) {\n    if (!er)\n      return true\n\n    if (er.code === \"ENOSYS\")\n      return true\n\n    var nonroot = !process.getuid || process.getuid() !== 0\n    if (nonroot) {\n      if (er.code === \"EINVAL\" || er.code === \"EPERM\")\n        return true\n    }\n\n    return false\n  }\n}\n", "var Stream = require('stream').Stream\n\nmodule.exports = legacy\n\nfunction legacy (fs) {\n  return {\n    ReadStream: ReadStream,\n    WriteStream: WriteStream\n  }\n\n  function ReadStream (path, options) {\n    if (!(this instanceof ReadStream)) return new ReadStream(path, options);\n\n    Stream.call(this);\n\n    var self = this;\n\n    this.path = path;\n    this.fd = null;\n    this.readable = true;\n    this.paused = false;\n\n    this.flags = 'r';\n    this.mode = 438; /*=0666*/\n    this.bufferSize = 64 * 1024;\n\n    options = options || {};\n\n    // Mixin options into this\n    var keys = Object.keys(options);\n    for (var index = 0, length = keys.length; index < length; index++) {\n      var key = keys[index];\n      this[key] = options[key];\n    }\n\n    if (this.encoding) this.setEncoding(this.encoding);\n\n    if (this.start !== undefined) {\n      if ('number' !== typeof this.start) {\n        throw TypeError('start must be a Number');\n      }\n      if (this.end === undefined) {\n        this.end = Infinity;\n      } else if ('number' !== typeof this.end) {\n        throw TypeError('end must be a Number');\n      }\n\n      if (this.start > this.end) {\n        throw new Error('start must be <= end');\n      }\n\n      this.pos = this.start;\n    }\n\n    if (this.fd !== null) {\n      process.nextTick(function() {\n        self._read();\n      });\n      return;\n    }\n\n    fs.open(this.path, this.flags, this.mode, function (err, fd) {\n      if (err) {\n        self.emit('error', err);\n        self.readable = false;\n        return;\n      }\n\n      self.fd = fd;\n      self.emit('open', fd);\n      self._read();\n    })\n  }\n\n  function WriteStream (path, options) {\n    if (!(this instanceof WriteStream)) return new WriteStream(path, options);\n\n    Stream.call(this);\n\n    this.path = path;\n    this.fd = null;\n    this.writable = true;\n\n    this.flags = 'w';\n    this.encoding = 'binary';\n    this.mode = 438; /*=0666*/\n    this.bytesWritten = 0;\n\n    options = options || {};\n\n    // Mixin options into this\n    var keys = Object.keys(options);\n    for (var index = 0, length = keys.length; index < length; index++) {\n      var key = keys[index];\n      this[key] = options[key];\n    }\n\n    if (this.start !== undefined) {\n      if ('number' !== typeof this.start) {\n        throw TypeError('start must be a Number');\n      }\n      if (this.start < 0) {\n        throw new Error('start must be >= zero');\n      }\n\n      this.pos = this.start;\n    }\n\n    this.busy = false;\n    this._queue = [];\n\n    if (this.fd === null) {\n      this._open = fs.open;\n      this._queue.push([this._open, this.path, this.flags, this.mode, undefined]);\n      this.flush();\n    }\n  }\n}\n", "'use strict'\n\nmodule.exports = clone\n\nvar getPrototypeOf = Object.getPrototypeOf || function (obj) {\n  return obj.__proto__\n}\n\nfunction clone (obj) {\n  if (obj === null || typeof obj !== 'object')\n    return obj\n\n  if (obj instanceof Object)\n    var copy = { __proto__: getPrototypeOf(obj) }\n  else\n    var copy = Object.create(null)\n\n  Object.getOwnPropertyNames(obj).forEach(function (key) {\n    Object.defineProperty(copy, key, Object.getOwnPropertyDescriptor(obj, key))\n  })\n\n  return copy\n}\n", "var fs = require('fs'),\n    path = require('path');\n\nmodule.exports = ncp;\nncp.ncp = ncp;\n\nfunction ncp (source, dest, options, callback) {\n  var cback = callback;\n\n  if (!callback) {\n    cback = options;\n    options = {};\n  }\n\n  var basePath = process.cwd(),\n      currentPath = path.resolve(basePath, source),\n      targetPath = path.resolve(basePath, dest),\n      filter = options.filter,\n      rename = options.rename,\n      transform = options.transform,\n      clobber = options.clobber !== false,\n      modified = options.modified,\n      dereference = options.dereference,\n      errs = null,\n      started = 0,\n      finished = 0,\n      running = 0,\n      limit = options.limit || ncp.limit || 16;\n\n  limit = (limit < 1) ? 1 : (limit > 512) ? 512 : limit;\n\n  startCopy(currentPath);\n  \n  function startCopy(source) {\n    started++;\n    if (filter) {\n      if (filter instanceof RegExp) {\n        if (!filter.test(source)) {\n          return cb(true);\n        }\n      }\n      else if (typeof filter === 'function') {\n        if (!filter(source)) {\n          return cb(true);\n        }\n      }\n    }\n    return getStats(source);\n  }\n\n  function getStats(source) {\n    var stat = dereference ? fs.stat : fs.lstat;\n    if (running >= limit) {\n      return setImmediate(function () {\n        getStats(source);\n      });\n    }\n    running++;\n    stat(source, function (err, stats) {\n      var item = {};\n      if (err) {\n        return onError(err);\n      }\n\n      // We need to get the mode from the stats object and preserve it.\n      item.name = source;\n      item.mode = stats.mode;\n      item.mtime = stats.mtime; //modified time\n      item.atime = stats.atime; //access time\n\n      if (stats.isDirectory()) {\n        return onDir(item);\n      }\n      else if (stats.isFile()) {\n        return onFile(item);\n      }\n      else if (stats.isSymbolicLink()) {\n        // Symlinks don't really need to know about the mode.\n        return onLink(source);\n      }\n    });\n  }\n\n  function onFile(file) {\n    var target = file.name.replace(currentPath, targetPath);\n    if(rename) {\n      target =  rename(target);\n    }\n    isWritable(target, function (writable) {\n      if (writable) {\n        return copyFile(file, target);\n      }\n      if(clobber) {\n        rmFile(target, function () {\n          copyFile(file, target);\n        });\n      }\n      if (modified) {\n        var stat = dereference ? fs.stat : fs.lstat;\n        stat(target, function(err, stats) {\n            //if souce modified time greater to target modified time copy file\n            if (file.mtime.getTime()>stats.mtime.getTime())\n                copyFile(file, target);\n            else return cb();\n        });\n      }\n      else {\n        return cb();\n      }\n    });\n  }\n\n  function copyFile(file, target) {\n    var readStream = fs.createReadStream(file.name),\n        writeStream = fs.createWriteStream(target, { mode: file.mode });\n    \n    readStream.on('error', onError);\n    writeStream.on('error', onError);\n    \n    if(transform) {\n      transform(readStream, writeStream, file);\n    } else {\n      writeStream.on('open', function() {\n        readStream.pipe(writeStream);\n      });\n    }\n    writeStream.once('finish', function() {\n        if (modified) {\n            //target file modified date sync.\n            fs.utimesSync(target, file.atime, file.mtime);\n            cb();\n        }\n        else cb();\n    });\n  }\n\n  function rmFile(file, done) {\n    fs.unlink(file, function (err) {\n      if (err) {\n        return onError(err);\n      }\n      return done();\n    });\n  }\n\n  function onDir(dir) {\n    var target = dir.name.replace(currentPath, targetPath);\n    isWritable(target, function (writable) {\n      if (writable) {\n        return mkDir(dir, target);\n      }\n      copyDir(dir.name);\n    });\n  }\n\n  function mkDir(dir, target) {\n    fs.mkdir(target, dir.mode, function (err) {\n      if (err) {\n        return onError(err);\n      }\n      copyDir(dir.name);\n    });\n  }\n\n  function copyDir(dir) {\n    fs.readdir(dir, function (err, items) {\n      if (err) {\n        return onError(err);\n      }\n      items.forEach(function (item) {\n        startCopy(path.join(dir, item));\n      });\n      return cb();\n    });\n  }\n\n  function onLink(link) {\n    var target = link.replace(currentPath, targetPath);\n    fs.readlink(link, function (err, resolvedPath) {\n      if (err) {\n        return onError(err);\n      }\n      checkLink(resolvedPath, target);\n    });\n  }\n\n  function checkLink(resolvedPath, target) {\n    if (dereference) {\n      resolvedPath = path.resolve(basePath, resolvedPath);\n    }\n    isWritable(target, function (writable) {\n      if (writable) {\n        return makeLink(resolvedPath, target);\n      }\n      fs.readlink(target, function (err, targetDest) {\n        if (err) {\n          return onError(err);\n        }\n        if (dereference) {\n          targetDest = path.resolve(basePath, targetDest);\n        }\n        if (targetDest === resolvedPath) {\n          return cb();\n        }\n        return rmFile(target, function () {\n          makeLink(resolvedPath, target);\n        });\n      });\n    });\n  }\n\n  function makeLink(linkPath, target) {\n    fs.symlink(linkPath, target, function (err) {\n      if (err) {\n        return onError(err);\n      }\n      return cb();\n    });\n  }\n\n  function isWritable(path, done) {\n    fs.lstat(path, function (err) {\n      if (err) {\n        if (err.code === 'ENOENT') return done(true);\n        return done(false);\n      }\n      return done(false);\n    });\n  }\n\n  function onError(err) {\n    if (options.stopOnError) {\n      return cback(err);\n    }\n    else if (!errs && options.errs) {\n      errs = fs.createWriteStream(options.errs);\n    }\n    else if (!errs) {\n      errs = [];\n    }\n    if (typeof errs.write === 'undefined') {\n      errs.push(err);\n    }\n    else { \n      errs.write(err.stack + '\\n\\n');\n    }\n    return cb();\n  }\n\n  function cb(skipped) {\n    if (!skipped) running--;\n    finished++;\n    if ((started === finished) && (running === 0)) {\n      if (cback !== undefined ) {\n        return errs ? cback(errs) : cback(null);\n      }\n    }\n  }\n}\n\n\n", "{\n  \"name\": \"@parcel/fs\",\n  \"version\": \"2.15.2\",\n  \"description\": \"Blazing fast, zero configuration web application bundler\",\n  \"license\": \"MIT\",\n  \"publishConfig\": {\n    \"access\": \"public\"\n  },\n  \"funding\": {\n    \"type\": \"opencollective\",\n    \"url\": \"https://opencollective.com/parcel\"\n  },\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/parcel-bundler/parcel.git\"\n  },\n  \"main\": \"lib/index.js\",\n  \"source\": \"src/index.js\",\n  \"types\": \"index.d.ts\",\n  \"engines\": {\n    \"node\": \">= 16.0.0\"\n  },\n  \"targets\": {\n    \"types\": false,\n    \"main\": {\n      \"includeNodeModules\": {\n        \"@parcel/core\": false,\n        \"@parcel/feature-flags\": false,\n        \"@parcel/rust\": false,\n        \"@parcel/types-internal\": false,\n        \"@parcel/utils\": false,\n        \"@parcel/watcher\": false,\n        \"@parcel/watcher-watchman-js\": false,\n        \"@parcel/workers\": false\n      }\n    },\n    \"browser\": {\n      \"includeNodeModules\": {\n        \"@parcel/core\": false,\n        \"@parcel/feature-flags\": false,\n        \"@parcel/rust\": false,\n        \"@parcel/types-internal\": false,\n        \"@parcel/utils\": false,\n        \"@parcel/watcher\": false,\n        \"@parcel/watcher-watchman-js\": false,\n        \"@parcel/workers\": false\n      }\n    }\n  },\n  \"scripts\": {\n    \"build-ts\": \"mkdir -p lib && flow-to-ts src/types.js > lib/types.d.ts\",\n    \"check-ts\": \"tsc --noEmit index.d.ts\"\n  },\n  \"dependencies\": {\n    \"@parcel/feature-flags\": \"2.15.2\",\n    \"@parcel/rust\": \"2.15.2\",\n    \"@parcel/types-internal\": \"2.15.2\",\n    \"@parcel/utils\": \"2.15.2\",\n    \"@parcel/watcher\": \"^2.0.7\",\n    \"@parcel/workers\": \"2.15.2\"\n  },\n  \"devDependencies\": {\n    \"@parcel/watcher-watchman-js\": \"2.15.2\",\n    \"graceful-fs\": \"^4.2.11\",\n    \"ncp\": \"^2.0.0\",\n    \"nullthrows\": \"^1.1.1\",\n    \"utility-types\": \"^3.11.0\"\n  },\n  \"peerDependencies\": {\n    \"@parcel/core\": \"^2.15.2\"\n  },\n  \"browser\": {\n    \"./src/NodeFS.js\": \"./src/NodeFS.browser.js\",\n    \"@parcel/fs\": \"./lib/browser.js\"\n  },\n  \"gitHead\": \"b66f37168d0e830c030d0427bceac90117674cae\"\n}\n", "// @flow\nimport type {FilePath, FileSystem} from '@parcel/types-internal';\nimport path from 'path';\n\nexport function findNodeModule(\n  fs: FileSystem,\n  moduleName: string,\n  dir: FilePath,\n): ?FilePath {\n  let {root} = path.parse(dir);\n  while (dir !== root) {\n    // Skip node_modules directories\n    if (path.basename(dir) === 'node_modules') {\n      dir = path.dirname(dir);\n    }\n\n    try {\n      let moduleDir = path.join(dir, 'node_modules', moduleName);\n      let stats = fs.statSync(moduleDir);\n      if (stats.isDirectory()) {\n        return moduleDir;\n      }\n    } catch (err) {\n      // ignore\n    }\n\n    // Move up a directory\n    dir = path.dirname(dir);\n  }\n\n  return null;\n}\n\nexport function findAncestorFile(\n  fs: FileSystem,\n  fileNames: Array<string>,\n  dir: FilePath,\n  root: FilePath,\n): ?FilePath {\n  let {root: pathRoot} = path.parse(dir);\n  // eslint-disable-next-line no-constant-condition\n  while (true) {\n    if (path.basename(dir) === 'node_modules') {\n      return null;\n    }\n\n    for (const fileName of fileNames) {\n      let filePath = path.join(dir, fileName);\n      try {\n        if (fs.statSync(filePath).isFile()) {\n          return filePath;\n        }\n      } catch (err) {\n        // ignore\n      }\n    }\n\n    if (dir === root || dir === pathRoot) {\n      break;\n    }\n\n    dir = path.dirname(dir);\n  }\n\n  return null;\n}\n\nexport function findFirstFile(\n  fs: FileSystem,\n  filePaths: Array<FilePath>,\n): ?FilePath {\n  for (let filePath of filePaths) {\n    try {\n      if (fs.statSync(filePath).isFile()) {\n        return filePath;\n      }\n    } catch (err) {\n      // ignore\n    }\n  }\n}\n", "// @flow\n\nimport type {\n  FilePath,\n  FileSystem,\n  FileOptions,\n  ReaddirOptions,\n  Encoding,\n} from '@parcel/types-internal';\nimport type {\n  Event,\n  Options as WatcherOptions,\n  AsyncSubscription,\n} from '@parcel/watcher';\n\nimport path from 'path';\nimport {Readable, Writable} from 'stream';\nimport {registerSerializableClass} from '@parcel/core';\nimport {SharedBuffer} from '@parcel/utils';\nimport packageJSON from '../package.json';\nimport WorkerFarm, {Handle} from '@parcel/workers';\nimport nullthrows from 'nullthrows';\nimport EventEmitter from 'events';\nimport {findAncestorFile, findNodeModule, findFirstFile} from './find';\n\nconst instances: Map<number, MemoryFS> = new Map();\nlet id = 0;\n\ntype HandleFunction = (...args: Array<any>) => any;\ntype SerializedMemoryFS = {\n  id: number,\n  handle: any,\n  dirs: Map<FilePath, Directory>,\n  files: Map<FilePath, File>,\n  symlinks: Map<FilePath, FilePath>,\n  ...\n};\n\ntype WorkerEvent = {|\n  type: 'writeFile' | 'unlink' | 'mkdir' | 'symlink',\n  path: FilePath,\n  entry?: Entry,\n  target?: FilePath,\n|};\n\ntype ResolveFunction = () => mixed;\n\nexport class MemoryFS implements FileSystem {\n  dirs: Map<FilePath, Directory>;\n  files: Map<FilePath, File>;\n  symlinks: Map<FilePath, FilePath>;\n  watchers: Map<FilePath, Set<Watcher>>;\n  events: Array<Event>;\n  id: number;\n  handle: Handle;\n  farm: WorkerFarm;\n  _cwd: FilePath;\n  _eventQueue: Array<Event>;\n  _watcherTimer: TimeoutID;\n  _numWorkerInstances: number = 0;\n  _workerHandles: Array<Handle>;\n  _workerRegisterResolves: Array<ResolveFunction> = [];\n  _emitter: EventEmitter = new EventEmitter();\n\n  constructor(workerFarm: WorkerFarm) {\n    this.farm = workerFarm;\n    this._cwd = path.resolve(path.sep);\n    this.dirs = new Map([[this._cwd, new Directory()]]);\n    this.files = new Map();\n    this.symlinks = new Map();\n    this.watchers = new Map();\n    this.events = [];\n    this.id = id++;\n    this._workerHandles = [];\n    this._eventQueue = [];\n    instances.set(this.id, this);\n    this._emitter.on('allWorkersRegistered', () => {\n      for (let resolve of this._workerRegisterResolves) {\n        resolve();\n      }\n      this._workerRegisterResolves = [];\n    });\n  }\n\n  static deserialize(opts: SerializedMemoryFS): MemoryFS | WorkerFS {\n    let existing = instances.get(opts.id);\n    if (existing != null) {\n      // Correct the count of worker instances since serialization assumes a new instance is created\n      WorkerFarm.getWorkerApi().runHandle(opts.handle, [\n        'decrementWorkerInstance',\n        [],\n      ]);\n      return existing;\n    }\n\n    let fs = new WorkerFS(opts.id, nullthrows(opts.handle));\n    fs.dirs = opts.dirs;\n    fs.files = opts.files;\n    fs.symlinks = opts.symlinks;\n    return fs;\n  }\n\n  serialize(): SerializedMemoryFS {\n    if (!this.handle) {\n      this.handle = this.farm.createReverseHandle(\n        (fn: string, args: Array<mixed>) => {\n          // $FlowFixMe\n          return this[fn](...args);\n        },\n      );\n    }\n\n    // If a worker instance already exists, it will decrement this number\n    this._numWorkerInstances++;\n\n    return {\n      $$raw: false,\n      id: this.id,\n      handle: this.handle,\n      dirs: this.dirs,\n      files: this.files,\n      symlinks: this.symlinks,\n    };\n  }\n\n  decrementWorkerInstance() {\n    this._numWorkerInstances--;\n    if (this._numWorkerInstances === this._workerHandles.length) {\n      this._emitter.emit('allWorkersRegistered');\n    }\n  }\n\n  cwd(): FilePath {\n    return this._cwd;\n  }\n\n  chdir(dir: FilePath) {\n    this._cwd = dir;\n  }\n\n  _normalizePath(filePath: FilePath, realpath: boolean = true): FilePath {\n    filePath = path.normalize(filePath);\n    if (!filePath.startsWith(this.cwd())) {\n      filePath = path.resolve(this.cwd(), filePath);\n    }\n\n    // get realpath by following symlinks\n    let {root, dir, base} = path.parse(filePath);\n    let parts = dir.slice(root.length).split(path.sep).concat(base);\n\n    // If the realpath option is not true, don't follow the final link\n    let last;\n    if (!realpath) {\n      last = parts[parts.length - 1];\n      parts = parts.slice(0, -1);\n    }\n\n    let res = root;\n    for (let part of parts) {\n      res = path.join(res, part);\n      let symlink = this.symlinks.get(res);\n      if (symlink) {\n        res = symlink;\n      }\n    }\n\n    if (last) {\n      res = path.join(res, last);\n    }\n\n    return res;\n  }\n\n  async writeFile(\n    filePath: FilePath,\n    contents: Buffer | string,\n    options?: ?FileOptions,\n  ) {\n    filePath = this._normalizePath(filePath);\n    if (this.dirs.has(filePath)) {\n      throw new FSError('EISDIR', filePath, 'is a directory');\n    }\n\n    let dir = path.dirname(filePath);\n    if (!this.dirs.has(dir)) {\n      throw new FSError('ENOENT', dir, 'does not exist');\n    }\n\n    let buffer = makeShared(contents);\n    let file = this.files.get(filePath);\n    let mode = (options && options.mode) || 0o666;\n    if (file) {\n      file.write(buffer, mode);\n      this.files.set(filePath, file);\n    } else {\n      this.files.set(filePath, new File(buffer, mode));\n    }\n\n    await this._sendWorkerEvent({\n      type: 'writeFile',\n      path: filePath,\n      entry: this.files.get(filePath),\n    });\n\n    this._triggerEvent({\n      type: file ? 'update' : 'create',\n      path: filePath,\n    });\n  }\n\n  // eslint-disable-next-line require-await\n  async readFile(filePath: FilePath, encoding?: Encoding): Promise<any> {\n    return this.readFileSync(filePath, encoding);\n  }\n\n  readFileSync(filePath: FilePath, encoding?: Encoding): any {\n    filePath = this._normalizePath(filePath);\n    let file = this.files.get(filePath);\n    if (file == null) {\n      throw new FSError('ENOENT', filePath, 'does not exist');\n    }\n\n    let buffer = file.read();\n    if (encoding) {\n      return buffer.toString(encoding);\n    }\n\n    return buffer;\n  }\n\n  async copyFile(source: FilePath, destination: FilePath) {\n    let contents = await this.readFile(source);\n    await this.writeFile(destination, contents);\n  }\n\n  statSync(filePath: FilePath): Stat {\n    filePath = this._normalizePath(filePath);\n\n    let dir = this.dirs.get(filePath);\n    if (dir) {\n      return dir.stat();\n    }\n\n    let file = this.files.get(filePath);\n    if (file == null) {\n      throw new FSError('ENOENT', filePath, 'does not exist');\n    }\n\n    return file.stat();\n  }\n\n  // eslint-disable-next-line require-await\n  async stat(filePath: FilePath): Promise<Stat> {\n    return this.statSync(filePath);\n  }\n\n  lstatSync(filePath: FilePath): Stat {\n    filePath = this._normalizePath(filePath, false);\n\n    if (this.symlinks.has(filePath)) {\n      let stat = new Stat();\n      stat.mode = S_IFLNK;\n      return stat;\n    }\n\n    let dir = this.dirs.get(filePath);\n    if (dir) {\n      return dir.stat();\n    }\n\n    let file = this.files.get(filePath);\n    if (file == null) {\n      throw new FSError('ENOENT', filePath, 'does not exist');\n    }\n\n    return file.stat();\n  }\n\n  // eslint-disable-next-line require-await\n  async lstat(filePath: FilePath): Promise<Stat> {\n    return this.lstatSync(filePath);\n  }\n\n  readdirSync(dir: FilePath, opts?: ReaddirOptions): any {\n    dir = this._normalizePath(dir);\n    if (!this.dirs.has(dir)) {\n      throw new FSError('ENOENT', dir, 'does not exist');\n    }\n\n    if (!dir.endsWith(path.sep)) {\n      dir += path.sep;\n    }\n\n    let res = [];\n    for (let [filePath, entry] of this.dirs) {\n      if (filePath === dir) {\n        continue;\n      }\n      if (\n        filePath.startsWith(dir) &&\n        filePath.indexOf(path.sep, dir.length) === -1\n      ) {\n        let name = filePath.slice(dir.length);\n        if (opts?.withFileTypes) {\n          res.push(new Dirent(name, entry));\n        } else {\n          res.push(name);\n        }\n      }\n    }\n\n    for (let [filePath, entry] of this.files) {\n      if (\n        filePath.startsWith(dir) &&\n        filePath.indexOf(path.sep, dir.length) === -1\n      ) {\n        let name = filePath.slice(dir.length);\n        if (opts?.withFileTypes) {\n          res.push(new Dirent(name, entry));\n        } else {\n          res.push(name);\n        }\n      }\n    }\n\n    for (let [from] of this.symlinks) {\n      if (from.startsWith(dir) && from.indexOf(path.sep, dir.length) === -1) {\n        let name = from.slice(dir.length);\n        if (opts?.withFileTypes) {\n          res.push(new Dirent(name, {mode: S_IFLNK}));\n        } else {\n          res.push(name);\n        }\n      }\n    }\n\n    return res;\n  }\n\n  // eslint-disable-next-line require-await\n  async readdir(dir: FilePath, opts?: ReaddirOptions): Promise<any> {\n    return this.readdirSync(dir, opts);\n  }\n\n  async unlink(filePath: FilePath): Promise<void> {\n    filePath = this._normalizePath(filePath);\n    if (!this.files.has(filePath) && !this.dirs.has(filePath)) {\n      throw new FSError('ENOENT', filePath, 'does not exist');\n    }\n\n    this.files.delete(filePath);\n    this.dirs.delete(filePath);\n    this.watchers.delete(filePath);\n\n    await this._sendWorkerEvent({\n      type: 'unlink',\n      path: filePath,\n    });\n\n    this._triggerEvent({\n      type: 'delete',\n      path: filePath,\n    });\n\n    return Promise.resolve();\n  }\n\n  async mkdirp(dir: FilePath): Promise<void> {\n    dir = this._normalizePath(dir);\n    if (this.dirs.has(dir)) {\n      return Promise.resolve();\n    }\n\n    if (this.files.has(dir)) {\n      throw new FSError('ENOENT', dir, 'is not a directory');\n    }\n\n    let root = path.parse(dir).root;\n    while (dir !== root) {\n      if (this.dirs.has(dir)) {\n        break;\n      }\n\n      this.dirs.set(dir, new Directory());\n      await this._sendWorkerEvent({\n        type: 'mkdir',\n        path: dir,\n      });\n\n      this._triggerEvent({\n        type: 'create',\n        path: dir,\n      });\n\n      dir = path.dirname(dir);\n    }\n\n    return Promise.resolve();\n  }\n\n  async rimraf(filePath: FilePath): Promise<void> {\n    filePath = this._normalizePath(filePath);\n\n    if (this.dirs.has(filePath)) {\n      let dir = filePath + path.sep;\n      for (let filePath of this.files.keys()) {\n        if (filePath.startsWith(dir)) {\n          this.files.delete(filePath);\n          await this._sendWorkerEvent({\n            type: 'unlink',\n            path: filePath,\n          });\n\n          this._triggerEvent({\n            type: 'delete',\n            path: filePath,\n          });\n        }\n      }\n\n      for (let dirPath of this.dirs.keys()) {\n        if (dirPath.startsWith(dir)) {\n          this.dirs.delete(dirPath);\n          this.watchers.delete(dirPath);\n          await this._sendWorkerEvent({\n            type: 'unlink',\n            path: filePath,\n          });\n\n          this._triggerEvent({\n            type: 'delete',\n            path: dirPath,\n          });\n        }\n      }\n\n      for (let filePath of this.symlinks.keys()) {\n        if (filePath.startsWith(dir)) {\n          this.symlinks.delete(filePath);\n          await this._sendWorkerEvent({\n            type: 'unlink',\n            path: filePath,\n          });\n        }\n      }\n\n      this.dirs.delete(filePath);\n      await this._sendWorkerEvent({\n        type: 'unlink',\n        path: filePath,\n      });\n\n      this._triggerEvent({\n        type: 'delete',\n        path: filePath,\n      });\n    } else if (this.files.has(filePath)) {\n      this.files.delete(filePath);\n      await this._sendWorkerEvent({\n        type: 'unlink',\n        path: filePath,\n      });\n\n      this._triggerEvent({\n        type: 'delete',\n        path: filePath,\n      });\n    }\n\n    return Promise.resolve();\n  }\n\n  async ncp(source: FilePath, destination: FilePath) {\n    source = this._normalizePath(source);\n\n    if (this.dirs.has(source)) {\n      if (!this.dirs.has(destination)) {\n        this.dirs.set(destination, new Directory());\n        await this._sendWorkerEvent({\n          type: 'mkdir',\n          path: destination,\n        });\n\n        this._triggerEvent({\n          type: 'create',\n          path: destination,\n        });\n      }\n\n      let dir = source + path.sep;\n      for (let dirPath of this.dirs.keys()) {\n        if (dirPath.startsWith(dir)) {\n          let destName = path.join(destination, dirPath.slice(dir.length));\n          if (!this.dirs.has(destName)) {\n            this.dirs.set(destName, new Directory());\n            await this._sendWorkerEvent({\n              type: 'mkdir',\n              path: destination,\n            });\n            this._triggerEvent({\n              type: 'create',\n              path: destName,\n            });\n          }\n        }\n      }\n\n      for (let [filePath, file] of this.files) {\n        if (filePath.startsWith(dir)) {\n          let destName = path.join(destination, filePath.slice(dir.length));\n          let exists = this.files.has(destName);\n          this.files.set(destName, file);\n          await this._sendWorkerEvent({\n            type: 'writeFile',\n            path: destName,\n            entry: file,\n          });\n\n          this._triggerEvent({\n            type: exists ? 'update' : 'create',\n            path: destName,\n          });\n        }\n      }\n    } else {\n      await this.copyFile(source, destination);\n    }\n  }\n\n  createReadStream(filePath: FilePath): ReadStream {\n    return new ReadStream(this, filePath);\n  }\n\n  createWriteStream(filePath: FilePath, options: ?FileOptions): WriteStream {\n    return new WriteStream(this, filePath, options);\n  }\n\n  realpathSync(filePath: FilePath): FilePath {\n    return this._normalizePath(filePath);\n  }\n\n  // eslint-disable-next-line require-await\n  async realpath(filePath: FilePath): Promise<FilePath> {\n    return this.realpathSync(filePath);\n  }\n\n  readlinkSync(filePath: FilePath): FilePath {\n    let symlink = this.symlinks.get(filePath);\n    if (!symlink) {\n      throw new FSError('EINVAL', filePath, 'is not a symlink');\n    }\n    return symlink;\n  }\n\n  // eslint-disable-next-line require-await\n  async readlink(filePath: FilePath): Promise<FilePath> {\n    return this.readlinkSync(filePath);\n  }\n\n  async symlink(target: FilePath, path: FilePath) {\n    target = this._normalizePath(target);\n    path = this._normalizePath(path);\n    this.symlinks.set(path, target);\n    await this._sendWorkerEvent({\n      type: 'symlink',\n      path,\n      target,\n    });\n  }\n\n  existsSync(filePath: FilePath): boolean {\n    filePath = this._normalizePath(filePath);\n    return this.files.has(filePath) || this.dirs.has(filePath);\n  }\n\n  // eslint-disable-next-line require-await\n  async exists(filePath: FilePath): Promise<boolean> {\n    return this.existsSync(filePath);\n  }\n\n  _triggerEvent(event: Event) {\n    this.events.push(event);\n    if (this.watchers.size === 0) {\n      return;\n    }\n\n    // Batch events\n    this._eventQueue.push(event);\n    clearTimeout(this._watcherTimer);\n\n    this._watcherTimer = setTimeout(() => {\n      let events = this._eventQueue;\n      this._eventQueue = [];\n\n      for (let [dir, watchers] of this.watchers) {\n        if (!dir.endsWith(path.sep)) {\n          dir += path.sep;\n        }\n\n        if (event.path.startsWith(dir)) {\n          for (let watcher of watchers) {\n            watcher.trigger(events);\n          }\n        }\n      }\n    }, 50);\n  }\n\n  _registerWorker(handle: Handle) {\n    this._workerHandles.push(handle);\n    if (this._numWorkerInstances === this._workerHandles.length) {\n      this._emitter.emit('allWorkersRegistered');\n    }\n  }\n\n  async _sendWorkerEvent(event: WorkerEvent) {\n    // Wait for worker instances to register their handles\n    while (this._workerHandles.length < this._numWorkerInstances) {\n      await new Promise(resolve => this._workerRegisterResolves.push(resolve));\n    }\n\n    await Promise.all(\n      this._workerHandles.map(workerHandle =>\n        this.farm.workerApi.runHandle(workerHandle, [event]),\n      ),\n    );\n  }\n\n  watch(\n    dir: FilePath,\n    fn: (err: ?Error, events: Array<Event>) => mixed,\n    opts: WatcherOptions,\n  ): Promise<AsyncSubscription> {\n    dir = this._normalizePath(dir);\n    let watcher = new Watcher(fn, opts);\n    let watchers = this.watchers.get(dir);\n    if (!watchers) {\n      watchers = new Set();\n      this.watchers.set(dir, watchers);\n    }\n\n    watchers.add(watcher);\n\n    return Promise.resolve({\n      unsubscribe: () => {\n        watchers = nullthrows(watchers);\n        watchers.delete(watcher);\n\n        if (watchers.size === 0) {\n          this.watchers.delete(dir);\n        }\n\n        return Promise.resolve();\n      },\n    });\n  }\n\n  async getEventsSince(\n    dir: FilePath,\n    snapshot: FilePath,\n    opts: WatcherOptions,\n  ): Promise<Array<Event>> {\n    let contents = await this.readFile(snapshot, 'utf8');\n    let len = Number(contents);\n    let events = this.events.slice(len);\n    let ignore = opts.ignore;\n    if (ignore) {\n      events = events.filter(\n        event => !ignore.some(i => event.path.startsWith(i + path.sep)),\n      );\n    }\n\n    return events;\n  }\n\n  async writeSnapshot(dir: FilePath, snapshot: FilePath): Promise<void> {\n    await this.writeFile(snapshot, '' + this.events.length);\n  }\n\n  findAncestorFile(\n    fileNames: Array<string>,\n    fromDir: FilePath,\n    root: FilePath,\n  ): ?FilePath {\n    return findAncestorFile(this, fileNames, fromDir, root);\n  }\n\n  findNodeModule(moduleName: string, fromDir: FilePath): ?FilePath {\n    return findNodeModule(this, moduleName, fromDir);\n  }\n\n  findFirstFile(filePaths: Array<FilePath>): ?FilePath {\n    return findFirstFile(this, filePaths);\n  }\n}\n\nclass Watcher {\n  fn: (err: ?Error, events: Array<Event>) => mixed;\n  options: WatcherOptions;\n\n  constructor(\n    fn: (err: ?Error, events: Array<Event>) => mixed,\n    options: WatcherOptions,\n  ) {\n    this.fn = fn;\n    this.options = options;\n  }\n\n  trigger(events: Array<Event>) {\n    let ignore = this.options.ignore;\n    if (ignore) {\n      events = events.filter(\n        event => !ignore.some(i => event.path.startsWith(i + path.sep)),\n      );\n    }\n\n    if (events.length > 0) {\n      this.fn(null, events);\n    }\n  }\n}\n\nexport class FSError extends Error {\n  code: string;\n  path: FilePath;\n  constructor(code: string, path: FilePath, message: string) {\n    super(`${code}: ${path} ${message}`);\n    this.name = 'FSError';\n    this.code = code;\n    this.path = path;\n    Error.captureStackTrace?.(this, this.constructor);\n  }\n}\n\nclass ReadStream extends Readable {\n  fs: FileSystem;\n  filePath: FilePath;\n  reading: boolean;\n  bytesRead: number;\n  constructor(fs: FileSystem, filePath: FilePath) {\n    super();\n    this.fs = fs;\n    this.filePath = filePath;\n    this.reading = false;\n    this.bytesRead = 0;\n  }\n\n  _read() {\n    if (this.reading) {\n      return;\n    }\n\n    this.reading = true;\n    this.fs.readFile(this.filePath).then(\n      res => {\n        this.bytesRead += res.byteLength;\n        this.push(res);\n        this.push(null);\n      },\n      err => {\n        this.emit('error', err);\n      },\n    );\n  }\n}\n\nclass WriteStream extends Writable {\n  fs: FileSystem;\n  filePath: FilePath;\n  options: ?FileOptions;\n  buffer: Buffer;\n\n  constructor(fs: FileSystem, filePath: FilePath, options: ?FileOptions) {\n    super({emitClose: true, autoDestroy: true});\n    this.fs = fs;\n    this.filePath = filePath;\n    this.options = options;\n    this.buffer = Buffer.alloc(0);\n  }\n\n  _write(\n    chunk: Buffer | string,\n    encoding: any,\n    callback: (error?: Error) => void,\n  ) {\n    let c = typeof chunk === 'string' ? Buffer.from(chunk, encoding) : chunk;\n    this.buffer = Buffer.concat([this.buffer, c]);\n    callback();\n  }\n\n  _final(callback: (error?: Error) => void) {\n    this.fs\n      .writeFile(this.filePath, this.buffer, this.options)\n      .then(callback)\n      .catch(callback);\n  }\n}\n\nconst S_IFREG = 0o100000;\nconst S_IFDIR = 0o040000;\nconst S_IFLNK = 0o120000;\nconst S_IFMT = 0o170000;\n\nclass Entry {\n  mode: number;\n  atime: number;\n  mtime: number;\n  ctime: number;\n  birthtime: number;\n  constructor(mode: number) {\n    this.mode = mode;\n    let now = Date.now();\n    this.atime = now;\n    this.mtime = now;\n    this.ctime = now;\n    this.birthtime = now;\n  }\n\n  access() {\n    let now = Date.now();\n    this.atime = now;\n    this.ctime = now;\n  }\n\n  modify(mode: number) {\n    let now = Date.now();\n    this.mtime = now;\n    this.ctime = now;\n    this.mode = mode;\n  }\n\n  getSize(): number {\n    return 0;\n  }\n\n  stat(): Stat {\n    return Stat.fromEntry(this);\n  }\n}\n\nclass Stat {\n  dev: number = 0;\n  ino: number = 0;\n  mode: number = 0;\n  nlink: number = 0;\n  uid: number = 0;\n  gid: number = 0;\n  rdev: number = 0;\n  size: number = 0;\n  blksize: number = 0;\n  blocks: number = 0;\n  atimeMs: number = 0;\n  mtimeMs: number = 0;\n  ctimeMs: number = 0;\n  birthtimeMs: number = 0;\n  atime: Date = new Date();\n  mtime: Date = new Date();\n  ctime: Date = new Date();\n  birthtime: Date = new Date();\n\n  static fromEntry(entry: Entry): Stat {\n    let stat = new Stat();\n    stat.mode = entry.mode;\n    stat.size = entry.getSize();\n    stat.atimeMs = entry.atime;\n    stat.mtimeMs = entry.mtime;\n    stat.ctimeMs = entry.ctime;\n    stat.birthtimeMs = entry.birthtime;\n    stat.atime = new Date(entry.atime);\n    stat.mtime = new Date(entry.mtime);\n    stat.ctime = new Date(entry.ctime);\n    stat.birthtime = new Date(entry.birthtime);\n    return stat;\n  }\n\n  isFile(): boolean {\n    return (this.mode & S_IFREG) === S_IFREG;\n  }\n\n  isDirectory(): boolean {\n    return (this.mode & S_IFDIR) === S_IFDIR;\n  }\n\n  isBlockDevice(): boolean {\n    return false;\n  }\n\n  isCharacterDevice(): boolean {\n    return false;\n  }\n\n  isSymbolicLink(): boolean {\n    return (this.mode & S_IFMT) === S_IFLNK;\n  }\n\n  isFIFO(): boolean {\n    return false;\n  }\n\n  isSocket(): boolean {\n    return false;\n  }\n}\n\nclass Dirent {\n  name: string;\n  #mode: number;\n\n  constructor(name: string, entry: interface {mode: number}) {\n    this.name = name;\n    this.#mode = entry.mode;\n  }\n\n  isFile(): boolean {\n    return (this.#mode & S_IFMT) === S_IFREG;\n  }\n\n  isDirectory(): boolean {\n    return (this.#mode & S_IFMT) === S_IFDIR;\n  }\n\n  isBlockDevice(): boolean {\n    return false;\n  }\n\n  isCharacterDevice(): boolean {\n    return false;\n  }\n\n  isSymbolicLink(): boolean {\n    return (this.#mode & S_IFMT) === S_IFLNK;\n  }\n\n  isFIFO(): boolean {\n    return false;\n  }\n\n  isSocket(): boolean {\n    return false;\n  }\n}\n\nexport class File extends Entry {\n  buffer: Buffer;\n  constructor(buffer: Buffer, mode: number) {\n    super(S_IFREG | mode);\n    this.buffer = buffer;\n  }\n\n  read(): Buffer {\n    super.access();\n    return Buffer.from(this.buffer);\n  }\n\n  write(buffer: Buffer, mode: number) {\n    super.modify(S_IFREG | mode);\n    this.buffer = buffer;\n  }\n\n  getSize(): number {\n    return this.buffer.byteLength;\n  }\n}\n\nclass Directory extends Entry {\n  constructor() {\n    super(S_IFDIR);\n  }\n}\n\nexport function makeShared(contents: Buffer | string): Buffer {\n  if (typeof contents !== 'string' && contents.buffer instanceof SharedBuffer) {\n    return contents;\n  }\n\n  let contentsBuffer: Buffer | string = contents;\n  // $FlowFixMe\n  if (process.browser) {\n    // For the polyfilled buffer module, it's faster to always convert once so that the subsequent\n    // operations are fast (.byteLength and using .set instead of .write)\n    contentsBuffer =\n      contentsBuffer instanceof Buffer\n        ? contentsBuffer\n        : Buffer.from(contentsBuffer);\n  }\n\n  let length = Buffer.byteLength(contentsBuffer);\n  let shared = new SharedBuffer(length);\n  let buffer = Buffer.from(shared);\n  if (length > 0) {\n    if (typeof contentsBuffer === 'string') {\n      buffer.write(contentsBuffer);\n    } else {\n      buffer.set(contentsBuffer);\n    }\n  }\n\n  return buffer;\n}\n\nclass WorkerFS extends MemoryFS {\n  id: number;\n  handleFn: HandleFunction;\n\n  constructor(id: number, handle: Handle) {\n    // TODO Make this not a subclass\n    // $FlowFixMe\n    super();\n    this.id = id;\n    this.handleFn = (methodName, args) =>\n      WorkerFarm.getWorkerApi().runHandle(handle, [methodName, args]);\n\n    this.handleFn('_registerWorker', [\n      WorkerFarm.getWorkerApi().createReverseHandle(event => {\n        switch (event.type) {\n          case 'writeFile':\n            this.files.set(event.path, event.entry);\n            break;\n          case 'unlink':\n            this.files.delete(event.path);\n            this.dirs.delete(event.path);\n            this.symlinks.delete(event.path);\n            break;\n          case 'mkdir':\n            this.dirs.set(event.path, new Directory());\n            break;\n          case 'symlink':\n            this.symlinks.set(event.path, event.target);\n            break;\n        }\n      }),\n    ]);\n  }\n\n  static deserialize(opts: SerializedMemoryFS): MemoryFS {\n    return nullthrows(instances.get(opts.id));\n  }\n\n  serialize(): SerializedMemoryFS {\n    // $FlowFixMe\n    return {\n      id: this.id,\n    };\n  }\n\n  writeFile(\n    filePath: FilePath,\n    contents: Buffer | string,\n    options: ?FileOptions,\n  ): Promise<void> {\n    super.writeFile(filePath, contents, options);\n    let buffer = makeShared(contents);\n    return this.handleFn('writeFile', [filePath, buffer, options]);\n  }\n\n  unlink(filePath: FilePath): Promise<void> {\n    super.unlink(filePath);\n    return this.handleFn('unlink', [filePath]);\n  }\n\n  mkdirp(dir: FilePath): Promise<void> {\n    super.mkdirp(dir);\n    return this.handleFn('mkdirp', [dir]);\n  }\n\n  rimraf(filePath: FilePath): Promise<void> {\n    super.rimraf(filePath);\n    return this.handleFn('rimraf', [filePath]);\n  }\n\n  ncp(source: FilePath, destination: FilePath): Promise<void> {\n    super.ncp(source, destination);\n    return this.handleFn('ncp', [source, destination]);\n  }\n\n  symlink(target: FilePath, path: FilePath): Promise<void> {\n    super.symlink(target, path);\n    return this.handleFn('symlink', [target, path]);\n  }\n}\n\nregisterSerializableClass(`${packageJSON.version}:MemoryFS`, MemoryFS);\nregisterSerializableClass(`${packageJSON.version}:WorkerFS`, WorkerFS);\nregisterSerializableClass(`${packageJSON.version}:Stat`, Stat);\nregisterSerializableClass(`${packageJSON.version}:File`, File);\nregisterSerializableClass(`${packageJSON.version}:Directory`, Directory);\n", "'use strict';\n\nfunction nullthrows(x, message) {\n  if (x != null) {\n    return x;\n  }\n  var error = new Error(message !== undefined ? message : 'Got unexpected ' + x);\n  error.framesToPop = 1; // Skip nullthrows's own stack frame.\n  throw error;\n}\n\nmodule.exports = nullthrows;\nmodule.exports.default = nullthrows;\n\nObject.defineProperty(module.exports, '__esModule', {value: true});\n", "// @flow\n\nimport type {Readable, Writable} from 'stream';\nimport type {\n  FilePath,\n  Encoding,\n  FileOptions,\n  FileSystem,\n  ReaddirOptions,\n  FileStats,\n} from '@parcel/types-internal';\nimport type {\n  Event,\n  Options as WatcherOptions,\n  AsyncSubscription,\n} from '@parcel/watcher';\n\nimport {registerSerializableClass} from '@parcel/core';\nimport WorkerFarm from '@parcel/workers';\nimport packageJSON from '../package.json';\nimport {findAncestorFile, findNodeModule, findFirstFile} from './find';\nimport {MemoryFS} from './MemoryFS';\n\nimport nullthrows from 'nullthrows';\nimport path from 'path';\n\nexport class OverlayFS implements FileSystem {\n  deleted: Set<FilePath> = new Set();\n  writable: FileSystem;\n  readable: FileSystem;\n  _cwd: FilePath;\n\n  constructor(workerFarmOrFS: WorkerFarm | FileSystem, readable: FileSystem) {\n    if (workerFarmOrFS instanceof WorkerFarm) {\n      this.writable = new MemoryFS(workerFarmOrFS);\n    } else {\n      this.writable = workerFarmOrFS;\n    }\n    this.readable = readable;\n    this._cwd = readable.cwd();\n  }\n\n  static deserialize(opts: any): OverlayFS {\n    let fs = new OverlayFS(opts.writable, opts.readable);\n    if (opts.deleted != null) fs.deleted = opts.deleted;\n    return fs;\n  }\n\n  serialize(): {|\n    $$raw: boolean,\n    readable: FileSystem,\n    writable: FileSystem,\n    deleted: Set<FilePath>,\n  |} {\n    return {\n      $$raw: false,\n      writable: this.writable,\n      readable: this.readable,\n      deleted: this.deleted,\n    };\n  }\n\n  _deletedThrows(filePath: FilePath): FilePath {\n    filePath = this._normalizePath(filePath);\n    if (this.deleted.has(filePath)) {\n      throw new FSError('ENOENT', filePath, 'does not exist');\n    }\n    return filePath;\n  }\n\n  _checkExists(filePath: FilePath): FilePath {\n    filePath = this._deletedThrows(filePath);\n    if (!this.existsSync(filePath)) {\n      throw new FSError('ENOENT', filePath, 'does not exist');\n    }\n    return filePath;\n  }\n\n  _isSymlink(filePath: FilePath): boolean {\n    filePath = this._normalizePath(filePath);\n    // Check the parts of the path to see if any are symlinks.\n    let {root, dir, base} = path.parse(filePath);\n    let segments = dir.slice(root.length).split(path.sep).concat(base);\n    while (segments.length) {\n      filePath = path.join(root, ...segments);\n      let name = segments.pop();\n      if (this.deleted.has(filePath)) {\n        return false;\n      } else if (\n        this.writable instanceof MemoryFS &&\n        this.writable.symlinks.has(filePath)\n      ) {\n        return true;\n      } else {\n        // HACK: Parcel fs does not provide `lstatSync`,\n        // so we use `readdirSync` to check if the path is a symlink.\n        let parent = path.resolve(filePath, '..');\n        if (parent === filePath) {\n          return false;\n        }\n        try {\n          for (let dirent of this.readdirSync(parent, {withFileTypes: true})) {\n            if (typeof dirent === 'string') {\n              break; // {withFileTypes: true} not supported\n            } else if (dirent.name === name) {\n              if (dirent.isSymbolicLink()) {\n                return true;\n              }\n            }\n          }\n        } catch (e) {\n          if (e.code === 'ENOENT') {\n            return false;\n          }\n          throw e;\n        }\n      }\n    }\n\n    return false;\n  }\n\n  async _copyPathForWrite(filePath: FilePath): Promise<FilePath> {\n    filePath = await this._normalizePath(filePath);\n    let dirPath = path.dirname(filePath);\n    if (this.existsSync(dirPath) && !this.writable.existsSync(dirPath)) {\n      await this.writable.mkdirp(dirPath);\n    }\n    return filePath;\n  }\n\n  _normalizePath(filePath: FilePath): FilePath {\n    return path.resolve(this.cwd(), filePath);\n  }\n\n  // eslint-disable-next-line require-await\n  async readFile(filePath: FilePath, encoding?: Encoding): Promise<any> {\n    return this.readFileSync(filePath, encoding);\n  }\n\n  async writeFile(\n    filePath: FilePath,\n    contents: string | Buffer,\n    options: ?FileOptions,\n  ): Promise<void> {\n    filePath = await this._copyPathForWrite(filePath);\n    await this.writable.writeFile(filePath, contents, options);\n    this.deleted.delete(filePath);\n  }\n\n  async copyFile(source: FilePath, destination: FilePath): Promise<void> {\n    source = this._normalizePath(source);\n    destination = await this._copyPathForWrite(destination);\n\n    if (await this.writable.exists(source)) {\n      await this.writable.writeFile(\n        destination,\n        await this.writable.readFile(source),\n      );\n    } else {\n      await this.writable.writeFile(\n        destination,\n        await this.readable.readFile(source),\n      );\n    }\n\n    this.deleted.delete(destination);\n  }\n\n  // eslint-disable-next-line require-await\n  async stat(filePath: FilePath): Promise<FileStats> {\n    return this.statSync(filePath);\n  }\n\n  // eslint-disable-next-line require-await\n  async lstat(filePath: FilePath): Promise<FileStats> {\n    return this.lstatSync(filePath);\n  }\n\n  async symlink(target: FilePath, filePath: FilePath): Promise<void> {\n    target = this._normalizePath(target);\n    filePath = this._normalizePath(filePath);\n    await this.writable.symlink(target, filePath);\n    this.deleted.delete(filePath);\n  }\n\n  async unlink(filePath: FilePath): Promise<void> {\n    filePath = this._normalizePath(filePath);\n\n    let toDelete = [filePath];\n\n    if (this.writable instanceof MemoryFS && this._isSymlink(filePath)) {\n      this.writable.symlinks.delete(filePath);\n    } else if (this.statSync(filePath).isDirectory()) {\n      let stack = [filePath];\n\n      // Recursively add every descendant path to deleted.\n      while (stack.length) {\n        let root = nullthrows(stack.pop());\n        for (let ent of this.readdirSync(root, {withFileTypes: true})) {\n          if (typeof ent === 'string') {\n            let childPath = path.join(root, ent);\n            toDelete.push(childPath);\n            if (this.statSync(childPath).isDirectory()) {\n              stack.push(childPath);\n            }\n          } else {\n            let childPath = path.join(root, ent.name);\n            toDelete.push(childPath);\n            if (ent.isDirectory()) {\n              stack.push(childPath);\n            }\n          }\n        }\n      }\n    }\n\n    try {\n      await this.writable.unlink(filePath);\n    } catch (e) {\n      if (e.code === 'ENOENT' && !this.readable.existsSync(filePath)) {\n        throw e;\n      }\n    }\n\n    for (let pathToDelete of toDelete) {\n      this.deleted.add(pathToDelete);\n    }\n  }\n\n  async mkdirp(dir: FilePath): Promise<void> {\n    dir = this._normalizePath(dir);\n    await this.writable.mkdirp(dir);\n\n    if (this.deleted != null) {\n      let root = path.parse(dir).root;\n      while (dir !== root) {\n        this.deleted.delete(dir);\n        dir = path.dirname(dir);\n      }\n    }\n  }\n\n  async rimraf(filePath: FilePath): Promise<void> {\n    try {\n      await this.unlink(filePath);\n    } catch (e) {\n      // noop\n    }\n  }\n\n  // eslint-disable-next-line require-await\n  async ncp(source: FilePath, destination: FilePath): Promise<void> {\n    // TODO: Implement this correctly.\n    return this.writable.ncp(source, destination);\n  }\n\n  createReadStream(filePath: FilePath, opts?: ?FileOptions): Readable {\n    filePath = this._deletedThrows(filePath);\n    if (this.writable.existsSync(filePath)) {\n      return this.writable.createReadStream(filePath, opts);\n    }\n\n    return this.readable.createReadStream(filePath, opts);\n  }\n\n  createWriteStream(path: FilePath, opts?: ?FileOptions): Writable {\n    path = this._normalizePath(path);\n    this.deleted.delete(path);\n    return this.writable.createWriteStream(path, opts);\n  }\n\n  cwd(): FilePath {\n    return this._cwd;\n  }\n\n  chdir(path: FilePath): void {\n    this._cwd = this._checkExists(path);\n  }\n\n  // eslint-disable-next-line require-await\n  async realpath(filePath: FilePath): Promise<FilePath> {\n    return this.realpathSync(filePath);\n  }\n\n  readFileSync(filePath: FilePath, encoding?: Encoding): any {\n    filePath = this.realpathSync(filePath);\n    try {\n      // $FlowFixMe[incompatible-call]\n      return this.writable.readFileSync(filePath, encoding);\n    } catch (err) {\n      // $FlowFixMe[incompatible-call]\n      return this.readable.readFileSync(filePath, encoding);\n    }\n  }\n\n  statSync(filePath: FilePath): FileStats {\n    filePath = this._normalizePath(filePath);\n    try {\n      return this.writable.statSync(filePath);\n    } catch (e) {\n      if (e.code === 'ENOENT' && this.existsSync(filePath)) {\n        return this.readable.statSync(filePath);\n      }\n      throw e;\n    }\n  }\n\n  lstatSync(filePath: FilePath): FileStats {\n    filePath = this._normalizePath(filePath);\n    try {\n      return this.writable.lstatSync(filePath);\n    } catch (e) {\n      if (e.code === 'ENOENT') {\n        return this.readable.lstatSync(filePath);\n      }\n      throw e;\n    }\n  }\n\n  realpathSync(filePath: FilePath): FilePath {\n    filePath = this._deletedThrows(filePath);\n    filePath = this._deletedThrows(this.writable.realpathSync(filePath));\n    if (!this.writable.existsSync(filePath)) {\n      return this.readable.realpathSync(filePath);\n    }\n    return filePath;\n  }\n\n  readlinkSync(filePath: FilePath): FilePath {\n    filePath = this._deletedThrows(filePath);\n    try {\n      return this.writable.readlinkSync(filePath);\n    } catch (err) {\n      return this.readable.readlinkSync(filePath);\n    }\n  }\n\n  // eslint-disable-next-line require-await\n  async readlink(filePath: FilePath): Promise<FilePath> {\n    return this.readlinkSync(filePath);\n  }\n\n  // eslint-disable-next-line require-await\n  async exists(filePath: FilePath): Promise<boolean> {\n    return this.existsSync(filePath);\n  }\n\n  existsSync(filePath: FilePath): boolean {\n    filePath = this._normalizePath(filePath);\n    if (this.deleted.has(filePath)) return false;\n\n    try {\n      filePath = this.realpathSync(filePath);\n    } catch (err) {\n      if (err.code !== 'ENOENT') throw err;\n    }\n\n    if (this.deleted.has(filePath)) return false;\n\n    return (\n      this.writable.existsSync(filePath) || this.readable.existsSync(filePath)\n    );\n  }\n\n  // eslint-disable-next-line require-await\n  async readdir(path: FilePath, opts?: ReaddirOptions): Promise<any> {\n    return this.readdirSync(path, opts);\n  }\n\n  readdirSync(dir: FilePath, opts?: ReaddirOptions): any {\n    dir = this.realpathSync(dir);\n    // Read from both filesystems and merge the results\n    let entries = new Map();\n\n    try {\n      for (let entry: any of this.writable.readdirSync(dir, opts)) {\n        let filePath = path.join(dir, entry.name ?? entry);\n        if (this.deleted.has(filePath)) continue;\n        entries.set(filePath, entry);\n      }\n    } catch {\n      // noop\n    }\n\n    try {\n      for (let entry: any of this.readable.readdirSync(dir, opts)) {\n        let filePath = path.join(dir, entry.name ?? entry);\n        if (this.deleted.has(filePath)) continue;\n        if (entries.has(filePath)) continue;\n        entries.set(filePath, entry);\n      }\n    } catch {\n      // noop\n    }\n\n    return Array.from(entries.values());\n  }\n\n  async watch(\n    dir: FilePath,\n    fn: (err: ?Error, events: Array<Event>) => mixed,\n    opts: WatcherOptions,\n  ): Promise<AsyncSubscription> {\n    let writableSubscription = await this.writable.watch(dir, fn, opts);\n    let readableSubscription = await this.readable.watch(dir, fn, opts);\n    return {\n      unsubscribe: async () => {\n        await writableSubscription.unsubscribe();\n        await readableSubscription.unsubscribe();\n      },\n    };\n  }\n\n  async getEventsSince(\n    dir: FilePath,\n    snapshot: FilePath,\n    opts: WatcherOptions,\n  ): Promise<Array<Event>> {\n    let writableEvents = await this.writable.getEventsSince(\n      dir,\n      snapshot,\n      opts,\n    );\n    let readableEvents = await this.readable.getEventsSince(\n      dir,\n      snapshot,\n      opts,\n    );\n    return [...writableEvents, ...readableEvents];\n  }\n\n  async writeSnapshot(\n    dir: FilePath,\n    snapshot: FilePath,\n    opts: WatcherOptions,\n  ): Promise<void> {\n    await this.writable.writeSnapshot(dir, snapshot, opts);\n  }\n\n  findAncestorFile(\n    fileNames: Array<string>,\n    fromDir: FilePath,\n    root: FilePath,\n  ): ?FilePath {\n    return findAncestorFile(this, fileNames, fromDir, root);\n  }\n\n  findNodeModule(moduleName: string, fromDir: FilePath): ?FilePath {\n    return findNodeModule(this, moduleName, fromDir);\n  }\n\n  findFirstFile(filePaths: Array<FilePath>): ?FilePath {\n    return findFirstFile(this, filePaths);\n  }\n}\n\nclass FSError extends Error {\n  code: string;\n  path: FilePath;\n  constructor(code: string, path: FilePath, message: string) {\n    super(`${code}: ${path} ${message}`);\n    this.name = 'FSError';\n    this.code = code;\n    this.path = path;\n    Error.captureStackTrace?.(this, this.constructor);\n  }\n}\n\nregisterSerializableClass(`${packageJSON.version}:OverlayFS`, OverlayFS);\n"], "names": ["path", "stream", "promisify", "pipeline", "ncp", "sourceFS", "source", "destinationFS", "destination", "mkdirp", "files", "readdir", "file", "sourcePath", "join", "destPath", "stats", "stat", "isFile", "createReadStream", "createWriteStream", "isDirectory", "fs", "nativeFS", "tmpdir", "registerSerializableClass", "hashFile", "getFeatureFlag", "watcher", "packageJSON", "searchNative", "searchJS", "realpath", "process", "platform", "native", "isPnP", "versions", "pnp", "getWatchmanWatcher", "packageName", "require", "NodeFS", "readFile", "copyFile", "lstat", "symlink", "readlink", "unlink", "utimes", "cwd", "chdir", "directory", "statSync", "lstatSync", "realpathSync", "readlinkSync", "existsSync", "readdirSync", "findAncestorFile", "args", "findNodeModule", "findFirstFile", "filePath", "options", "tmpFile<PERSON>ath", "getTempFilePath", "failed", "move", "promises", "rename", "e", "syscall", "code", "hashTmp", "hash<PERSON>arget", "Promise", "all", "writeStream", "close", "fd", "cb", "err", "then", "once", "unlinkSync", "writeFile", "contents", "readFileSync", "encoding", "originalPath", "exists", "resolve", "watch", "dir", "fn", "opts", "subscribe", "getEventsSince", "snapshot", "writeSnapshot", "deserialize", "serialize", "mkdir", "recursive", "<PERSON><PERSON><PERSON>", "rm", "force", "rmdir", "version", "writeStreamCalls", "threadId", "useOsTmpDir", "shouldUseOsTmpDir", "tmpDir", "accessSync", "constants", "<PERSON>_<PERSON>", "W_OK", "tmpDirStats", "filePathStats", "dev", "Number", "MAX_SAFE_INTEGER", "basename", "pid", "toString", "module", "exports", "JSON", "parse", "moduleName", "root", "dirname", "moduleDir", "fileNames", "pathRoot", "fileName", "filePaths", "Readable", "Writable", "SharedBuffer", "WorkerFarm", "<PERSON><PERSON>", "nullthrows", "EventEmitter", "instances", "Map", "id", "MemoryFS", "_numWorkerInstances", "_workerRegisterResolves", "_emitter", "constructor", "workerFarm", "farm", "_cwd", "sep", "dirs", "Directory", "symlinks", "watchers", "events", "_<PERSON><PERSON><PERSON><PERSON>", "_eventQueue", "set", "on", "existing", "get", "getWorkerApi", "<PERSON><PERSON><PERSON><PERSON>", "handle", "WorkerFS", "createReverseHandle", "$$raw", "decrementWorkerInstance", "length", "emit", "_normalizePath", "normalize", "startsWith", "base", "parts", "slice", "split", "concat", "last", "res", "part", "has", "FSError", "buffer", "makeShared", "mode", "write", "File", "_sendWorkerEvent", "type", "entry", "_triggerEvent", "read", "Stat", "S_IFLNK", "endsWith", "indexOf", "name", "withFileTypes", "push", "Dirent", "from", "delete", "keys", "<PERSON><PERSON><PERSON>", "destName", "ReadStream", "WriteStream", "target", "event", "size", "clearTimeout", "_watcher<PERSON>imer", "setTimeout", "trigger", "_registerWorker", "map", "worker<PERSON><PERSON><PERSON>", "workerApi", "Watcher", "Set", "add", "unsubscribe", "len", "ignore", "filter", "some", "i", "fromDir", "Error", "message", "captureStackTrace", "reading", "bytesRead", "_read", "byteLength", "emitClose", "autoDestroy", "<PERSON><PERSON><PERSON>", "alloc", "_write", "chunk", "callback", "c", "_final", "catch", "S_IFREG", "S_IFDIR", "S_IFMT", "Entry", "now", "Date", "atime", "mtime", "ctime", "birthtime", "access", "modify", "getSize", "fromEntry", "ino", "nlink", "uid", "gid", "rdev", "blksize", "blocks", "atimeMs", "mtimeMs", "ctimeMs", "birthtimeMs", "isBlockDevice", "isCharacterDevice", "isSymbolicLink", "isFIFO", "isSocket", "contentsBuffer", "browser", "shared", "handleFn", "methodName", "OverlayFS", "deleted", "workerFarmOrFS", "readable", "writable", "_deletedThrows", "_checkExists", "_isSymlink", "segments", "pop", "parent", "dirent", "_copyPathForWrite", "toDelete", "stack", "ent", "child<PERSON><PERSON>", "pathToDelete", "entries", "Array", "values", "writableSubscription", "readableSubscription", "writableEvents", "readableEvents"], "version": 3, "file": "index.js.map", "sourceRoot": "../../../../"}