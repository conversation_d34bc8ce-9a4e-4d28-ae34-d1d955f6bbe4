{"mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACGA,+DAAA;AACO,MAAMsB;IACXC,aAAc;QACZ,MAAM,IAAIC,MAAM;IAClB;AACF;;;;;;;;;;;;;;AERA8M,4BAAiBE,KAAKnJ,KAAK,CAAC;;;;;ACA5B;AAEA,SAAS,iCAAW,CAAC,EAAE,OAAO;IAC5B,IAAI,KAAK,MACP,OAAO;IAET,IAAI,QAAQ,IAAI,MAAM,YAAY,YAAY,UAAU,oBAAoB;IAC5E,MAAM,WAAW,GAAG,GAAG,qCAAqC;IAC5D,MAAM;AACR;AAEA,4BAAiB;AACjB,0BAAe,OAAO,GAAG;AAEzB,OAAO,cAAc,CAAC,2BAAgB,cAAc;IAAC,OAAO;AAAI;;;;;ACVzD,SAASlD,0CACd8B,EAAc,EACd0G,UAAkB,EAClB9F,GAAa;IAEb,IAAI,QAACM,IAAAA,EAAK,GAAGnF,CAAAA,GAAAA,qCAAAA,EAAKqF,KAAK,CAACR;IACxB,MAAOA,QAAQM,KAAM;QACnB,gCAAA;QACA,IAAInF,CAAAA,GAAAA,qCAAAA,EAAKyO,QAAQ,CAAC5J,SAAS,gBACzBA,MAAM7E,CAAAA,GAAAA,qCAAAA,EAAKmG,OAAO,CAACtB;QAGrB,IAAI;YACF,IAAI6J,YAAY1O,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAAC+D,KAAK,gBAAgB8F;YAC/C,IAAI3J,QAAQiD,GAAGkD,QAAQ,CAACuH;YACxB,IAAI1N,MAAMK,WAAW,IACnB,OAAOqN;QAEX,EAAE,OAAOrD,KAAK;QACZ,SAAA;QAAA;QAGF,sBAAA;QACAxG,MAAM7E,CAAAA,GAAAA,qCAAAA,EAAKmG,OAAO,CAACtB;IACrB;IAEA,OAAO;AACT;AAEO,SAAS3C,0CACd+B,EAAc,EACdwG,SAAwB,EACxB5F,GAAa,EACbM,IAAc;IAEd,IAAI,EAACA,MAAMwJ,QAANxJ,EAAe,GAAGnF,CAAAA,GAAAA,qCAAAA,EAAKqF,KAAK,CAACR;IAClC,iDAAA;IACA,MAAO,KAAM;QACX,IAAI7E,CAAAA,GAAAA,qCAAAA,EAAKyO,QAAQ,CAAC5J,SAAS,gBACzB,OAAO;QAGT,KAAK,MAAM+J,YAAYnE,UAAW;YAChC,IAAI1F,WAAW/E,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAAC+D,KAAK+J;YAC9B,IAAI;gBACF,IAAI3K,GAAGkD,QAAQ,CAACpC,UAAU7D,MAAM,IAC9B,OAAO6D;YAEX,EAAE,OAAOsG,KAAK;YACZ,SAAA;YAAA;QAEJ;QAEA,IAAIxG,QAAQM,QAAQN,QAAQ8J,UAC1B;QAGF9J,MAAM7E,CAAAA,GAAAA,qCAAAA,EAAKmG,OAAO,CAACtB;IACrB;IAEA,OAAO;AACT;AAEO,SAASzC,0CACd6B,EAAc,EACd2G,SAA0B;IAE1B,KAAK,IAAI7F,YAAY6F,UACnB,IAAI;QACF,IAAI3G,GAAGkD,QAAQ,CAACpC,UAAU7D,MAAM,IAC9B,OAAO6D;IAEX,EAAE,OAAOsG,KAAK;IACZ,SAAA;IAAA;AAGN;;;AHvDA,MAAMhJ,kCAAmC,IAAIC;AAC7C,IAAIC,2BAAK;AAqBF,MAAMC;IAYXC,sBAA8B,EAA9BA;IAEAC,0BAAkD,EAAE,CAApDA;IACAC,WAAyB,IAAIV,CAAAA,GAAAA,uCAAAA,IAA7BU;IAEApB,YAAYqB,UAAsB,CAAE;QAClC,IAAI,CAACC,IAAI,GAAGD;QACZ,IAAI,CAACE,IAAI,GAAG9C,CAAAA,GAAAA,qCAAAA,EAAK+C,OAAO,CAAC/C,CAAAA,GAAAA,qCAAAA,EAAKgD,GAAG;QACjC,IAAI,CAACC,IAAI,GAAG,IAAIX,IAAI;YAAC;gBAAC,IAAI,CAACQ,IAAI;gBAAE,IAAII;aAAY;SAAC;QAClD,IAAI,CAACxC,KAAK,GAAG,IAAI4B;QACjB,IAAI,CAACa,QAAQ,GAAG,IAAIb;QACpB,IAAI,CAACc,QAAQ,GAAG,IAAId;QACpB,IAAI,CAACe,MAAM,GAAG,EAAE;QAChB,IAAI,CAACd,EAAE,GAAGA;QACV,IAAI,CAACe,cAAc,GAAG,EAAE;QACxB,IAAI,CAACC,WAAW,GAAG,EAAE;QACrBlB,gCAAUmB,GAAG,CAAC,IAAI,CAACjB,EAAE,EAAE,IAAI;QAC3B,IAAI,CAACI,QAAQ,CAACc,EAAE,CAAC,wBAAwB;YACvC,KAAK,IAAIV,WAAW,IAAI,CAACL,uBAAuB,CAC9CK;YAEF,IAAI,CAACL,uBAAuB,GAAG,EAAE;QACnC;IACF;IAEA,OAAOgB,YAAYC,IAAwB,EAAuB;QAChE,IAAIC,WAAWvB,gCAAUwB,GAAG,CAACF,KAAKpB,EAAE;QACpC,IAAIqB,YAAY,MAAM;YACpB,8FAAA;YACA9B,CAAAA,GAAAA,8CAAAA,EAAWgC,YAAY,GAAGC,SAAS,CAACJ,KAAKK,MAAM,EAAE;gBAC/C;gBACA,EAAE;aACH;YACD,OAAOJ;QACT;QAEA,IAAIK,KAAK,IAAIC,+BAASP,KAAKpB,EAAE,EAAEP,CAAAA,GAAAA,gEAAAA,EAAW2B,KAAKK,MAAM;QACrDC,GAAGhB,IAAI,GAAGU,KAAKV,IAAI;QACnBgB,GAAGvD,KAAK,GAAGiD,KAAKjD,KAAK;QACrBuD,GAAGd,QAAQ,GAAGQ,KAAKR,QAAQ;QAC3B,OAAOc;IACT;IAEAE,YAAgC;QAC9B,IAAI,CAAC,IAAI,CAACH,MAAM,EACd,IAAI,CAACA,MAAM,GAAG,IAAI,CAACnB,IAAI,CAACuB,mBAAmB,CACzC,CAACC,IAAYC;YACX,aAAA;YACA,OAAO,IAAI,CAACD,GAAG,IAAIC;QACrB;QAIJ,qEAAA;QACA,IAAI,CAAC7B,mBAAmB;QAExB,OAAO;YACL8B,OAAO;YACPhC,IAAI,IAAI,CAACA,EAAE;YACXyB,QAAQ,IAAI,CAACA,MAAM;YACnBf,MAAM,IAAI,CAACA,IAAI;YACfvC,OAAO,IAAI,CAACA,KAAK;YACjByC,UAAU,IAAI,CAACA,QAAfA;QACF;IACF;IAEAqB,0BAA0B;QACxB,IAAI,CAAC/B,mBAAmB;QACxB,IAAI,IAAI,CAACA,mBAAmB,KAAK,IAAI,CAACa,cAAc,CAACmB,MAAM,EACzD,IAAI,CAAC9B,QAAQ,CAAC+B,IAAI,CAAC;IAEvB;IAEAC,MAAgB;QACd,OAAO,IAAI,CAAC7B,IAAI;IAClB;IAEA8B,MAAMC,GAAa,EAAE;QACnB,IAAI,CAAC/B,IAAI,GAAG+B;IACd;IAEAC,eAAeC,QAAkB,EAAEC,WAAoB,IAAI,EAAY;QACrED,WAAW/E,CAAAA,GAAAA,qCAAAA,EAAKiF,SAAS,CAACF;QAC1B,IAAI,CAACA,SAASG,UAAU,CAAC,IAAI,CAACP,GAAG,KAC/BI,WAAW/E,CAAAA,GAAAA,qCAAAA,EAAK+C,OAAO,CAAC,IAAI,CAAC4B,GAAG,IAAII;QAGtC,qCAAA;QACA,IAAI,QAACI,IAAI,OAAEN,GAAG,QAAEO,IAAAA,EAAK,GAAGpF,CAAAA,GAAAA,qCAAAA,EAAKqF,KAAK,CAACN;QACnC,IAAIO,QAAQT,IAAIU,KAAK,CAACJ,KAAKV,MAAM,EAAEe,KAAK,CAACxF,CAAAA,GAAAA,qCAAAA,EAAKgD,GAAG,EAAEyC,MAAM,CAACL;QAE1D,kEAAA;QACA,IAAIM;QACJ,IAAI,CAACV,UAAU;YACbU,OAAOJ,KAAK,CAACA,MAAMb,MAAM,GAAG,EAAE;YAC9Ba,QAAQA,MAAMC,KAAK,CAAC,GAAG;QACzB;QAEA,IAAII,MAAMR;QACV,KAAK,IAAIS,QAAQN,MAAO;YACtBK,MAAM3F,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAAC6E,KAAKC;YACrB,IAAIC,UAAU,IAAI,CAAC1C,QAAQ,CAACU,GAAG,CAAC8B;YAChC,IAAIE,SACFF,MAAME;QAEV;QAEA,IAAIH,MACFC,MAAM3F,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAAC6E,KAAKD;QAGvB,OAAOC;IACT;IAEA,MAAMG,UACJf,QAAkB,EAClBgB,QAAyB,EACzBC,OAAsB,EACtB;QACAjB,WAAW,IAAI,CAACD,cAAc,CAACC;QAC/B,IAAI,IAAI,CAAC9B,IAAI,CAACgD,GAAG,CAAClB,WAChB,MAAM,IAAImB,0CAAQ,UAAUnB,UAAU;QAGxC,IAAIF,MAAM7E,CAAAA,GAAAA,qCAAAA,EAAKmG,OAAO,CAACpB;QACvB,IAAI,CAAC,IAAI,CAAC9B,IAAI,CAACgD,GAAG,CAACpB,MACjB,MAAM,IAAIqB,0CAAQ,UAAUrB,KAAK;QAGnC,IAAIuB,SAASC,0CAAWN;QACxB,IAAInF,OAAO,IAAI,CAACF,KAAK,CAACmD,GAAG,CAACkB;QAC1B,IAAIuB,OAAQN,WAAWA,QAAQM,IAAI,IAAK;QACxC,IAAI1F,MAAM;YACRA,KAAK2F,KAAK,CAACH,QAAQE;YACnB,IAAI,CAAC5F,KAAK,CAAC8C,GAAG,CAACuB,UAAUnE;QAC3B,OACE,IAAI,CAACF,KAAK,CAAC8C,GAAG,CAACuB,UAAU,IAAIyB,0CAAKJ,QAAQE;QAG5C,MAAM,IAAI,CAACG,gBAAgB,CAAC;YAC1BC,MAAM;YACN1G,MAAM+E;YACN4B,OAAO,IAAI,CAACjG,KAAK,CAACmD,GAAG,CAACkB;QACxB;QAEA,IAAI,CAAC6B,aAAa,CAAC;YACjBF,MAAM9F,OAAO,WAAW;YACxBZ,MAAM+E;QACR;IACF;IAEA,yCAAA;IACA,MAAM8B,SAAS9B,QAAkB,EAAE+B,QAAmB,EAAgB;QACpE,OAAO,IAAI,CAACC,YAAY,CAAChC,UAAU+B;IACrC;IAEAC,aAAahC,QAAkB,EAAE+B,QAAmB,EAAO;QACzD/B,WAAW,IAAI,CAACD,cAAc,CAACC;QAC/B,IAAInE,OAAO,IAAI,CAACF,KAAK,CAACmD,GAAG,CAACkB;QAC1B,IAAInE,QAAQ,MACV,MAAM,IAAIsF,0CAAQ,UAAUnB,UAAU;QAGxC,IAAIqB,SAASxF,KAAKoG,IAAI;QACtB,IAAIF,UACF,OAAOV,OAAOa,QAAQ,CAACH;QAGzB,OAAOV;IACT;IAEA,MAAMc,SAAS5G,MAAgB,EAAEE,WAAqB,EAAE;QACtD,IAAIuF,WAAW,MAAM,IAAI,CAACc,QAAQ,CAACvG;QACnC,MAAM,IAAI,CAACwF,SAAS,CAACtF,aAAauF;IACpC;IAEAoB,SAASpC,QAAkB,EAAQ;QACjCA,WAAW,IAAI,CAACD,cAAc,CAACC;QAE/B,IAAIF,MAAM,IAAI,CAAC5B,IAAI,CAACY,GAAG,CAACkB;QACxB,IAAIF,KACF,OAAOA,IAAI5D,IAAI;QAGjB,IAAIL,OAAO,IAAI,CAACF,KAAK,CAACmD,GAAG,CAACkB;QAC1B,IAAInE,QAAQ,MACV,MAAM,IAAIsF,0CAAQ,UAAUnB,UAAU;QAGxC,OAAOnE,KAAKK,IAAI;IAClB;IAEA,yCAAA;IACA,MAAMA,KAAK8D,QAAkB,EAAiB;QAC5C,OAAO,IAAI,CAACoC,QAAQ,CAACpC;IACvB;IAEAqC,UAAUrC,QAAkB,EAAQ;QAClCA,WAAW,IAAI,CAACD,cAAc,CAACC,UAAU;QAEzC,IAAI,IAAI,CAAC5B,QAAQ,CAAC8C,GAAG,CAAClB,WAAW;YAC/B,IAAI9D,OAAO,IAAIoG;YACfpG,KAAKqF,IAAI,GAAGgB;YACZ,OAAOrG;QACT;QAEA,IAAI4D,MAAM,IAAI,CAAC5B,IAAI,CAACY,GAAG,CAACkB;QACxB,IAAIF,KACF,OAAOA,IAAI5D,IAAI;QAGjB,IAAIL,OAAO,IAAI,CAACF,KAAK,CAACmD,GAAG,CAACkB;QAC1B,IAAInE,QAAQ,MACV,MAAM,IAAIsF,0CAAQ,UAAUnB,UAAU;QAGxC,OAAOnE,KAAKK,IAAI;IAClB;IAEA,yCAAA;IACA,MAAMsG,MAAMxC,QAAkB,EAAiB;QAC7C,OAAO,IAAI,CAACqC,SAAS,CAACrC;IACxB;IAEAyC,YAAY3C,GAAa,EAAElB,IAAqB,EAAO;QACrDkB,MAAM,IAAI,CAACC,cAAc,CAACD;QAC1B,IAAI,CAAC,IAAI,CAAC5B,IAAI,CAACgD,GAAG,CAACpB,MACjB,MAAM,IAAIqB,0CAAQ,UAAUrB,KAAK;QAGnC,IAAI,CAACA,IAAI4C,QAAQ,CAACzH,CAAAA,GAAAA,qCAAAA,EAAKgD,GAAG,GACxB6B,OAAO7E,CAAAA,GAAAA,qCAAAA,EAAKgD,GAAG;QAGjB,IAAI2C,MAAM,EAAE;QACZ,KAAK,IAAI,CAACZ,UAAU4B,MAAM,IAAI,IAAI,CAAC1D,IAAI,CAAE;YACvC,IAAI8B,aAAaF,KACf;YAEF,IACEE,SAASG,UAAU,CAACL,QACpBE,SAAS2C,OAAO,CAAC1H,CAAAA,GAAAA,qCAAAA,EAAKgD,GAAG,EAAE6B,IAAIJ,MAAM,MAAM,IAC3C;gBACA,IAAIkD,OAAO5C,SAASQ,KAAK,CAACV,IAAIJ,MAAM;gBACpC,IAAId,MAAMiE,eACRjC,IAAIkC,IAAI,CAAC,IAAIC,6BAAOH,MAAMhB;qBAE1BhB,IAAIkC,IAAI,CAACF;YAEb;QACF;QAEA,KAAK,IAAI,CAAC5C,UAAU4B,MAAM,IAAI,IAAI,CAACjG,KAAK,CACtC,IACEqE,SAASG,UAAU,CAACL,QACpBE,SAAS2C,OAAO,CAAC1H,CAAAA,GAAAA,qCAAAA,EAAKgD,GAAG,EAAE6B,IAAIJ,MAAM,MAAM,IAC3C;YACA,IAAIkD,OAAO5C,SAASQ,KAAK,CAACV,IAAIJ,MAAM;YACpC,IAAId,MAAMiE,eACRjC,IAAIkC,IAAI,CAAC,IAAIC,6BAAOH,MAAMhB;iBAE1BhB,IAAIkC,IAAI,CAACF;QAEb;QAGF,KAAK,IAAI,CAACI,KAAK,IAAI,IAAI,CAAC5E,QAAQ,CAC9B,IAAI4E,KAAK7C,UAAU,CAACL,QAAQkD,KAAKL,OAAO,CAAC1H,CAAAA,GAAAA,qCAAAA,EAAKgD,GAAG,EAAE6B,IAAIJ,MAAM,MAAM,IAAI;YACrE,IAAIkD,OAAOI,KAAKxC,KAAK,CAACV,IAAIJ,MAAM;YAChC,IAAId,MAAMiE,eACRjC,IAAIkC,IAAI,CAAC,IAAIC,6BAAOH,MAAM;gBAACrB,MAAMgB;YAAO;iBAExC3B,IAAIkC,IAAI,CAACF;QAEb;QAGF,OAAOhC;IACT;IAEA,yCAAA;IACA,MAAMhF,QAAQkE,GAAa,EAAElB,IAAqB,EAAgB;QAChE,OAAO,IAAI,CAAC6D,WAAW,CAAC3C,KAAKlB;IAC/B;IAEA,MAAMqE,OAAOjD,QAAkB,EAAiB;QAC9CA,WAAW,IAAI,CAACD,cAAc,CAACC;QAC/B,IAAI,CAAC,IAAI,CAACrE,KAAK,CAACuF,GAAG,CAAClB,aAAa,CAAC,IAAI,CAAC9B,IAAI,CAACgD,GAAG,CAAClB,WAC9C,MAAM,IAAImB,0CAAQ,UAAUnB,UAAU;QAGxC,IAAI,CAACrE,KAAK,CAACuH,MAAM,CAAClD;QAClB,IAAI,CAAC9B,IAAI,CAACgF,MAAM,CAAClD;QACjB,IAAI,CAAC3B,QAAQ,CAAC6E,MAAM,CAAClD;QAErB,MAAM,IAAI,CAAC0B,gBAAgB,CAAC;YAC1BC,MAAM;YACN1G,MAAM+E;QACR;QAEA,IAAI,CAAC6B,aAAa,CAAC;YACjBF,MAAM;YACN1G,MAAM+E;QACR;QAEA,OAAOmD,QAAQnF,OAAO;IACxB;IAEA,MAAMtC,OAAOoE,GAAa,EAAiB;QACzCA,MAAM,IAAI,CAACC,cAAc,CAACD;QAC1B,IAAI,IAAI,CAAC5B,IAAI,CAACgD,GAAG,CAACpB,MAChB,OAAOqD,QAAQnF,OAAO;QAGxB,IAAI,IAAI,CAACrC,KAAK,CAACuF,GAAG,CAACpB,MACjB,MAAM,IAAIqB,0CAAQ,UAAUrB,KAAK;QAGnC,IAAIM,OAAOnF,CAAAA,GAAAA,qCAAAA,EAAKqF,KAAK,CAACR,KAAKM,IAAI;QAC/B,MAAON,QAAQM,KAAM;YACnB,IAAI,IAAI,CAAClC,IAAI,CAACgD,GAAG,CAACpB,MAChB;YAGF,IAAI,CAAC5B,IAAI,CAACO,GAAG,CAACqB,KAAK,IAAI3B;YACvB,MAAM,IAAI,CAACuD,gBAAgB,CAAC;gBAC1BC,MAAM;gBACN1G,MAAM6E;YACR;YAEA,IAAI,CAAC+B,aAAa,CAAC;gBACjBF,MAAM;gBACN1G,MAAM6E;YACR;YAEAA,MAAM7E,CAAAA,GAAAA,qCAAAA,EAAKmG,OAAO,CAACtB;QACrB;QAEA,OAAOqD,QAAQnF,OAAO;IACxB;IAEA,MAAMoF,OAAOpD,QAAkB,EAAiB;QAC9CA,WAAW,IAAI,CAACD,cAAc,CAACC;QAE/B,IAAI,IAAI,CAAC9B,IAAI,CAACgD,GAAG,CAAClB,WAAW;YAC3B,IAAIF,MAAME,WAAW/E,CAAAA,GAAAA,qCAAAA,EAAKgD,GAAG;YAC7B,KAAK,IAAI+B,YAAY,IAAI,CAACrE,KAAK,CAAC0H,IAAI,GAClC,IAAIrD,SAASG,UAAU,CAACL,MAAM;gBAC5B,IAAI,CAACnE,KAAK,CAACuH,MAAM,CAAClD;gBAClB,MAAM,IAAI,CAAC0B,gBAAgB,CAAC;oBAC1BC,MAAM;oBACN1G,MAAM+E;gBACR;gBAEA,IAAI,CAAC6B,aAAa,CAAC;oBACjBF,MAAM;oBACN1G,MAAM+E;gBACR;YACF;YAGF,KAAK,IAAIsD,WAAW,IAAI,CAACpF,IAAI,CAACmF,IAAI,GAChC,IAAIC,QAAQnD,UAAU,CAACL,MAAM;gBAC3B,IAAI,CAAC5B,IAAI,CAACgF,MAAM,CAACI;gBACjB,IAAI,CAACjF,QAAQ,CAAC6E,MAAM,CAACI;gBACrB,MAAM,IAAI,CAAC5B,gBAAgB,CAAC;oBAC1BC,MAAM;oBACN1G,MAAM+E;gBACR;gBAEA,IAAI,CAAC6B,aAAa,CAAC;oBACjBF,MAAM;oBACN1G,MAAMqI;gBACR;YACF;YAGF,KAAK,IAAItD,YAAY,IAAI,CAAC5B,QAAQ,CAACiF,IAAI,GACrC,IAAIrD,SAASG,UAAU,CAACL,MAAM;gBAC5B,IAAI,CAAC1B,QAAQ,CAAC8E,MAAM,CAAClD;gBACrB,MAAM,IAAI,CAAC0B,gBAAgB,CAAC;oBAC1BC,MAAM;oBACN1G,MAAM+E;gBACR;YACF;YAGF,IAAI,CAAC9B,IAAI,CAACgF,MAAM,CAAClD;YACjB,MAAM,IAAI,CAAC0B,gBAAgB,CAAC;gBAC1BC,MAAM;gBACN1G,MAAM+E;YACR;YAEA,IAAI,CAAC6B,aAAa,CAAC;gBACjBF,MAAM;gBACN1G,MAAM+E;YACR;QACF,OAAO,IAAI,IAAI,CAACrE,KAAK,CAACuF,GAAG,CAAClB,WAAW;YACnC,IAAI,CAACrE,KAAK,CAACuH,MAAM,CAAClD;YAClB,MAAM,IAAI,CAAC0B,gBAAgB,CAAC;gBAC1BC,MAAM;gBACN1G,MAAM+E;YACR;YAEA,IAAI,CAAC6B,aAAa,CAAC;gBACjBF,MAAM;gBACN1G,MAAM+E;YACR;QACF;QAEA,OAAOmD,QAAQnF,OAAO;IACxB;IAEA,MAAM3C,IAAIE,MAAgB,EAAEE,WAAqB,EAAE;QACjDF,SAAS,IAAI,CAACwE,cAAc,CAACxE;QAE7B,IAAI,IAAI,CAAC2C,IAAI,CAACgD,GAAG,CAAC3F,SAAS;YACzB,IAAI,CAAC,IAAI,CAAC2C,IAAI,CAACgD,GAAG,CAACzF,cAAc;gBAC/B,IAAI,CAACyC,IAAI,CAACO,GAAG,CAAChD,aAAa,IAAI0C;gBAC/B,MAAM,IAAI,CAACuD,gBAAgB,CAAC;oBAC1BC,MAAM;oBACN1G,MAAMQ;gBACR;gBAEA,IAAI,CAACoG,aAAa,CAAC;oBACjBF,MAAM;oBACN1G,MAAMQ;gBACR;YACF;YAEA,IAAIqE,MAAMvE,SAASN,CAAAA,GAAAA,qCAAAA,EAAKgD,GAAG;YAC3B,KAAK,IAAIqF,WAAW,IAAI,CAACpF,IAAI,CAACmF,IAAI,GAChC,IAAIC,QAAQnD,UAAU,CAACL,MAAM;gBAC3B,IAAIyD,WAAWtI,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACN,aAAa6H,QAAQ9C,KAAK,CAACV,IAAIJ,MAAM;gBAC9D,IAAI,CAAC,IAAI,CAACxB,IAAI,CAACgD,GAAG,CAACqC,WAAW;oBAC5B,IAAI,CAACrF,IAAI,CAACO,GAAG,CAAC8E,UAAU,IAAIpF;oBAC5B,MAAM,IAAI,CAACuD,gBAAgB,CAAC;wBAC1BC,MAAM;wBACN1G,MAAMQ;oBACR;oBACA,IAAI,CAACoG,aAAa,CAAC;wBACjBF,MAAM;wBACN1G,MAAMsI;oBACR;gBACF;YACF;YAGF,KAAK,IAAI,CAACvD,UAAUnE,KAAK,IAAI,IAAI,CAACF,KAAK,CACrC,IAAIqE,SAASG,UAAU,CAACL,MAAM;gBAC5B,IAAIyD,WAAWtI,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACN,aAAauE,SAASQ,KAAK,CAACV,IAAIJ,MAAM;gBAC/D,IAAI8D,SAAS,IAAI,CAAC7H,KAAK,CAACuF,GAAG,CAACqC;gBAC5B,IAAI,CAAC5H,KAAK,CAAC8C,GAAG,CAAC8E,UAAU1H;gBACzB,MAAM,IAAI,CAAC6F,gBAAgB,CAAC;oBAC1BC,MAAM;oBACN1G,MAAMsI;oBACN3B,OAAO/F;gBACT;gBAEA,IAAI,CAACgG,aAAa,CAAC;oBACjBF,MAAM6B,SAAS,WAAW;oBAC1BvI,MAAMsI;gBACR;YACF;QAEJ,OACE,MAAM,IAAI,CAACpB,QAAQ,CAAC5G,QAAQE;IAEhC;IAEAW,iBAAiB4D,QAAkB,EAAc;QAC/C,OAAO,IAAIyD,iCAAW,IAAI,EAAEzD;IAC9B;IAEA3D,kBAAkB2D,QAAkB,EAAEiB,OAAqB,EAAe;QACxE,OAAO,IAAIyC,kCAAY,IAAI,EAAE1D,UAAUiB;IACzC;IAEA0C,aAAa3D,QAAkB,EAAY;QACzC,OAAO,IAAI,CAACD,cAAc,CAACC;IAC7B;IAEA,yCAAA;IACA,MAAMC,SAASD,QAAkB,EAAqB;QACpD,OAAO,IAAI,CAAC2D,YAAY,CAAC3D;IAC3B;IAEA4D,aAAa5D,QAAkB,EAAY;QACzC,IAAIc,UAAU,IAAI,CAAC1C,QAAQ,CAACU,GAAG,CAACkB;QAChC,IAAI,CAACc,SACH,MAAM,IAAIK,0CAAQ,UAAUnB,UAAU;QAExC,OAAOc;IACT;IAEA,yCAAA;IACA,MAAM+C,SAAS7D,QAAkB,EAAqB;QACpD,OAAO,IAAI,CAAC4D,YAAY,CAAC5D;IAC3B;IAEA,MAAMc,QAAQgD,MAAgB,EAAE7I,IAAc,EAAE;QAC9C6I,SAAS,IAAI,CAAC/D,cAAc,CAAC+D;QAC7B7I,OAAO,IAAI,CAAC8E,cAAc,CAAC9E;QAC3B,IAAI,CAACmD,QAAQ,CAACK,GAAG,CAACxD,MAAM6I;QACxB,MAAM,IAAI,CAACpC,gBAAgB,CAAC;YAC1BC,MAAM;kBACN1G;oBACA6I;QACF;IACF;IAEAC,WAAW/D,QAAkB,EAAW;QACtCA,WAAW,IAAI,CAACD,cAAc,CAACC;QAC/B,OAAO,IAAI,CAACrE,KAAK,CAACuF,GAAG,CAAClB,aAAa,IAAI,CAAC9B,IAAI,CAACgD,GAAG,CAAClB;IACnD;IAEA,yCAAA;IACA,MAAMwD,OAAOxD,QAAkB,EAAoB;QACjD,OAAO,IAAI,CAAC+D,UAAU,CAAC/D;IACzB;IAEA6B,cAAcmC,KAAY,EAAE;QAC1B,IAAI,CAAC1F,MAAM,CAACwE,IAAI,CAACkB;QACjB,IAAI,IAAI,CAAC3F,QAAQ,CAAC4F,IAAI,KAAK,GACzB;QAGF,eAAA;QACA,IAAI,CAACzF,WAAW,CAACsE,IAAI,CAACkB;QACtBE,aAAa,IAAI,CAACC,aAAa;QAE/B,IAAI,CAACA,aAAa,GAAGC,WAAW;YAC9B,IAAI9F,SAAS,IAAI,CAACE,WAAW;YAC7B,IAAI,CAACA,WAAW,GAAG,EAAE;YAErB,KAAK,IAAI,CAACsB,KAAKzB,SAAS,IAAI,IAAI,CAACA,QAAQ,CAAE;gBACzC,IAAI,CAACyB,IAAI4C,QAAQ,CAACzH,CAAAA,GAAAA,qCAAAA,EAAKgD,GAAG,GACxB6B,OAAO7E,CAAAA,GAAAA,qCAAAA,EAAKgD,GAAG;gBAGjB,IAAI+F,MAAM/I,IAAI,CAACkF,UAAU,CAACL,MACxB,KAAK,IAAIuE,WAAWhG,SAClBgG,QAAQC,OAAO,CAAChG;YAGtB;QACF,GAAG;IACL;IAEAiG,gBAAgBtF,MAAc,EAAE;QAC9B,IAAI,CAACV,cAAc,CAACuE,IAAI,CAAC7D;QACzB,IAAI,IAAI,CAACvB,mBAAmB,KAAK,IAAI,CAACa,cAAc,CAACmB,MAAM,EACzD,IAAI,CAAC9B,QAAQ,CAAC+B,IAAI,CAAC;IAEvB;IAEA,MAAM+B,iBAAiBsC,KAAkB,EAAE;QACzC,sDAAA;QACA,MAAO,IAAI,CAACzF,cAAc,CAACmB,MAAM,GAAG,IAAI,CAAChC,mBAAmB,CAC1D,MAAM,IAAIyF,QAAQnF,CAAAA,UAAW,IAAI,CAACL,uBAAuB,CAACmF,IAAI,CAAC9E;QAGjE,MAAMmF,QAAQqB,GAAG,CACf,IAAI,CAACjG,cAAc,CAACkG,GAAG,CAACC,CAAAA,eACtB,IAAI,CAAC5G,IAAI,CAAC6G,SAAS,CAAC3F,SAAS,CAAC0F,cAAc;gBAACV;aAAM;IAGzD;IAEAY,MACE9E,GAAa,EACbR,EAAgD,EAChDV,IAAoB,EACQ;QAC5BkB,MAAM,IAAI,CAACC,cAAc,CAACD;QAC1B,IAAIuE,UAAU,IAAIQ,8BAAQvF,IAAIV;QAC9B,IAAIP,WAAW,IAAI,CAACA,QAAQ,CAACS,GAAG,CAACgB;QACjC,IAAI,CAACzB,UAAU;YACbA,WAAW,IAAIyG;YACf,IAAI,CAACzG,QAAQ,CAACI,GAAG,CAACqB,KAAKzB;QACzB;QAEAA,SAAS0G,GAAG,CAACV;QAEb,OAAOlB,QAAQnF,OAAO,CAAC;YACrBgH,aAAaA;gBACX3G,WAAWpB,CAAAA,GAAAA,gEAAAA,EAAWoB;gBACtBA,SAAS6E,MAAM,CAACmB;gBAEhB,IAAIhG,SAAS4F,IAAI,KAAK,GACpB,IAAI,CAAC5F,QAAQ,CAAC6E,MAAM,CAACpD;gBAGvB,OAAOqD,QAAQnF,OAAO;YACxB;QACF;IACF;IAEA,MAAMiH,eACJnF,GAAa,EACboF,QAAkB,EAClBtG,IAAoB,EACG;QACvB,IAAIoC,WAAW,MAAM,IAAI,CAACc,QAAQ,CAACoD,UAAU;QAC7C,IAAIC,MAAMC,OAAOpE;QACjB,IAAI1C,SAAS,IAAI,CAACA,MAAM,CAACkC,KAAK,CAAC2E;QAC/B,IAAIE,SAASzG,KAAKyG,MAAM;QACxB,IAAIA,QACF/G,SAASA,OAAOgH,MAAM,CACpBtB,CAAAA,QAAS,CAACqB,OAAOE,IAAI,CAACC,CAAAA,IAAKxB,MAAM/I,IAAI,CAACkF,UAAU,CAACqF,IAAIvK,CAAAA,GAAAA,qCAAAA,EAAKgD,GAAG;QAIjE,OAAOK;IACT;IAEA,MAAMmH,cAAc3F,GAAa,EAAEoF,QAAkB,EAAiB;QACpE,MAAM,IAAI,CAACnE,SAAS,CAACmE,UAAU,KAAK,IAAI,CAAC5G,MAAM,CAACoB,MAAM;IACxD;IAEAvC,iBACEuI,SAAwB,EACxBC,OAAiB,EACjBvF,IAAc,EACH;QACX,OAAOjD,CAAAA,GAAAA,yCAAAA,EAAiB,IAAI,EAAEuI,WAAWC,SAASvF;IACpD;IAEAhD,eAAewI,UAAkB,EAAED,OAAiB,EAAa;QAC/D,OAAOvI,CAAAA,GAAAA,yCAAAA,EAAe,IAAI,EAAEwI,YAAYD;IAC1C;IAEAtI,cAAcwI,SAA0B,EAAa;QACnD,OAAOxI,CAAAA,GAAAA,yCAAAA,EAAc,IAAI,EAAEwI;IAC7B;AACF;AAEA,MAAMhB;IAIJrI,YACE8C,EAAgD,EAChD2B,OAAuB,CACvB;QACA,IAAI,CAAC3B,EAAE,GAAGA;QACV,IAAI,CAAC2B,OAAO,GAAGA;IACjB;IAEAqD,QAAQhG,MAAoB,EAAE;QAC5B,IAAI+G,SAAS,IAAI,CAACpE,OAAO,CAACoE,MAAM;QAChC,IAAIA,QACF/G,SAASA,OAAOgH,MAAM,CACpBtB,CAAAA,QAAS,CAACqB,OAAOE,IAAI,CAACC,CAAAA,IAAKxB,MAAM/I,IAAI,CAACkF,UAAU,CAACqF,IAAIvK,CAAAA,GAAAA,qCAAAA,EAAKgD,GAAG;QAIjE,IAAIK,OAAOoB,MAAM,GAAG,GAClB,IAAI,CAACJ,EAAE,CAAC,MAAMhB;IAElB;AACF;AAEO,MAAM6C,kDAAgB1E;IAG3BD,YAAYsJ,IAAY,EAAE7K,IAAc,EAAE8K,OAAe,CAAE;QACzD,KAAK,CAAC,GAAGD,KAAI,EAAA,EAAK7K,KAAI,CAAA,EAAI8K,SAAS;QACnC,IAAI,CAACnD,IAAI,GAAG;QACZ,IAAI,CAACkD,IAAI,GAAGA;QACZ,IAAI,CAAC7K,IAAI,GAAGA;QACZwB,MAAMuJ,iBAAiB,GAAG,IAAI,EAAE,IAAI,CAACxJ,WAAW;IAClD;AACF;AAEA,MAAMiH,yCAAmB/G,CAAAA,GAAAA,sBAAAA;IAKvBF,YAAY0C,EAAc,EAAEc,QAAkB,CAAE;QAC9C,KAAK;QACL,IAAI,CAACd,EAAE,GAAGA;QACV,IAAI,CAACc,QAAQ,GAAGA;QAChB,IAAI,CAACiG,OAAO,GAAG;QACf,IAAI,CAACC,SAAS,GAAG;IACnB;IAEAC,QAAQ;QACN,IAAI,IAAI,CAACF,OAAO,EACd;QAGF,IAAI,CAACA,OAAO,GAAG;QACf,IAAI,CAAC/G,EAAE,CAAC4C,QAAQ,CAAC,IAAI,CAAC9B,QAAQ,EAAEoG,IAAI,CAClCxF,CAAAA;YACE,IAAI,CAACsF,SAAS,IAAItF,IAAIyF,UAAU;YAChC,IAAI,CAACvD,IAAI,CAAClC;YACV,IAAI,CAACkC,IAAI,CAAC;QACZ,GACAwD,CAAAA;YACE,IAAI,CAAC3G,IAAI,CAAC,SAAS2G;QACrB;IAEJ;AACF;AAEA,MAAM5C,0CAAoB/G,CAAAA,GAAAA,sBAAAA;IAMxBH,YAAY0C,EAAc,EAAEc,QAAkB,EAAEiB,OAAqB,CAAE;QACrE,KAAK,CAAC;YAACsF,WAAW;YAAMC,aAAa;QAAI;QACzC,IAAI,CAACtH,EAAE,GAAGA;QACV,IAAI,CAACc,QAAQ,GAAGA;QAChB,IAAI,CAACiB,OAAO,GAAGA;QACf,IAAI,CAACI,MAAM,GAAGoF,OAAOC,KAAK,CAAC;IAC7B;IAEAC,OACEC,KAAsB,EACtB7E,QAAa,EACb8E,QAAiC,EACjC;QACA,IAAIC,IAAI,OAAOF,UAAU,WAAWH,OAAOzD,IAAI,CAAC4D,OAAO7E,YAAY6E;QACnE,IAAI,CAACvF,MAAM,GAAGoF,OAAO/F,MAAM,CAAC;YAAC,IAAI,CAACW,MAAM;YAAEyF;SAAE;QAC5CD;IACF;IAEAE,OAAOF,QAAiC,EAAE;QACxC,IAAI,CAAC3H,EAAE,CACJ6B,SAAS,CAAC,IAAI,CAACf,QAAQ,EAAE,IAAI,CAACqB,MAAM,EAAE,IAAI,CAACJ,OAAO,EAClDmF,IAAI,CAACS,UACLG,KAAK,CAACH;IACX;AACF;AAEA,MAAMI,gCAAU;AAChB,MAAMC,gCAAU;AAChB,MAAM3E,gCAAU;AAChB,MAAM4E,+BAAS;AAEf,MAAMC;IAMJ5K,YAAY+E,IAAY,CAAE;QACxB,IAAI,CAACA,IAAI,GAAGA;QACZ,IAAI8F,MAAMC,KAAKD,GAAG;QAClB,IAAI,CAACE,KAAK,GAAGF;QACb,IAAI,CAACG,KAAK,GAAGH;QACb,IAAI,CAACI,KAAK,GAAGJ;QACb,IAAI,CAACK,SAAS,GAAGL;IACnB;IAEAM,SAAS;QACP,IAAIN,MAAMC,KAAKD,GAAG;QAClB,IAAI,CAACE,KAAK,GAAGF;QACb,IAAI,CAACI,KAAK,GAAGJ;IACf;IAEAO,OAAOrG,IAAY,EAAE;QACnB,IAAI8F,MAAMC,KAAKD,GAAG;QAClB,IAAI,CAACG,KAAK,GAAGH;QACb,IAAI,CAACI,KAAK,GAAGJ;QACb,IAAI,CAAC9F,IAAI,GAAGA;IACd;IAEAsG,UAAkB;QAChB,OAAO;IACT;IAEA3L,OAAa;QACX,OAAOoG,2BAAKwF,SAAS,CAAC,IAAI;IAC5B;AACF;AAEA,MAAMxF;IACJyF,MAAc,EAAdA;IACAC,MAAc,EAAdA;IACAzG,OAAe,EAAfA;IACA0G,QAAgB,EAAhBA;IACAC,MAAc,EAAdA;IACAC,MAAc,EAAdA;IACAC,OAAe,EAAfA;IACAnE,OAAe,EAAfA;IACAoE,UAAkB,EAAlBA;IACAC,SAAiB,EAAjBA;IACAC,UAAkB,EAAlBA;IACAC,UAAkB,EAAlBA;IACAC,UAAkB,EAAlBA;IACAC,cAAsB,EAAtBA;IACAnB,QAAc,IAAID,OAAlBC;IACAC,QAAc,IAAIF,OAAlBE;IACAC,QAAc,IAAIH,OAAlBG;IACAC,YAAkB,IAAIJ,OAAtBI;IAEA,OAAOI,UAAUlG,KAAY,EAAQ;QACnC,IAAI1F,OAAO,IAAIoG;QACfpG,KAAKqF,IAAI,GAAGK,MAAML,IAAI;QACtBrF,KAAK+H,IAAI,GAAGrC,MAAMiG,OAAO;QACzB3L,KAAKqM,OAAO,GAAG3G,MAAM2F,KAAK;QAC1BrL,KAAKsM,OAAO,GAAG5G,MAAM4F,KAAK;QAC1BtL,KAAKuM,OAAO,GAAG7G,MAAM6F,KAAK;QAC1BvL,KAAKwM,WAAW,GAAG9G,MAAM8F,SAAS;QAClCxL,KAAKqL,KAAK,GAAG,IAAID,KAAK1F,MAAM2F,KAAK;QACjCrL,KAAKsL,KAAK,GAAG,IAAIF,KAAK1F,MAAM4F,KAAK;QACjCtL,KAAKuL,KAAK,GAAG,IAAIH,KAAK1F,MAAM6F,KAAK;QACjCvL,KAAKwL,SAAS,GAAG,IAAIJ,KAAK1F,MAAM8F,SAAS;QACzC,OAAOxL;IACT;IAEAC,SAAkB;QAChB,OAAO,AAAC,CAAA,IAAI,CAACoF,IAAI,GAAG0F,6BAAAA,MAAaA;IACnC;IAEA3K,cAAuB;QACrB,OAAO,AAAC,CAAA,IAAI,CAACiF,IAAI,GAAG2F,6BAAAA,MAAaA;IACnC;IAEAyB,gBAAyB;QACvB,OAAO;IACT;IAEAC,oBAA6B;QAC3B,OAAO;IACT;IAEAC,iBAA0B;QACxB,OAAO,AAAC,CAAA,IAAI,CAACtH,IAAI,GAAG4F,4BAAAA,MAAY5E;IAClC;IAEAuG,SAAkB;QAChB,OAAO;IACT;IAEAC,WAAoB;QAClB,OAAO;IACT;AACF;AAEA,MAAMhG;IAEJ,CAAA,IAAK,CAAL;IAEAvG,YAAYoG,IAAY,EAAEhB,KAA+B,CAAE;QACzD,IAAI,CAACgB,IAAI,GAAGA;QACZ,IAAI,CAAC,CAAA,IAAK,GAAGhB,MAAML,IAAI;IACzB;IAEApF,SAAkB;QAChB,OAAO,AAAC,CAAA,IAAI,CAAC,CAAA,IAAK,GAAGgL,4BAAAA,MAAYF;IACnC;IAEA3K,cAAuB;QACrB,OAAO,AAAC,CAAA,IAAI,CAAC,CAAA,IAAK,GAAG6K,4BAAAA,MAAYD;IACnC;IAEAyB,gBAAyB;QACvB,OAAO;IACT;IAEAC,oBAA6B;QAC3B,OAAO;IACT;IAEAC,iBAA0B;QACxB,OAAO,AAAC,CAAA,IAAI,CAAC,CAAA,IAAK,GAAG1B,4BAAAA,MAAY5E;IACnC;IAEAuG,SAAkB;QAChB,OAAO;IACT;IAEAC,WAAoB;QAClB,OAAO;IACT;AACF;AAEO,MAAMtH,kDAAa2F;IAExB5K,YAAY6E,MAAc,EAAEE,IAAY,CAAE;QACxC,KAAK,CAAC0F,gCAAU1F;QAChB,IAAI,CAACF,MAAM,GAAGA;IAChB;IAEAY,OAAe;QACb,KAAK,CAAC0F;QACN,OAAOlB,OAAOzD,IAAI,CAAC,IAAI,CAAC3B,MAAM;IAChC;IAEAG,MAAMH,MAAc,EAAEE,IAAY,EAAE;QAClC,KAAK,CAACqG,OAAOX,gCAAU1F;QACvB,IAAI,CAACF,MAAM,GAAGA;IAChB;IAEAwG,UAAkB;QAChB,OAAO,IAAI,CAACxG,MAAM,CAACgF,UAAU;IAC/B;AACF;AAEA,MAAMlI,wCAAkBiJ;IACtB5K,aAAc;QACZ,KAAK,CAAC0K;IACR;AACF;AAEO,SAAS5F,0CAAWN,QAAyB;IAClD,IAAI,OAAOA,aAAa,YAAYA,SAASK,MAAM,YAAYxE,CAAAA,GAAAA,+BAAAA,GAC7D,OAAOmE;IAGT,IAAIgI,iBAAkChI;IACtC,aAAA;IACA,IAAIiI,QAAQC,OAAO,EACjB,8FAAA;IACA,qEAAA;IACAF,iBACEA,0BAA0BvC,SACtBuC,iBACAvC,OAAOzD,IAAI,CAACgG;IAGpB,IAAItJ,SAAS+G,OAAOJ,UAAU,CAAC2C;IAC/B,IAAIG,SAAS,IAAItM,CAAAA,GAAAA,+BAAAA,EAAa6C;IAC9B,IAAI2B,SAASoF,OAAOzD,IAAI,CAACmG;IACzB,IAAIzJ,SAAS;QACX,IAAI,OAAOsJ,mBAAmB,UAC5B3H,OAAOG,KAAK,CAACwH;aAEb3H,OAAO5C,GAAG,CAACuK;;IAIf,OAAO3H;AACT;AAEA,MAAMlC,uCAAiB1B;IAIrBjB,YAAYgB,EAAU,EAAEyB,MAAc,CAAE;QACtC,gCAAA;QACA,aAAA;QACA,KAAK;QACL,IAAI,CAACzB,EAAE,GAAGA;QACV,IAAI,CAAC4L,QAAQ,GAAG,CAACC,YAAY9J,OAC3BxC,CAAAA,GAAAA,8CAAAA,EAAWgC,YAAY,GAAGC,SAAS,CAACC,QAAQ;gBAACoK;gBAAY9J;aAAK;QAEhE,IAAI,CAAC6J,QAAQ,CAAC,mBAAmB;YAC/BrM,CAAAA,GAAAA,8CAAAA,EAAWgC,YAAY,GAAGM,mBAAmB,CAAC2E,CAAAA;gBAC5C,OAAQA,MAAMrC,IAAI;oBAChB,KAAK;wBACH,IAAI,CAAChG,KAAK,CAAC8C,GAAG,CAACuF,MAAM/I,IAAI,EAAE+I,MAAMpC,KAAK;wBACtC;oBACF,KAAK;wBACH,IAAI,CAACjG,KAAK,CAACuH,MAAM,CAACc,MAAM/I,IAAI;wBAC5B,IAAI,CAACiD,IAAI,CAACgF,MAAM,CAACc,MAAM/I,IAAI;wBAC3B,IAAI,CAACmD,QAAQ,CAAC8E,MAAM,CAACc,MAAM/I,IAAI;wBAC/B;oBACF,KAAK;wBACH,IAAI,CAACiD,IAAI,CAACO,GAAG,CAACuF,MAAM/I,IAAI,EAAE,IAAIkD;wBAC9B;oBACF,KAAK;wBACH,IAAI,CAACC,QAAQ,CAACK,GAAG,CAACuF,MAAM/I,IAAI,EAAE+I,MAAMF,MAAM;wBAC1C;gBACJ;YACF;SACD;IACH;IAEA,OAAOnF,YAAYC,IAAwB,EAAY;QACrD,OAAO3B,CAAAA,GAAAA,gEAAAA,EAAWK,gCAAUwB,GAAG,CAACF,KAAKpB,EAAE;IACzC;IAEA4B,YAAgC;QAC9B,aAAA;QACA,OAAO;YACL5B,IAAI,IAAI,CAACA,EAATA;QACF;IACF;IAEAuD,UACEf,QAAkB,EAClBgB,QAAyB,EACzBC,OAAqB,EACN;QACf,KAAK,CAACF,UAAUf,UAAUgB,UAAUC;QACpC,IAAII,SAASC,0CAAWN;QACxB,OAAO,IAAI,CAACoI,QAAQ,CAAC,aAAa;YAACpJ;YAAUqB;YAAQJ;SAAQ;IAC/D;IAEAgC,OAAOjD,QAAkB,EAAiB;QACxC,KAAK,CAACiD,OAAOjD;QACb,OAAO,IAAI,CAACoJ,QAAQ,CAAC,UAAU;YAACpJ;SAAS;IAC3C;IAEAtE,OAAOoE,GAAa,EAAiB;QACnC,KAAK,CAACpE,OAAOoE;QACb,OAAO,IAAI,CAACsJ,QAAQ,CAAC,UAAU;YAACtJ;SAAI;IACtC;IAEAsD,OAAOpD,QAAkB,EAAiB;QACxC,KAAK,CAACoD,OAAOpD;QACb,OAAO,IAAI,CAACoJ,QAAQ,CAAC,UAAU;YAACpJ;SAAS;IAC3C;IAEA3E,IAAIE,MAAgB,EAAEE,WAAqB,EAAiB;QAC1D,KAAK,CAACJ,IAAIE,QAAQE;QAClB,OAAO,IAAI,CAAC2N,QAAQ,CAAC,OAAO;YAAC7N;YAAQE;SAAY;IACnD;IAEAqF,QAAQgD,MAAgB,EAAE7I,IAAc,EAAiB;QACvD,KAAK,CAAC6F,QAAQgD,QAAQ7I;QACtB,OAAO,IAAI,CAACmO,QAAQ,CAAC,WAAW;YAACtF;YAAQ7I;SAAK;IAChD;AACF;AAEA2B,CAAAA,GAAAA,2CAAAA,EAA0B,GAAGE,CAAAA,GAAAA,gEAAAA,EAAYwM,OAAO,CAAA,SAAA,CAAW,EAAE7L;AAC7Db,CAAAA,GAAAA,2CAAAA,EAA0B,GAAGE,CAAAA,GAAAA,gEAAAA,EAAYwM,OAAO,CAAA,SAAA,CAAW,EAAEnK;AAC7DvC,CAAAA,GAAAA,2CAAAA,EAA0B,GAAGE,CAAAA,GAAAA,gEAAAA,EAAYwM,OAAO,CAAA,KAAA,CAAO,EAAEhH;AACzD1F,CAAAA,GAAAA,2CAAAA,EAA0B,GAAGE,CAAAA,GAAAA,gEAAAA,EAAYwM,OAAO,CAAA,KAAA,CAAO,EAAE7H;AACzD7E,CAAAA,GAAAA,2CAAAA,EAA0B,GAAGE,CAAAA,GAAAA,gEAAAA,EAAYwM,OAAO,CAAA,UAAA,CAAY,EAAEnL;;;;;;;;;;;;;AIniCvD,MAAM2L;IACXC,UAAyB,IAAIjF,MAA7BiF;IAKAvN,YAAYwN,cAAuC,EAAEC,QAAoB,CAAE;QACzE,IAAID,0BAA0BjN,CAAAA,GAAAA,8CAAAA,GAC5B,IAAI,CAACmN,QAAQ,GAAG,IAAIzM,CAAAA,GAAAA,yCAAAA,EAASuM;aAE7B,IAAI,CAACE,QAAQ,GAAGF;QAElB,IAAI,CAACC,QAAQ,GAAGA;QAChB,IAAI,CAAClM,IAAI,GAAGkM,SAASrK,GAAG;IAC1B;IAEA,OAAOjB,YAAYC,IAAS,EAAa;QACvC,IAAIM,KAAK,IAAI4K,0CAAUlL,KAAKsL,QAAQ,EAAEtL,KAAKqL,QAAQ;QACnD,IAAIrL,KAAKmL,OAAO,IAAI,MAAM7K,GAAG6K,OAAO,GAAGnL,KAAKmL,OAAO;QACnD,OAAO7K;IACT;IAEAE,YAKG;QACD,OAAO;YACLI,OAAO;YACP0K,UAAU,IAAI,CAACA,QAAQ;YACvBD,UAAU,IAAI,CAACA,QAAQ;YACvBF,SAAS,IAAI,CAACA,OAAdA;QACF;IACF;IAEAI,eAAenK,QAAkB,EAAY;QAC3CA,WAAW,IAAI,CAACD,cAAc,CAACC;QAC/B,IAAI,IAAI,CAAC+J,OAAO,CAAC7I,GAAG,CAAClB,WACnB,MAAM,IAAImB,8BAAQ,UAAUnB,UAAU;QAExC,OAAOA;IACT;IAEAoK,aAAapK,QAAkB,EAAY;QACzCA,WAAW,IAAI,CAACmK,cAAc,CAACnK;QAC/B,IAAI,CAAC,IAAI,CAAC+D,UAAU,CAAC/D,WACnB,MAAM,IAAImB,8BAAQ,UAAUnB,UAAU;QAExC,OAAOA;IACT;IAEAqK,WAAWrK,QAAkB,EAAW;QACtCA,WAAW,IAAI,CAACD,cAAc,CAACC;QAC/B,0DAAA;QACA,IAAI,QAACI,IAAI,OAAEN,GAAG,QAAEO,IAAAA,EAAK,GAAGpF,CAAAA,GAAAA,qCAAAA,EAAKqF,KAAK,CAACN;QACnC,IAAIsK,WAAWxK,IAAIU,KAAK,CAACJ,KAAKV,MAAM,EAAEe,KAAK,CAACxF,CAAAA,GAAAA,qCAAAA,EAAKgD,GAAG,EAAEyC,MAAM,CAACL;QAC7D,MAAOiK,SAAS5K,MAAM,CAAE;YACtBM,WAAW/E,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACqE,SAASkK;YAC9B,IAAI1H,OAAO0H,SAASC,GAAG;YACvB,IAAI,IAAI,CAACR,OAAO,CAAC7I,GAAG,CAAClB,WACnB,OAAO;iBACF,IACL,IAAI,CAACkK,QAAQ,YAAYzM,CAAAA,GAAAA,yCAAAA,KACzB,IAAI,CAACyM,QAAQ,CAAC9L,QAAQ,CAAC8C,GAAG,CAAClB,WAE3B,OAAO;iBACF;gBACL,gDAAA;gBACA,6DAAA;gBACA,IAAIwK,SAASvP,CAAAA,GAAAA,qCAAAA,EAAK+C,OAAO,CAACgC,UAAU;gBACpC,IAAIwK,WAAWxK,UACb,OAAO;gBAET,IAAI;oBACF,KAAK,IAAIyK,UAAU,IAAI,CAAChI,WAAW,CAAC+H,QAAQ;wBAAC3H,eAAe;oBAAI,GAAI;wBAClE,IAAI,OAAO4H,WAAW,UACpB,OAAO,sCAAP;6BACK,IAAIA,OAAO7H,IAAI,KAAKA,MAAM;4BAC/B,IAAI6H,OAAO5B,cAAc,IACvB,OAAO;wBAEX;oBACF;gBACF,EAAE,OAAO6B,GAAG;oBACV,IAAIA,EAAE5E,IAAI,KAAK,UACb,OAAO;oBAET,MAAM4E;gBACR;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAMC,kBAAkB3K,QAAkB,EAAqB;QAC7DA,WAAW,MAAM,IAAI,CAACD,cAAc,CAACC;QACrC,IAAIsD,UAAUrI,CAAAA,GAAAA,qCAAAA,EAAKmG,OAAO,CAACpB;QAC3B,IAAI,IAAI,CAAC+D,UAAU,CAACT,YAAY,CAAC,IAAI,CAAC4G,QAAQ,CAACnG,UAAU,CAACT,UACxD,MAAM,IAAI,CAAC4G,QAAQ,CAACxO,MAAM,CAAC4H;QAE7B,OAAOtD;IACT;IAEAD,eAAeC,QAAkB,EAAY;QAC3C,OAAO/E,CAAAA,GAAAA,qCAAAA,EAAK+C,OAAO,CAAC,IAAI,CAAC4B,GAAG,IAAII;IAClC;IAEA,yCAAA;IACA,MAAM8B,SAAS9B,QAAkB,EAAE+B,QAAmB,EAAgB;QACpE,OAAO,IAAI,CAACC,YAAY,CAAChC,UAAU+B;IACrC;IAEA,MAAMhB,UACJf,QAAkB,EAClBgB,QAAyB,EACzBC,OAAqB,EACN;QACfjB,WAAW,MAAM,IAAI,CAAC2K,iBAAiB,CAAC3K;QACxC,MAAM,IAAI,CAACkK,QAAQ,CAACnJ,SAAS,CAACf,UAAUgB,UAAUC;QAClD,IAAI,CAAC8I,OAAO,CAAC7G,MAAM,CAAClD;IACtB;IAEA,MAAMmC,SAAS5G,MAAgB,EAAEE,WAAqB,EAAiB;QACrEF,SAAS,IAAI,CAACwE,cAAc,CAACxE;QAC7BE,cAAc,MAAM,IAAI,CAACkP,iBAAiB,CAAClP;QAE3C,IAAI,MAAM,IAAI,CAACyO,QAAQ,CAAC1G,MAAM,CAACjI,SAC7B,MAAM,IAAI,CAAC2O,QAAQ,CAACnJ,SAAS,CAC3BtF,aACA,MAAM,IAAI,CAACyO,QAAQ,CAACpI,QAAQ,CAACvG;aAG/B,MAAM,IAAI,CAAC2O,QAAQ,CAACnJ,SAAS,CAC3BtF,aACA,MAAM,IAAI,CAACwO,QAAQ,CAACnI,QAAQ,CAACvG;QAIjC,IAAI,CAACwO,OAAO,CAAC7G,MAAM,CAACzH;IACtB;IAEA,yCAAA;IACA,MAAMS,KAAK8D,QAAkB,EAAsB;QACjD,OAAO,IAAI,CAACoC,QAAQ,CAACpC;IACvB;IAEA,yCAAA;IACA,MAAMwC,MAAMxC,QAAkB,EAAsB;QAClD,OAAO,IAAI,CAACqC,SAAS,CAACrC;IACxB;IAEA,MAAMc,QAAQgD,MAAgB,EAAE9D,QAAkB,EAAiB;QACjE8D,SAAS,IAAI,CAAC/D,cAAc,CAAC+D;QAC7B9D,WAAW,IAAI,CAACD,cAAc,CAACC;QAC/B,MAAM,IAAI,CAACkK,QAAQ,CAACpJ,OAAO,CAACgD,QAAQ9D;QACpC,IAAI,CAAC+J,OAAO,CAAC7G,MAAM,CAAClD;IACtB;IAEA,MAAMiD,OAAOjD,QAAkB,EAAiB;QAC9CA,WAAW,IAAI,CAACD,cAAc,CAACC;QAE/B,IAAI4K,WAAW;YAAC5K;SAAS;QAEzB,IAAI,IAAI,CAACkK,QAAQ,YAAYzM,CAAAA,GAAAA,yCAAAA,KAAY,IAAI,CAAC4M,UAAU,CAACrK,WACvD,IAAI,CAACkK,QAAQ,CAAC9L,QAAQ,CAAC8E,MAAM,CAAClD;aACzB,IAAI,IAAI,CAACoC,QAAQ,CAACpC,UAAU1D,WAAW,IAAI;YAChD,IAAIuO,QAAQ;gBAAC7K;aAAS;YAEtB,oDAAA;YACA,MAAO6K,MAAMnL,MAAM,CAAE;gBACnB,IAAIU,OAAOnD,CAAAA,GAAAA,gEAAAA,EAAW4N,MAAMN,GAAG;gBAC/B,KAAK,IAAIO,OAAO,IAAI,CAACrI,WAAW,CAACrC,MAAM;oBAACyC,eAAe;gBAAI,GACzD,IAAI,OAAOiI,QAAQ,UAAU;oBAC3B,IAAIC,YAAY9P,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACqE,MAAM0K;oBAChCF,SAAS9H,IAAI,CAACiI;oBACd,IAAI,IAAI,CAAC3I,QAAQ,CAAC2I,WAAWzO,WAAW,IACtCuO,MAAM/H,IAAI,CAACiI;gBAEf,OAAO;oBACL,IAAIA,YAAY9P,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACqE,MAAM0K,IAAIlI,IAAI;oBACxCgI,SAAS9H,IAAI,CAACiI;oBACd,IAAID,IAAIxO,WAAW,IACjBuO,MAAM/H,IAAI,CAACiI;gBAEf;YAEJ;QACF;QAEA,IAAI;YACF,MAAM,IAAI,CAACb,QAAQ,CAACjH,MAAM,CAACjD;QAC7B,EAAE,OAAO0K,GAAG;YACV,IAAIA,EAAE5E,IAAI,KAAK,YAAY,CAAC,IAAI,CAACmE,QAAQ,CAAClG,UAAU,CAAC/D,WACnD,MAAM0K;QAEV;QAEA,KAAK,IAAIM,gBAAgBJ,SACvB,IAAI,CAACb,OAAO,CAAChF,GAAG,CAACiG;IAErB;IAEA,MAAMtP,OAAOoE,GAAa,EAAiB;QACzCA,MAAM,IAAI,CAACC,cAAc,CAACD;QAC1B,MAAM,IAAI,CAACoK,QAAQ,CAACxO,MAAM,CAACoE;QAE3B,IAAI,IAAI,CAACiK,OAAO,IAAI,MAAM;YACxB,IAAI3J,OAAOnF,CAAAA,GAAAA,qCAAAA,EAAKqF,KAAK,CAACR,KAAKM,IAAI;YAC/B,MAAON,QAAQM,KAAM;gBACnB,IAAI,CAAC2J,OAAO,CAAC7G,MAAM,CAACpD;gBACpBA,MAAM7E,CAAAA,GAAAA,qCAAAA,EAAKmG,OAAO,CAACtB;YACrB;QACF;IACF;IAEA,MAAMsD,OAAOpD,QAAkB,EAAiB;QAC9C,IAAI;YACF,MAAM,IAAI,CAACiD,MAAM,CAACjD;QACpB,EAAE,OAAO0K,GAAG;QACV,OAAA;QAAA;IAEJ;IAEA,yCAAA;IACA,MAAMrP,IAAIE,MAAgB,EAAEE,WAAqB,EAAiB;QAChE,kCAAA;QACA,OAAO,IAAI,CAACyO,QAAQ,CAAC7O,GAAG,CAACE,QAAQE;IACnC;IAEAW,iBAAiB4D,QAAkB,EAAEpB,IAAmB,EAAY;QAClEoB,WAAW,IAAI,CAACmK,cAAc,CAACnK;QAC/B,IAAI,IAAI,CAACkK,QAAQ,CAACnG,UAAU,CAAC/D,WAC3B,OAAO,IAAI,CAACkK,QAAQ,CAAC9N,gBAAgB,CAAC4D,UAAUpB;QAGlD,OAAO,IAAI,CAACqL,QAAQ,CAAC7N,gBAAgB,CAAC4D,UAAUpB;IAClD;IAEAvC,kBAAkBpB,IAAc,EAAE2D,IAAmB,EAAY;QAC/D3D,OAAO,IAAI,CAAC8E,cAAc,CAAC9E;QAC3B,IAAI,CAAC8O,OAAO,CAAC7G,MAAM,CAACjI;QACpB,OAAO,IAAI,CAACiP,QAAQ,CAAC7N,iBAAiB,CAACpB,MAAM2D;IAC/C;IAEAgB,MAAgB;QACd,OAAO,IAAI,CAAC7B,IAAI;IAClB;IAEA8B,MAAM5E,IAAc,EAAQ;QAC1B,IAAI,CAAC8C,IAAI,GAAG,IAAI,CAACqM,YAAY,CAACnP;IAChC;IAEA,yCAAA;IACA,MAAMgF,SAASD,QAAkB,EAAqB;QACpD,OAAO,IAAI,CAAC2D,YAAY,CAAC3D;IAC3B;IAEAgC,aAAahC,QAAkB,EAAE+B,QAAmB,EAAO;QACzD/B,WAAW,IAAI,CAAC2D,YAAY,CAAC3D;QAC7B,IAAI;YACF,gCAAA;YACA,OAAO,IAAI,CAACkK,QAAQ,CAAClI,YAAY,CAAChC,UAAU+B;QAC9C,EAAE,OAAOuE,KAAK;YACZ,gCAAA;YACA,OAAO,IAAI,CAAC2D,QAAQ,CAACjI,YAAY,CAAChC,UAAU+B;QAC9C;IACF;IAEAK,SAASpC,QAAkB,EAAa;QACtCA,WAAW,IAAI,CAACD,cAAc,CAACC;QAC/B,IAAI;YACF,OAAO,IAAI,CAACkK,QAAQ,CAAC9H,QAAQ,CAACpC;QAChC,EAAE,OAAO0K,GAAG;YACV,IAAIA,EAAE5E,IAAI,KAAK,YAAY,IAAI,CAAC/B,UAAU,CAAC/D,WACzC,OAAO,IAAI,CAACiK,QAAQ,CAAC7H,QAAQ,CAACpC;YAEhC,MAAM0K;QACR;IACF;IAEArI,UAAUrC,QAAkB,EAAa;QACvCA,WAAW,IAAI,CAACD,cAAc,CAACC;QAC/B,IAAI;YACF,OAAO,IAAI,CAACkK,QAAQ,CAAC7H,SAAS,CAACrC;QACjC,EAAE,OAAO0K,GAAG;YACV,IAAIA,EAAE5E,IAAI,KAAK,UACb,OAAO,IAAI,CAACmE,QAAQ,CAAC5H,SAAS,CAACrC;YAEjC,MAAM0K;QACR;IACF;IAEA/G,aAAa3D,QAAkB,EAAY;QACzCA,WAAW,IAAI,CAACmK,cAAc,CAACnK;QAC/BA,WAAW,IAAI,CAACmK,cAAc,CAAC,IAAI,CAACD,QAAQ,CAACvG,YAAY,CAAC3D;QAC1D,IAAI,CAAC,IAAI,CAACkK,QAAQ,CAACnG,UAAU,CAAC/D,WAC5B,OAAO,IAAI,CAACiK,QAAQ,CAACtG,YAAY,CAAC3D;QAEpC,OAAOA;IACT;IAEA4D,aAAa5D,QAAkB,EAAY;QACzCA,WAAW,IAAI,CAACmK,cAAc,CAACnK;QAC/B,IAAI;YACF,OAAO,IAAI,CAACkK,QAAQ,CAACtG,YAAY,CAAC5D;QACpC,EAAE,OAAOsG,KAAK;YACZ,OAAO,IAAI,CAAC2D,QAAQ,CAACrG,YAAY,CAAC5D;QACpC;IACF;IAEA,yCAAA;IACA,MAAM6D,SAAS7D,QAAkB,EAAqB;QACpD,OAAO,IAAI,CAAC4D,YAAY,CAAC5D;IAC3B;IAEA,yCAAA;IACA,MAAMwD,OAAOxD,QAAkB,EAAoB;QACjD,OAAO,IAAI,CAAC+D,UAAU,CAAC/D;IACzB;IAEA+D,WAAW/D,QAAkB,EAAW;QACtCA,WAAW,IAAI,CAACD,cAAc,CAACC;QAC/B,IAAI,IAAI,CAAC+J,OAAO,CAAC7I,GAAG,CAAClB,WAAW,OAAO;QAEvC,IAAI;YACFA,WAAW,IAAI,CAAC2D,YAAY,CAAC3D;QAC/B,EAAE,OAAOsG,KAAK;YACZ,IAAIA,IAAIR,IAAI,KAAK,UAAU,MAAMQ;QACnC;QAEA,IAAI,IAAI,CAACyD,OAAO,CAAC7I,GAAG,CAAClB,WAAW,OAAO;QAEvC,OACE,IAAI,CAACkK,QAAQ,CAACnG,UAAU,CAAC/D,aAAa,IAAI,CAACiK,QAAQ,CAAClG,UAAU,CAAC/D;IAEnE;IAEA,yCAAA;IACA,MAAMpE,QAAQX,IAAc,EAAE2D,IAAqB,EAAgB;QACjE,OAAO,IAAI,CAAC6D,WAAW,CAACxH,MAAM2D;IAChC;IAEA6D,YAAY3C,GAAa,EAAElB,IAAqB,EAAO;QACrDkB,MAAM,IAAI,CAAC6D,YAAY,CAAC7D;QACxB,mDAAA;QACA,IAAImL,UAAU,IAAI1N;QAElB,IAAI;YACF,KAAK,IAAIqE,SAAc,IAAI,CAACsI,QAAQ,CAACzH,WAAW,CAAC3C,KAAKlB,MAAO;gBAC3D,IAAIoB,WAAW/E,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAAC+D,KAAK8B,MAAMgB,IAAI,IAAIhB;gBAC5C,IAAI,IAAI,CAACmI,OAAO,CAAC7I,GAAG,CAAClB,WAAW;gBAChCiL,QAAQxM,GAAG,CAACuB,UAAU4B;YACxB;QACF,EAAE,OAAM;QACN,OAAA;QAAA;QAGF,IAAI;YACF,KAAK,IAAIA,SAAc,IAAI,CAACqI,QAAQ,CAACxH,WAAW,CAAC3C,KAAKlB,MAAO;gBAC3D,IAAIoB,WAAW/E,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAAC+D,KAAK8B,MAAMgB,IAAI,IAAIhB;gBAC5C,IAAI,IAAI,CAACmI,OAAO,CAAC7I,GAAG,CAAClB,WAAW;gBAChC,IAAIiL,QAAQ/J,GAAG,CAAClB,WAAW;gBAC3BiL,QAAQxM,GAAG,CAACuB,UAAU4B;YACxB;QACF,EAAE,OAAM;QACN,OAAA;QAAA;QAGF,OAAOsJ,MAAMlI,IAAI,CAACiI,QAAQE,MAAM;IAClC;IAEA,MAAMvG,MACJ9E,GAAa,EACbR,EAAgD,EAChDV,IAAoB,EACQ;QAC5B,IAAIwM,uBAAuB,MAAM,IAAI,CAAClB,QAAQ,CAACtF,KAAK,CAAC9E,KAAKR,IAAIV;QAC9D,IAAIyM,uBAAuB,MAAM,IAAI,CAACpB,QAAQ,CAACrF,KAAK,CAAC9E,KAAKR,IAAIV;QAC9D,OAAO;YACLoG,aAAa;gBACX,MAAMoG,qBAAqBpG,WAAW;gBACtC,MAAMqG,qBAAqBrG,WAAW;YACxC;QACF;IACF;IAEA,MAAMC,eACJnF,GAAa,EACboF,QAAkB,EAClBtG,IAAoB,EACG;QACvB,IAAI0M,iBAAiB,MAAM,IAAI,CAACpB,QAAQ,CAACjF,cAAc,CACrDnF,KACAoF,UACAtG;QAEF,IAAI2M,iBAAiB,MAAM,IAAI,CAACtB,QAAQ,CAAChF,cAAc,CACrDnF,KACAoF,UACAtG;QAEF,OAAO;eAAI0M;eAAmBC;SAAe;IAC/C;IAEA,MAAM9F,cACJ3F,GAAa,EACboF,QAAkB,EAClBtG,IAAoB,EACL;QACf,MAAM,IAAI,CAACsL,QAAQ,CAACzE,aAAa,CAAC3F,KAAKoF,UAAUtG;IACnD;IAEAzB,iBACEuI,SAAwB,EACxBC,OAAiB,EACjBvF,IAAc,EACH;QACX,OAAOjD,CAAAA,GAAAA,yCAAAA,EAAiB,IAAI,EAAEuI,WAAWC,SAASvF;IACpD;IAEAhD,eAAewI,UAAkB,EAAED,OAAiB,EAAa;QAC/D,OAAOvI,CAAAA,GAAAA,yCAAAA,EAAe,IAAI,EAAEwI,YAAYD;IAC1C;IAEAtI,cAAcwI,SAA0B,EAAa;QACnD,OAAOxI,CAAAA,GAAAA,yCAAAA,EAAc,IAAI,EAAEwI;IAC7B;AACF;AAEA,MAAM1E,sCAAgB1E;IAGpBD,YAAYsJ,IAAY,EAAE7K,IAAc,EAAE8K,OAAe,CAAE;QACzD,KAAK,CAAC,GAAGD,KAAI,EAAA,EAAK7K,KAAI,CAAA,EAAI8K,SAAS;QACnC,IAAI,CAACnD,IAAI,GAAG;QACZ,IAAI,CAACkD,IAAI,GAAGA;QACZ,IAAI,CAAC7K,IAAI,GAAGA;QACZwB,MAAMuJ,iBAAiB,GAAG,IAAI,EAAE,IAAI,CAACxJ,WAAW;IAClD;AACF;AAEAI,CAAAA,GAAAA,2CAAAA,EAA0B,GAAGE,CAAAA,GAAAA,gEAAAA,EAAYwM,OAAO,CAAA,UAAA,CAAY,EAAEQ;;;ANvc9D,MAAM1O,iCAAkDD,CAAAA,GAAAA,qBAAAA,EACtDD,CAAAA,GAAAA,uCAAAA,EAAOE,QACT;AAGO,eAAeC,0CACpBC,QAAoB,EACpBC,MAAgB,EAChBC,aAAyB,EACzBC,WAAqB;IAErB,MAAMD,cAAcE,MAAM,CAACD;IAC3B,IAAIE,QAAQ,MAAML,SAASM,OAAO,CAACL;IACnC,KAAK,IAAIM,QAAQF,MAAO;QACtB,IAAIG,aAAab,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACR,QAAQM;QACnC,IAAIG,WAAWf,CAAAA,GAAAA,qCAAAA,EAAKc,IAAI,CAACN,aAAaI;QACtC,IAAII,QAAQ,MAAMX,SAASY,IAAI,CAACJ;QAChC,IAAIG,MAAME,MAAM,IACd,MAAMf,+BACJE,SAASc,gBAAgB,CAACN,aAC1BN,cAAca,iBAAiB,CAACL;aAE7B,IAAIC,MAAMK,WAAW,IAC1B,MAAMjB,0CAAIC,UAAUQ,YAAYN,eAAeQ;IAEnD;AACF", "sources": ["packages/core/fs/src/index.js", "packages/core/fs/src/NodeFS.browser.js", "packages/core/fs/src/MemoryFS.js", "packages/core/fs/package.json", "node_modules/nullthrows/nullthrows.js", "packages/core/fs/src/find.js", "packages/core/fs/src/OverlayFS.js"], "sourcesContent": ["// @flow strict-local\nimport type {FilePath, FileSystem, FileOptions} from '@parcel/types-internal';\nimport type {Readable, Writable} from 'stream';\n\nimport path from 'path';\nimport stream from 'stream';\nimport {promisify} from 'util';\n\nexport * from './NodeFS';\nexport * from './MemoryFS';\nexport * from './OverlayFS';\n\nexport type {FileSystem, FileOptions};\n\nconst pipeline: (Readable, Writable) => Promise<void> = promisify(\n  stream.pipeline,\n);\n\n// Recursively copies a directory from the sourceFS to the destinationFS\nexport async function ncp(\n  sourceFS: FileSystem,\n  source: FilePath,\n  destinationFS: FileSystem,\n  destination: FilePath,\n) {\n  await destinationFS.mkdirp(destination);\n  let files = await sourceFS.readdir(source);\n  for (let file of files) {\n    let sourcePath = path.join(source, file);\n    let destPath = path.join(destination, file);\n    let stats = await sourceFS.stat(sourcePath);\n    if (stats.isFile()) {\n      await pipeline(\n        sourceFS.createReadStream(sourcePath),\n        destinationFS.createWriteStream(destPath),\n      );\n    } else if (stats.isDirectory()) {\n      await ncp(sourceFS, sourcePath, destinationFS, destPath);\n    }\n  }\n}\n", "// @flow\nimport type {FileSystem} from '@parcel/types-internal';\n\n// $FlowFixMe[prop-missing] handled by the throwing constructor\nexport class NodeFS implements FileSystem {\n  constructor() {\n    throw new Error(\"NodeFS isn't available in the browser\");\n  }\n}\n", "// @flow\n\nimport type {\n  FilePath,\n  FileSystem,\n  FileOptions,\n  ReaddirOptions,\n  Encoding,\n} from '@parcel/types-internal';\nimport type {\n  Event,\n  Options as WatcherOptions,\n  AsyncSubscription,\n} from '@parcel/watcher';\n\nimport path from 'path';\nimport {Readable, Writable} from 'stream';\nimport {registerSerializableClass} from '@parcel/core';\nimport {SharedBuffer} from '@parcel/utils';\nimport packageJSON from '../package.json';\nimport WorkerFarm, {Handle} from '@parcel/workers';\nimport nullthrows from 'nullthrows';\nimport EventEmitter from 'events';\nimport {findAncestorFile, findNodeModule, findFirstFile} from './find';\n\nconst instances: Map<number, MemoryFS> = new Map();\nlet id = 0;\n\ntype HandleFunction = (...args: Array<any>) => any;\ntype SerializedMemoryFS = {\n  id: number,\n  handle: any,\n  dirs: Map<FilePath, Directory>,\n  files: Map<FilePath, File>,\n  symlinks: Map<FilePath, FilePath>,\n  ...\n};\n\ntype WorkerEvent = {|\n  type: 'writeFile' | 'unlink' | 'mkdir' | 'symlink',\n  path: FilePath,\n  entry?: Entry,\n  target?: FilePath,\n|};\n\ntype ResolveFunction = () => mixed;\n\nexport class MemoryFS implements FileSystem {\n  dirs: Map<FilePath, Directory>;\n  files: Map<FilePath, File>;\n  symlinks: Map<FilePath, FilePath>;\n  watchers: Map<FilePath, Set<Watcher>>;\n  events: Array<Event>;\n  id: number;\n  handle: Handle;\n  farm: WorkerFarm;\n  _cwd: FilePath;\n  _eventQueue: Array<Event>;\n  _watcherTimer: TimeoutID;\n  _numWorkerInstances: number = 0;\n  _workerHandles: Array<Handle>;\n  _workerRegisterResolves: Array<ResolveFunction> = [];\n  _emitter: EventEmitter = new EventEmitter();\n\n  constructor(workerFarm: WorkerFarm) {\n    this.farm = workerFarm;\n    this._cwd = path.resolve(path.sep);\n    this.dirs = new Map([[this._cwd, new Directory()]]);\n    this.files = new Map();\n    this.symlinks = new Map();\n    this.watchers = new Map();\n    this.events = [];\n    this.id = id++;\n    this._workerHandles = [];\n    this._eventQueue = [];\n    instances.set(this.id, this);\n    this._emitter.on('allWorkersRegistered', () => {\n      for (let resolve of this._workerRegisterResolves) {\n        resolve();\n      }\n      this._workerRegisterResolves = [];\n    });\n  }\n\n  static deserialize(opts: SerializedMemoryFS): MemoryFS | WorkerFS {\n    let existing = instances.get(opts.id);\n    if (existing != null) {\n      // Correct the count of worker instances since serialization assumes a new instance is created\n      WorkerFarm.getWorkerApi().runHandle(opts.handle, [\n        'decrementWorkerInstance',\n        [],\n      ]);\n      return existing;\n    }\n\n    let fs = new WorkerFS(opts.id, nullthrows(opts.handle));\n    fs.dirs = opts.dirs;\n    fs.files = opts.files;\n    fs.symlinks = opts.symlinks;\n    return fs;\n  }\n\n  serialize(): SerializedMemoryFS {\n    if (!this.handle) {\n      this.handle = this.farm.createReverseHandle(\n        (fn: string, args: Array<mixed>) => {\n          // $FlowFixMe\n          return this[fn](...args);\n        },\n      );\n    }\n\n    // If a worker instance already exists, it will decrement this number\n    this._numWorkerInstances++;\n\n    return {\n      $$raw: false,\n      id: this.id,\n      handle: this.handle,\n      dirs: this.dirs,\n      files: this.files,\n      symlinks: this.symlinks,\n    };\n  }\n\n  decrementWorkerInstance() {\n    this._numWorkerInstances--;\n    if (this._numWorkerInstances === this._workerHandles.length) {\n      this._emitter.emit('allWorkersRegistered');\n    }\n  }\n\n  cwd(): FilePath {\n    return this._cwd;\n  }\n\n  chdir(dir: FilePath) {\n    this._cwd = dir;\n  }\n\n  _normalizePath(filePath: FilePath, realpath: boolean = true): FilePath {\n    filePath = path.normalize(filePath);\n    if (!filePath.startsWith(this.cwd())) {\n      filePath = path.resolve(this.cwd(), filePath);\n    }\n\n    // get realpath by following symlinks\n    let {root, dir, base} = path.parse(filePath);\n    let parts = dir.slice(root.length).split(path.sep).concat(base);\n\n    // If the realpath option is not true, don't follow the final link\n    let last;\n    if (!realpath) {\n      last = parts[parts.length - 1];\n      parts = parts.slice(0, -1);\n    }\n\n    let res = root;\n    for (let part of parts) {\n      res = path.join(res, part);\n      let symlink = this.symlinks.get(res);\n      if (symlink) {\n        res = symlink;\n      }\n    }\n\n    if (last) {\n      res = path.join(res, last);\n    }\n\n    return res;\n  }\n\n  async writeFile(\n    filePath: FilePath,\n    contents: Buffer | string,\n    options?: ?FileOptions,\n  ) {\n    filePath = this._normalizePath(filePath);\n    if (this.dirs.has(filePath)) {\n      throw new FSError('EISDIR', filePath, 'is a directory');\n    }\n\n    let dir = path.dirname(filePath);\n    if (!this.dirs.has(dir)) {\n      throw new FSError('ENOENT', dir, 'does not exist');\n    }\n\n    let buffer = makeShared(contents);\n    let file = this.files.get(filePath);\n    let mode = (options && options.mode) || 0o666;\n    if (file) {\n      file.write(buffer, mode);\n      this.files.set(filePath, file);\n    } else {\n      this.files.set(filePath, new File(buffer, mode));\n    }\n\n    await this._sendWorkerEvent({\n      type: 'writeFile',\n      path: filePath,\n      entry: this.files.get(filePath),\n    });\n\n    this._triggerEvent({\n      type: file ? 'update' : 'create',\n      path: filePath,\n    });\n  }\n\n  // eslint-disable-next-line require-await\n  async readFile(filePath: FilePath, encoding?: Encoding): Promise<any> {\n    return this.readFileSync(filePath, encoding);\n  }\n\n  readFileSync(filePath: FilePath, encoding?: Encoding): any {\n    filePath = this._normalizePath(filePath);\n    let file = this.files.get(filePath);\n    if (file == null) {\n      throw new FSError('ENOENT', filePath, 'does not exist');\n    }\n\n    let buffer = file.read();\n    if (encoding) {\n      return buffer.toString(encoding);\n    }\n\n    return buffer;\n  }\n\n  async copyFile(source: FilePath, destination: FilePath) {\n    let contents = await this.readFile(source);\n    await this.writeFile(destination, contents);\n  }\n\n  statSync(filePath: FilePath): Stat {\n    filePath = this._normalizePath(filePath);\n\n    let dir = this.dirs.get(filePath);\n    if (dir) {\n      return dir.stat();\n    }\n\n    let file = this.files.get(filePath);\n    if (file == null) {\n      throw new FSError('ENOENT', filePath, 'does not exist');\n    }\n\n    return file.stat();\n  }\n\n  // eslint-disable-next-line require-await\n  async stat(filePath: FilePath): Promise<Stat> {\n    return this.statSync(filePath);\n  }\n\n  lstatSync(filePath: FilePath): Stat {\n    filePath = this._normalizePath(filePath, false);\n\n    if (this.symlinks.has(filePath)) {\n      let stat = new Stat();\n      stat.mode = S_IFLNK;\n      return stat;\n    }\n\n    let dir = this.dirs.get(filePath);\n    if (dir) {\n      return dir.stat();\n    }\n\n    let file = this.files.get(filePath);\n    if (file == null) {\n      throw new FSError('ENOENT', filePath, 'does not exist');\n    }\n\n    return file.stat();\n  }\n\n  // eslint-disable-next-line require-await\n  async lstat(filePath: FilePath): Promise<Stat> {\n    return this.lstatSync(filePath);\n  }\n\n  readdirSync(dir: FilePath, opts?: ReaddirOptions): any {\n    dir = this._normalizePath(dir);\n    if (!this.dirs.has(dir)) {\n      throw new FSError('ENOENT', dir, 'does not exist');\n    }\n\n    if (!dir.endsWith(path.sep)) {\n      dir += path.sep;\n    }\n\n    let res = [];\n    for (let [filePath, entry] of this.dirs) {\n      if (filePath === dir) {\n        continue;\n      }\n      if (\n        filePath.startsWith(dir) &&\n        filePath.indexOf(path.sep, dir.length) === -1\n      ) {\n        let name = filePath.slice(dir.length);\n        if (opts?.withFileTypes) {\n          res.push(new Dirent(name, entry));\n        } else {\n          res.push(name);\n        }\n      }\n    }\n\n    for (let [filePath, entry] of this.files) {\n      if (\n        filePath.startsWith(dir) &&\n        filePath.indexOf(path.sep, dir.length) === -1\n      ) {\n        let name = filePath.slice(dir.length);\n        if (opts?.withFileTypes) {\n          res.push(new Dirent(name, entry));\n        } else {\n          res.push(name);\n        }\n      }\n    }\n\n    for (let [from] of this.symlinks) {\n      if (from.startsWith(dir) && from.indexOf(path.sep, dir.length) === -1) {\n        let name = from.slice(dir.length);\n        if (opts?.withFileTypes) {\n          res.push(new Dirent(name, {mode: S_IFLNK}));\n        } else {\n          res.push(name);\n        }\n      }\n    }\n\n    return res;\n  }\n\n  // eslint-disable-next-line require-await\n  async readdir(dir: FilePath, opts?: ReaddirOptions): Promise<any> {\n    return this.readdirSync(dir, opts);\n  }\n\n  async unlink(filePath: FilePath): Promise<void> {\n    filePath = this._normalizePath(filePath);\n    if (!this.files.has(filePath) && !this.dirs.has(filePath)) {\n      throw new FSError('ENOENT', filePath, 'does not exist');\n    }\n\n    this.files.delete(filePath);\n    this.dirs.delete(filePath);\n    this.watchers.delete(filePath);\n\n    await this._sendWorkerEvent({\n      type: 'unlink',\n      path: filePath,\n    });\n\n    this._triggerEvent({\n      type: 'delete',\n      path: filePath,\n    });\n\n    return Promise.resolve();\n  }\n\n  async mkdirp(dir: FilePath): Promise<void> {\n    dir = this._normalizePath(dir);\n    if (this.dirs.has(dir)) {\n      return Promise.resolve();\n    }\n\n    if (this.files.has(dir)) {\n      throw new FSError('ENOENT', dir, 'is not a directory');\n    }\n\n    let root = path.parse(dir).root;\n    while (dir !== root) {\n      if (this.dirs.has(dir)) {\n        break;\n      }\n\n      this.dirs.set(dir, new Directory());\n      await this._sendWorkerEvent({\n        type: 'mkdir',\n        path: dir,\n      });\n\n      this._triggerEvent({\n        type: 'create',\n        path: dir,\n      });\n\n      dir = path.dirname(dir);\n    }\n\n    return Promise.resolve();\n  }\n\n  async rimraf(filePath: FilePath): Promise<void> {\n    filePath = this._normalizePath(filePath);\n\n    if (this.dirs.has(filePath)) {\n      let dir = filePath + path.sep;\n      for (let filePath of this.files.keys()) {\n        if (filePath.startsWith(dir)) {\n          this.files.delete(filePath);\n          await this._sendWorkerEvent({\n            type: 'unlink',\n            path: filePath,\n          });\n\n          this._triggerEvent({\n            type: 'delete',\n            path: filePath,\n          });\n        }\n      }\n\n      for (let dirPath of this.dirs.keys()) {\n        if (dirPath.startsWith(dir)) {\n          this.dirs.delete(dirPath);\n          this.watchers.delete(dirPath);\n          await this._sendWorkerEvent({\n            type: 'unlink',\n            path: filePath,\n          });\n\n          this._triggerEvent({\n            type: 'delete',\n            path: dirPath,\n          });\n        }\n      }\n\n      for (let filePath of this.symlinks.keys()) {\n        if (filePath.startsWith(dir)) {\n          this.symlinks.delete(filePath);\n          await this._sendWorkerEvent({\n            type: 'unlink',\n            path: filePath,\n          });\n        }\n      }\n\n      this.dirs.delete(filePath);\n      await this._sendWorkerEvent({\n        type: 'unlink',\n        path: filePath,\n      });\n\n      this._triggerEvent({\n        type: 'delete',\n        path: filePath,\n      });\n    } else if (this.files.has(filePath)) {\n      this.files.delete(filePath);\n      await this._sendWorkerEvent({\n        type: 'unlink',\n        path: filePath,\n      });\n\n      this._triggerEvent({\n        type: 'delete',\n        path: filePath,\n      });\n    }\n\n    return Promise.resolve();\n  }\n\n  async ncp(source: FilePath, destination: FilePath) {\n    source = this._normalizePath(source);\n\n    if (this.dirs.has(source)) {\n      if (!this.dirs.has(destination)) {\n        this.dirs.set(destination, new Directory());\n        await this._sendWorkerEvent({\n          type: 'mkdir',\n          path: destination,\n        });\n\n        this._triggerEvent({\n          type: 'create',\n          path: destination,\n        });\n      }\n\n      let dir = source + path.sep;\n      for (let dirPath of this.dirs.keys()) {\n        if (dirPath.startsWith(dir)) {\n          let destName = path.join(destination, dirPath.slice(dir.length));\n          if (!this.dirs.has(destName)) {\n            this.dirs.set(destName, new Directory());\n            await this._sendWorkerEvent({\n              type: 'mkdir',\n              path: destination,\n            });\n            this._triggerEvent({\n              type: 'create',\n              path: destName,\n            });\n          }\n        }\n      }\n\n      for (let [filePath, file] of this.files) {\n        if (filePath.startsWith(dir)) {\n          let destName = path.join(destination, filePath.slice(dir.length));\n          let exists = this.files.has(destName);\n          this.files.set(destName, file);\n          await this._sendWorkerEvent({\n            type: 'writeFile',\n            path: destName,\n            entry: file,\n          });\n\n          this._triggerEvent({\n            type: exists ? 'update' : 'create',\n            path: destName,\n          });\n        }\n      }\n    } else {\n      await this.copyFile(source, destination);\n    }\n  }\n\n  createReadStream(filePath: FilePath): ReadStream {\n    return new ReadStream(this, filePath);\n  }\n\n  createWriteStream(filePath: FilePath, options: ?FileOptions): WriteStream {\n    return new WriteStream(this, filePath, options);\n  }\n\n  realpathSync(filePath: FilePath): FilePath {\n    return this._normalizePath(filePath);\n  }\n\n  // eslint-disable-next-line require-await\n  async realpath(filePath: FilePath): Promise<FilePath> {\n    return this.realpathSync(filePath);\n  }\n\n  readlinkSync(filePath: FilePath): FilePath {\n    let symlink = this.symlinks.get(filePath);\n    if (!symlink) {\n      throw new FSError('EINVAL', filePath, 'is not a symlink');\n    }\n    return symlink;\n  }\n\n  // eslint-disable-next-line require-await\n  async readlink(filePath: FilePath): Promise<FilePath> {\n    return this.readlinkSync(filePath);\n  }\n\n  async symlink(target: FilePath, path: FilePath) {\n    target = this._normalizePath(target);\n    path = this._normalizePath(path);\n    this.symlinks.set(path, target);\n    await this._sendWorkerEvent({\n      type: 'symlink',\n      path,\n      target,\n    });\n  }\n\n  existsSync(filePath: FilePath): boolean {\n    filePath = this._normalizePath(filePath);\n    return this.files.has(filePath) || this.dirs.has(filePath);\n  }\n\n  // eslint-disable-next-line require-await\n  async exists(filePath: FilePath): Promise<boolean> {\n    return this.existsSync(filePath);\n  }\n\n  _triggerEvent(event: Event) {\n    this.events.push(event);\n    if (this.watchers.size === 0) {\n      return;\n    }\n\n    // Batch events\n    this._eventQueue.push(event);\n    clearTimeout(this._watcherTimer);\n\n    this._watcherTimer = setTimeout(() => {\n      let events = this._eventQueue;\n      this._eventQueue = [];\n\n      for (let [dir, watchers] of this.watchers) {\n        if (!dir.endsWith(path.sep)) {\n          dir += path.sep;\n        }\n\n        if (event.path.startsWith(dir)) {\n          for (let watcher of watchers) {\n            watcher.trigger(events);\n          }\n        }\n      }\n    }, 50);\n  }\n\n  _registerWorker(handle: Handle) {\n    this._workerHandles.push(handle);\n    if (this._numWorkerInstances === this._workerHandles.length) {\n      this._emitter.emit('allWorkersRegistered');\n    }\n  }\n\n  async _sendWorkerEvent(event: WorkerEvent) {\n    // Wait for worker instances to register their handles\n    while (this._workerHandles.length < this._numWorkerInstances) {\n      await new Promise(resolve => this._workerRegisterResolves.push(resolve));\n    }\n\n    await Promise.all(\n      this._workerHandles.map(workerHandle =>\n        this.farm.workerApi.runHandle(workerHandle, [event]),\n      ),\n    );\n  }\n\n  watch(\n    dir: FilePath,\n    fn: (err: ?Error, events: Array<Event>) => mixed,\n    opts: WatcherOptions,\n  ): Promise<AsyncSubscription> {\n    dir = this._normalizePath(dir);\n    let watcher = new Watcher(fn, opts);\n    let watchers = this.watchers.get(dir);\n    if (!watchers) {\n      watchers = new Set();\n      this.watchers.set(dir, watchers);\n    }\n\n    watchers.add(watcher);\n\n    return Promise.resolve({\n      unsubscribe: () => {\n        watchers = nullthrows(watchers);\n        watchers.delete(watcher);\n\n        if (watchers.size === 0) {\n          this.watchers.delete(dir);\n        }\n\n        return Promise.resolve();\n      },\n    });\n  }\n\n  async getEventsSince(\n    dir: FilePath,\n    snapshot: FilePath,\n    opts: WatcherOptions,\n  ): Promise<Array<Event>> {\n    let contents = await this.readFile(snapshot, 'utf8');\n    let len = Number(contents);\n    let events = this.events.slice(len);\n    let ignore = opts.ignore;\n    if (ignore) {\n      events = events.filter(\n        event => !ignore.some(i => event.path.startsWith(i + path.sep)),\n      );\n    }\n\n    return events;\n  }\n\n  async writeSnapshot(dir: FilePath, snapshot: FilePath): Promise<void> {\n    await this.writeFile(snapshot, '' + this.events.length);\n  }\n\n  findAncestorFile(\n    fileNames: Array<string>,\n    fromDir: FilePath,\n    root: FilePath,\n  ): ?FilePath {\n    return findAncestorFile(this, fileNames, fromDir, root);\n  }\n\n  findNodeModule(moduleName: string, fromDir: FilePath): ?FilePath {\n    return findNodeModule(this, moduleName, fromDir);\n  }\n\n  findFirstFile(filePaths: Array<FilePath>): ?FilePath {\n    return findFirstFile(this, filePaths);\n  }\n}\n\nclass Watcher {\n  fn: (err: ?Error, events: Array<Event>) => mixed;\n  options: WatcherOptions;\n\n  constructor(\n    fn: (err: ?Error, events: Array<Event>) => mixed,\n    options: WatcherOptions,\n  ) {\n    this.fn = fn;\n    this.options = options;\n  }\n\n  trigger(events: Array<Event>) {\n    let ignore = this.options.ignore;\n    if (ignore) {\n      events = events.filter(\n        event => !ignore.some(i => event.path.startsWith(i + path.sep)),\n      );\n    }\n\n    if (events.length > 0) {\n      this.fn(null, events);\n    }\n  }\n}\n\nexport class FSError extends Error {\n  code: string;\n  path: FilePath;\n  constructor(code: string, path: FilePath, message: string) {\n    super(`${code}: ${path} ${message}`);\n    this.name = 'FSError';\n    this.code = code;\n    this.path = path;\n    Error.captureStackTrace?.(this, this.constructor);\n  }\n}\n\nclass ReadStream extends Readable {\n  fs: FileSystem;\n  filePath: FilePath;\n  reading: boolean;\n  bytesRead: number;\n  constructor(fs: FileSystem, filePath: FilePath) {\n    super();\n    this.fs = fs;\n    this.filePath = filePath;\n    this.reading = false;\n    this.bytesRead = 0;\n  }\n\n  _read() {\n    if (this.reading) {\n      return;\n    }\n\n    this.reading = true;\n    this.fs.readFile(this.filePath).then(\n      res => {\n        this.bytesRead += res.byteLength;\n        this.push(res);\n        this.push(null);\n      },\n      err => {\n        this.emit('error', err);\n      },\n    );\n  }\n}\n\nclass WriteStream extends Writable {\n  fs: FileSystem;\n  filePath: FilePath;\n  options: ?FileOptions;\n  buffer: Buffer;\n\n  constructor(fs: FileSystem, filePath: FilePath, options: ?FileOptions) {\n    super({emitClose: true, autoDestroy: true});\n    this.fs = fs;\n    this.filePath = filePath;\n    this.options = options;\n    this.buffer = Buffer.alloc(0);\n  }\n\n  _write(\n    chunk: Buffer | string,\n    encoding: any,\n    callback: (error?: Error) => void,\n  ) {\n    let c = typeof chunk === 'string' ? Buffer.from(chunk, encoding) : chunk;\n    this.buffer = Buffer.concat([this.buffer, c]);\n    callback();\n  }\n\n  _final(callback: (error?: Error) => void) {\n    this.fs\n      .writeFile(this.filePath, this.buffer, this.options)\n      .then(callback)\n      .catch(callback);\n  }\n}\n\nconst S_IFREG = 0o100000;\nconst S_IFDIR = 0o040000;\nconst S_IFLNK = 0o120000;\nconst S_IFMT = 0o170000;\n\nclass Entry {\n  mode: number;\n  atime: number;\n  mtime: number;\n  ctime: number;\n  birthtime: number;\n  constructor(mode: number) {\n    this.mode = mode;\n    let now = Date.now();\n    this.atime = now;\n    this.mtime = now;\n    this.ctime = now;\n    this.birthtime = now;\n  }\n\n  access() {\n    let now = Date.now();\n    this.atime = now;\n    this.ctime = now;\n  }\n\n  modify(mode: number) {\n    let now = Date.now();\n    this.mtime = now;\n    this.ctime = now;\n    this.mode = mode;\n  }\n\n  getSize(): number {\n    return 0;\n  }\n\n  stat(): Stat {\n    return Stat.fromEntry(this);\n  }\n}\n\nclass Stat {\n  dev: number = 0;\n  ino: number = 0;\n  mode: number = 0;\n  nlink: number = 0;\n  uid: number = 0;\n  gid: number = 0;\n  rdev: number = 0;\n  size: number = 0;\n  blksize: number = 0;\n  blocks: number = 0;\n  atimeMs: number = 0;\n  mtimeMs: number = 0;\n  ctimeMs: number = 0;\n  birthtimeMs: number = 0;\n  atime: Date = new Date();\n  mtime: Date = new Date();\n  ctime: Date = new Date();\n  birthtime: Date = new Date();\n\n  static fromEntry(entry: Entry): Stat {\n    let stat = new Stat();\n    stat.mode = entry.mode;\n    stat.size = entry.getSize();\n    stat.atimeMs = entry.atime;\n    stat.mtimeMs = entry.mtime;\n    stat.ctimeMs = entry.ctime;\n    stat.birthtimeMs = entry.birthtime;\n    stat.atime = new Date(entry.atime);\n    stat.mtime = new Date(entry.mtime);\n    stat.ctime = new Date(entry.ctime);\n    stat.birthtime = new Date(entry.birthtime);\n    return stat;\n  }\n\n  isFile(): boolean {\n    return (this.mode & S_IFREG) === S_IFREG;\n  }\n\n  isDirectory(): boolean {\n    return (this.mode & S_IFDIR) === S_IFDIR;\n  }\n\n  isBlockDevice(): boolean {\n    return false;\n  }\n\n  isCharacterDevice(): boolean {\n    return false;\n  }\n\n  isSymbolicLink(): boolean {\n    return (this.mode & S_IFMT) === S_IFLNK;\n  }\n\n  isFIFO(): boolean {\n    return false;\n  }\n\n  isSocket(): boolean {\n    return false;\n  }\n}\n\nclass Dirent {\n  name: string;\n  #mode: number;\n\n  constructor(name: string, entry: interface {mode: number}) {\n    this.name = name;\n    this.#mode = entry.mode;\n  }\n\n  isFile(): boolean {\n    return (this.#mode & S_IFMT) === S_IFREG;\n  }\n\n  isDirectory(): boolean {\n    return (this.#mode & S_IFMT) === S_IFDIR;\n  }\n\n  isBlockDevice(): boolean {\n    return false;\n  }\n\n  isCharacterDevice(): boolean {\n    return false;\n  }\n\n  isSymbolicLink(): boolean {\n    return (this.#mode & S_IFMT) === S_IFLNK;\n  }\n\n  isFIFO(): boolean {\n    return false;\n  }\n\n  isSocket(): boolean {\n    return false;\n  }\n}\n\nexport class File extends Entry {\n  buffer: Buffer;\n  constructor(buffer: Buffer, mode: number) {\n    super(S_IFREG | mode);\n    this.buffer = buffer;\n  }\n\n  read(): Buffer {\n    super.access();\n    return Buffer.from(this.buffer);\n  }\n\n  write(buffer: Buffer, mode: number) {\n    super.modify(S_IFREG | mode);\n    this.buffer = buffer;\n  }\n\n  getSize(): number {\n    return this.buffer.byteLength;\n  }\n}\n\nclass Directory extends Entry {\n  constructor() {\n    super(S_IFDIR);\n  }\n}\n\nexport function makeShared(contents: Buffer | string): Buffer {\n  if (typeof contents !== 'string' && contents.buffer instanceof SharedBuffer) {\n    return contents;\n  }\n\n  let contentsBuffer: Buffer | string = contents;\n  // $FlowFixMe\n  if (process.browser) {\n    // For the polyfilled buffer module, it's faster to always convert once so that the subsequent\n    // operations are fast (.byteLength and using .set instead of .write)\n    contentsBuffer =\n      contentsBuffer instanceof Buffer\n        ? contentsBuffer\n        : Buffer.from(contentsBuffer);\n  }\n\n  let length = Buffer.byteLength(contentsBuffer);\n  let shared = new SharedBuffer(length);\n  let buffer = Buffer.from(shared);\n  if (length > 0) {\n    if (typeof contentsBuffer === 'string') {\n      buffer.write(contentsBuffer);\n    } else {\n      buffer.set(contentsBuffer);\n    }\n  }\n\n  return buffer;\n}\n\nclass WorkerFS extends MemoryFS {\n  id: number;\n  handleFn: HandleFunction;\n\n  constructor(id: number, handle: Handle) {\n    // TODO Make this not a subclass\n    // $FlowFixMe\n    super();\n    this.id = id;\n    this.handleFn = (methodName, args) =>\n      WorkerFarm.getWorkerApi().runHandle(handle, [methodName, args]);\n\n    this.handleFn('_registerWorker', [\n      WorkerFarm.getWorkerApi().createReverseHandle(event => {\n        switch (event.type) {\n          case 'writeFile':\n            this.files.set(event.path, event.entry);\n            break;\n          case 'unlink':\n            this.files.delete(event.path);\n            this.dirs.delete(event.path);\n            this.symlinks.delete(event.path);\n            break;\n          case 'mkdir':\n            this.dirs.set(event.path, new Directory());\n            break;\n          case 'symlink':\n            this.symlinks.set(event.path, event.target);\n            break;\n        }\n      }),\n    ]);\n  }\n\n  static deserialize(opts: SerializedMemoryFS): MemoryFS {\n    return nullthrows(instances.get(opts.id));\n  }\n\n  serialize(): SerializedMemoryFS {\n    // $FlowFixMe\n    return {\n      id: this.id,\n    };\n  }\n\n  writeFile(\n    filePath: FilePath,\n    contents: Buffer | string,\n    options: ?FileOptions,\n  ): Promise<void> {\n    super.writeFile(filePath, contents, options);\n    let buffer = makeShared(contents);\n    return this.handleFn('writeFile', [filePath, buffer, options]);\n  }\n\n  unlink(filePath: FilePath): Promise<void> {\n    super.unlink(filePath);\n    return this.handleFn('unlink', [filePath]);\n  }\n\n  mkdirp(dir: FilePath): Promise<void> {\n    super.mkdirp(dir);\n    return this.handleFn('mkdirp', [dir]);\n  }\n\n  rimraf(filePath: FilePath): Promise<void> {\n    super.rimraf(filePath);\n    return this.handleFn('rimraf', [filePath]);\n  }\n\n  ncp(source: FilePath, destination: FilePath): Promise<void> {\n    super.ncp(source, destination);\n    return this.handleFn('ncp', [source, destination]);\n  }\n\n  symlink(target: FilePath, path: FilePath): Promise<void> {\n    super.symlink(target, path);\n    return this.handleFn('symlink', [target, path]);\n  }\n}\n\nregisterSerializableClass(`${packageJSON.version}:MemoryFS`, MemoryFS);\nregisterSerializableClass(`${packageJSON.version}:WorkerFS`, WorkerFS);\nregisterSerializableClass(`${packageJSON.version}:Stat`, Stat);\nregisterSerializableClass(`${packageJSON.version}:File`, File);\nregisterSerializableClass(`${packageJSON.version}:Directory`, Directory);\n", "{\n  \"name\": \"@parcel/fs\",\n  \"version\": \"2.15.2\",\n  \"description\": \"Blazing fast, zero configuration web application bundler\",\n  \"license\": \"MIT\",\n  \"publishConfig\": {\n    \"access\": \"public\"\n  },\n  \"funding\": {\n    \"type\": \"opencollective\",\n    \"url\": \"https://opencollective.com/parcel\"\n  },\n  \"repository\": {\n    \"type\": \"git\",\n    \"url\": \"https://github.com/parcel-bundler/parcel.git\"\n  },\n  \"main\": \"lib/index.js\",\n  \"source\": \"src/index.js\",\n  \"types\": \"index.d.ts\",\n  \"engines\": {\n    \"node\": \">= 16.0.0\"\n  },\n  \"targets\": {\n    \"types\": false,\n    \"main\": {\n      \"includeNodeModules\": {\n        \"@parcel/core\": false,\n        \"@parcel/feature-flags\": false,\n        \"@parcel/rust\": false,\n        \"@parcel/types-internal\": false,\n        \"@parcel/utils\": false,\n        \"@parcel/watcher\": false,\n        \"@parcel/watcher-watchman-js\": false,\n        \"@parcel/workers\": false\n      }\n    },\n    \"browser\": {\n      \"includeNodeModules\": {\n        \"@parcel/core\": false,\n        \"@parcel/feature-flags\": false,\n        \"@parcel/rust\": false,\n        \"@parcel/types-internal\": false,\n        \"@parcel/utils\": false,\n        \"@parcel/watcher\": false,\n        \"@parcel/watcher-watchman-js\": false,\n        \"@parcel/workers\": false\n      }\n    }\n  },\n  \"scripts\": {\n    \"build-ts\": \"mkdir -p lib && flow-to-ts src/types.js > lib/types.d.ts\",\n    \"check-ts\": \"tsc --noEmit index.d.ts\"\n  },\n  \"dependencies\": {\n    \"@parcel/feature-flags\": \"2.15.2\",\n    \"@parcel/rust\": \"2.15.2\",\n    \"@parcel/types-internal\": \"2.15.2\",\n    \"@parcel/utils\": \"2.15.2\",\n    \"@parcel/watcher\": \"^2.0.7\",\n    \"@parcel/workers\": \"2.15.2\"\n  },\n  \"devDependencies\": {\n    \"@parcel/watcher-watchman-js\": \"2.15.2\",\n    \"graceful-fs\": \"^4.2.11\",\n    \"ncp\": \"^2.0.0\",\n    \"nullthrows\": \"^1.1.1\",\n    \"utility-types\": \"^3.11.0\"\n  },\n  \"peerDependencies\": {\n    \"@parcel/core\": \"^2.15.2\"\n  },\n  \"browser\": {\n    \"./src/NodeFS.js\": \"./src/NodeFS.browser.js\",\n    \"@parcel/fs\": \"./lib/browser.js\"\n  },\n  \"gitHead\": \"b66f37168d0e830c030d0427bceac90117674cae\"\n}\n", "'use strict';\n\nfunction nullthrows(x, message) {\n  if (x != null) {\n    return x;\n  }\n  var error = new Error(message !== undefined ? message : 'Got unexpected ' + x);\n  error.framesToPop = 1; // Skip nullthrows's own stack frame.\n  throw error;\n}\n\nmodule.exports = nullthrows;\nmodule.exports.default = nullthrows;\n\nObject.defineProperty(module.exports, '__esModule', {value: true});\n", "// @flow\nimport type {FilePath, FileSystem} from '@parcel/types-internal';\nimport path from 'path';\n\nexport function findNodeModule(\n  fs: FileSystem,\n  moduleName: string,\n  dir: FilePath,\n): ?FilePath {\n  let {root} = path.parse(dir);\n  while (dir !== root) {\n    // Skip node_modules directories\n    if (path.basename(dir) === 'node_modules') {\n      dir = path.dirname(dir);\n    }\n\n    try {\n      let moduleDir = path.join(dir, 'node_modules', moduleName);\n      let stats = fs.statSync(moduleDir);\n      if (stats.isDirectory()) {\n        return moduleDir;\n      }\n    } catch (err) {\n      // ignore\n    }\n\n    // Move up a directory\n    dir = path.dirname(dir);\n  }\n\n  return null;\n}\n\nexport function findAncestorFile(\n  fs: FileSystem,\n  fileNames: Array<string>,\n  dir: FilePath,\n  root: FilePath,\n): ?FilePath {\n  let {root: pathRoot} = path.parse(dir);\n  // eslint-disable-next-line no-constant-condition\n  while (true) {\n    if (path.basename(dir) === 'node_modules') {\n      return null;\n    }\n\n    for (const fileName of fileNames) {\n      let filePath = path.join(dir, fileName);\n      try {\n        if (fs.statSync(filePath).isFile()) {\n          return filePath;\n        }\n      } catch (err) {\n        // ignore\n      }\n    }\n\n    if (dir === root || dir === pathRoot) {\n      break;\n    }\n\n    dir = path.dirname(dir);\n  }\n\n  return null;\n}\n\nexport function findFirstFile(\n  fs: FileSystem,\n  filePaths: Array<FilePath>,\n): ?FilePath {\n  for (let filePath of filePaths) {\n    try {\n      if (fs.statSync(filePath).isFile()) {\n        return filePath;\n      }\n    } catch (err) {\n      // ignore\n    }\n  }\n}\n", "// @flow\n\nimport type {Readable, Writable} from 'stream';\nimport type {\n  FilePath,\n  Encoding,\n  FileOptions,\n  FileSystem,\n  ReaddirOptions,\n  FileStats,\n} from '@parcel/types-internal';\nimport type {\n  Event,\n  Options as WatcherOptions,\n  AsyncSubscription,\n} from '@parcel/watcher';\n\nimport {registerSerializableClass} from '@parcel/core';\nimport WorkerFarm from '@parcel/workers';\nimport packageJSON from '../package.json';\nimport {findAncestorFile, findNodeModule, findFirstFile} from './find';\nimport {MemoryFS} from './MemoryFS';\n\nimport nullthrows from 'nullthrows';\nimport path from 'path';\n\nexport class OverlayFS implements FileSystem {\n  deleted: Set<FilePath> = new Set();\n  writable: FileSystem;\n  readable: FileSystem;\n  _cwd: FilePath;\n\n  constructor(workerFarmOrFS: WorkerFarm | FileSystem, readable: FileSystem) {\n    if (workerFarmOrFS instanceof WorkerFarm) {\n      this.writable = new MemoryFS(workerFarmOrFS);\n    } else {\n      this.writable = workerFarmOrFS;\n    }\n    this.readable = readable;\n    this._cwd = readable.cwd();\n  }\n\n  static deserialize(opts: any): OverlayFS {\n    let fs = new OverlayFS(opts.writable, opts.readable);\n    if (opts.deleted != null) fs.deleted = opts.deleted;\n    return fs;\n  }\n\n  serialize(): {|\n    $$raw: boolean,\n    readable: FileSystem,\n    writable: FileSystem,\n    deleted: Set<FilePath>,\n  |} {\n    return {\n      $$raw: false,\n      writable: this.writable,\n      readable: this.readable,\n      deleted: this.deleted,\n    };\n  }\n\n  _deletedThrows(filePath: FilePath): FilePath {\n    filePath = this._normalizePath(filePath);\n    if (this.deleted.has(filePath)) {\n      throw new FSError('ENOENT', filePath, 'does not exist');\n    }\n    return filePath;\n  }\n\n  _checkExists(filePath: FilePath): FilePath {\n    filePath = this._deletedThrows(filePath);\n    if (!this.existsSync(filePath)) {\n      throw new FSError('ENOENT', filePath, 'does not exist');\n    }\n    return filePath;\n  }\n\n  _isSymlink(filePath: FilePath): boolean {\n    filePath = this._normalizePath(filePath);\n    // Check the parts of the path to see if any are symlinks.\n    let {root, dir, base} = path.parse(filePath);\n    let segments = dir.slice(root.length).split(path.sep).concat(base);\n    while (segments.length) {\n      filePath = path.join(root, ...segments);\n      let name = segments.pop();\n      if (this.deleted.has(filePath)) {\n        return false;\n      } else if (\n        this.writable instanceof MemoryFS &&\n        this.writable.symlinks.has(filePath)\n      ) {\n        return true;\n      } else {\n        // HACK: Parcel fs does not provide `lstatSync`,\n        // so we use `readdirSync` to check if the path is a symlink.\n        let parent = path.resolve(filePath, '..');\n        if (parent === filePath) {\n          return false;\n        }\n        try {\n          for (let dirent of this.readdirSync(parent, {withFileTypes: true})) {\n            if (typeof dirent === 'string') {\n              break; // {withFileTypes: true} not supported\n            } else if (dirent.name === name) {\n              if (dirent.isSymbolicLink()) {\n                return true;\n              }\n            }\n          }\n        } catch (e) {\n          if (e.code === 'ENOENT') {\n            return false;\n          }\n          throw e;\n        }\n      }\n    }\n\n    return false;\n  }\n\n  async _copyPathForWrite(filePath: FilePath): Promise<FilePath> {\n    filePath = await this._normalizePath(filePath);\n    let dirPath = path.dirname(filePath);\n    if (this.existsSync(dirPath) && !this.writable.existsSync(dirPath)) {\n      await this.writable.mkdirp(dirPath);\n    }\n    return filePath;\n  }\n\n  _normalizePath(filePath: FilePath): FilePath {\n    return path.resolve(this.cwd(), filePath);\n  }\n\n  // eslint-disable-next-line require-await\n  async readFile(filePath: FilePath, encoding?: Encoding): Promise<any> {\n    return this.readFileSync(filePath, encoding);\n  }\n\n  async writeFile(\n    filePath: FilePath,\n    contents: string | Buffer,\n    options: ?FileOptions,\n  ): Promise<void> {\n    filePath = await this._copyPathForWrite(filePath);\n    await this.writable.writeFile(filePath, contents, options);\n    this.deleted.delete(filePath);\n  }\n\n  async copyFile(source: FilePath, destination: FilePath): Promise<void> {\n    source = this._normalizePath(source);\n    destination = await this._copyPathForWrite(destination);\n\n    if (await this.writable.exists(source)) {\n      await this.writable.writeFile(\n        destination,\n        await this.writable.readFile(source),\n      );\n    } else {\n      await this.writable.writeFile(\n        destination,\n        await this.readable.readFile(source),\n      );\n    }\n\n    this.deleted.delete(destination);\n  }\n\n  // eslint-disable-next-line require-await\n  async stat(filePath: FilePath): Promise<FileStats> {\n    return this.statSync(filePath);\n  }\n\n  // eslint-disable-next-line require-await\n  async lstat(filePath: FilePath): Promise<FileStats> {\n    return this.lstatSync(filePath);\n  }\n\n  async symlink(target: FilePath, filePath: FilePath): Promise<void> {\n    target = this._normalizePath(target);\n    filePath = this._normalizePath(filePath);\n    await this.writable.symlink(target, filePath);\n    this.deleted.delete(filePath);\n  }\n\n  async unlink(filePath: FilePath): Promise<void> {\n    filePath = this._normalizePath(filePath);\n\n    let toDelete = [filePath];\n\n    if (this.writable instanceof MemoryFS && this._isSymlink(filePath)) {\n      this.writable.symlinks.delete(filePath);\n    } else if (this.statSync(filePath).isDirectory()) {\n      let stack = [filePath];\n\n      // Recursively add every descendant path to deleted.\n      while (stack.length) {\n        let root = nullthrows(stack.pop());\n        for (let ent of this.readdirSync(root, {withFileTypes: true})) {\n          if (typeof ent === 'string') {\n            let childPath = path.join(root, ent);\n            toDelete.push(childPath);\n            if (this.statSync(childPath).isDirectory()) {\n              stack.push(childPath);\n            }\n          } else {\n            let childPath = path.join(root, ent.name);\n            toDelete.push(childPath);\n            if (ent.isDirectory()) {\n              stack.push(childPath);\n            }\n          }\n        }\n      }\n    }\n\n    try {\n      await this.writable.unlink(filePath);\n    } catch (e) {\n      if (e.code === 'ENOENT' && !this.readable.existsSync(filePath)) {\n        throw e;\n      }\n    }\n\n    for (let pathToDelete of toDelete) {\n      this.deleted.add(pathToDelete);\n    }\n  }\n\n  async mkdirp(dir: FilePath): Promise<void> {\n    dir = this._normalizePath(dir);\n    await this.writable.mkdirp(dir);\n\n    if (this.deleted != null) {\n      let root = path.parse(dir).root;\n      while (dir !== root) {\n        this.deleted.delete(dir);\n        dir = path.dirname(dir);\n      }\n    }\n  }\n\n  async rimraf(filePath: FilePath): Promise<void> {\n    try {\n      await this.unlink(filePath);\n    } catch (e) {\n      // noop\n    }\n  }\n\n  // eslint-disable-next-line require-await\n  async ncp(source: FilePath, destination: FilePath): Promise<void> {\n    // TODO: Implement this correctly.\n    return this.writable.ncp(source, destination);\n  }\n\n  createReadStream(filePath: FilePath, opts?: ?FileOptions): Readable {\n    filePath = this._deletedThrows(filePath);\n    if (this.writable.existsSync(filePath)) {\n      return this.writable.createReadStream(filePath, opts);\n    }\n\n    return this.readable.createReadStream(filePath, opts);\n  }\n\n  createWriteStream(path: FilePath, opts?: ?FileOptions): Writable {\n    path = this._normalizePath(path);\n    this.deleted.delete(path);\n    return this.writable.createWriteStream(path, opts);\n  }\n\n  cwd(): FilePath {\n    return this._cwd;\n  }\n\n  chdir(path: FilePath): void {\n    this._cwd = this._checkExists(path);\n  }\n\n  // eslint-disable-next-line require-await\n  async realpath(filePath: FilePath): Promise<FilePath> {\n    return this.realpathSync(filePath);\n  }\n\n  readFileSync(filePath: FilePath, encoding?: Encoding): any {\n    filePath = this.realpathSync(filePath);\n    try {\n      // $FlowFixMe[incompatible-call]\n      return this.writable.readFileSync(filePath, encoding);\n    } catch (err) {\n      // $FlowFixMe[incompatible-call]\n      return this.readable.readFileSync(filePath, encoding);\n    }\n  }\n\n  statSync(filePath: FilePath): FileStats {\n    filePath = this._normalizePath(filePath);\n    try {\n      return this.writable.statSync(filePath);\n    } catch (e) {\n      if (e.code === 'ENOENT' && this.existsSync(filePath)) {\n        return this.readable.statSync(filePath);\n      }\n      throw e;\n    }\n  }\n\n  lstatSync(filePath: FilePath): FileStats {\n    filePath = this._normalizePath(filePath);\n    try {\n      return this.writable.lstatSync(filePath);\n    } catch (e) {\n      if (e.code === 'ENOENT') {\n        return this.readable.lstatSync(filePath);\n      }\n      throw e;\n    }\n  }\n\n  realpathSync(filePath: FilePath): FilePath {\n    filePath = this._deletedThrows(filePath);\n    filePath = this._deletedThrows(this.writable.realpathSync(filePath));\n    if (!this.writable.existsSync(filePath)) {\n      return this.readable.realpathSync(filePath);\n    }\n    return filePath;\n  }\n\n  readlinkSync(filePath: FilePath): FilePath {\n    filePath = this._deletedThrows(filePath);\n    try {\n      return this.writable.readlinkSync(filePath);\n    } catch (err) {\n      return this.readable.readlinkSync(filePath);\n    }\n  }\n\n  // eslint-disable-next-line require-await\n  async readlink(filePath: FilePath): Promise<FilePath> {\n    return this.readlinkSync(filePath);\n  }\n\n  // eslint-disable-next-line require-await\n  async exists(filePath: FilePath): Promise<boolean> {\n    return this.existsSync(filePath);\n  }\n\n  existsSync(filePath: FilePath): boolean {\n    filePath = this._normalizePath(filePath);\n    if (this.deleted.has(filePath)) return false;\n\n    try {\n      filePath = this.realpathSync(filePath);\n    } catch (err) {\n      if (err.code !== 'ENOENT') throw err;\n    }\n\n    if (this.deleted.has(filePath)) return false;\n\n    return (\n      this.writable.existsSync(filePath) || this.readable.existsSync(filePath)\n    );\n  }\n\n  // eslint-disable-next-line require-await\n  async readdir(path: FilePath, opts?: ReaddirOptions): Promise<any> {\n    return this.readdirSync(path, opts);\n  }\n\n  readdirSync(dir: FilePath, opts?: ReaddirOptions): any {\n    dir = this.realpathSync(dir);\n    // Read from both filesystems and merge the results\n    let entries = new Map();\n\n    try {\n      for (let entry: any of this.writable.readdirSync(dir, opts)) {\n        let filePath = path.join(dir, entry.name ?? entry);\n        if (this.deleted.has(filePath)) continue;\n        entries.set(filePath, entry);\n      }\n    } catch {\n      // noop\n    }\n\n    try {\n      for (let entry: any of this.readable.readdirSync(dir, opts)) {\n        let filePath = path.join(dir, entry.name ?? entry);\n        if (this.deleted.has(filePath)) continue;\n        if (entries.has(filePath)) continue;\n        entries.set(filePath, entry);\n      }\n    } catch {\n      // noop\n    }\n\n    return Array.from(entries.values());\n  }\n\n  async watch(\n    dir: FilePath,\n    fn: (err: ?Error, events: Array<Event>) => mixed,\n    opts: WatcherOptions,\n  ): Promise<AsyncSubscription> {\n    let writableSubscription = await this.writable.watch(dir, fn, opts);\n    let readableSubscription = await this.readable.watch(dir, fn, opts);\n    return {\n      unsubscribe: async () => {\n        await writableSubscription.unsubscribe();\n        await readableSubscription.unsubscribe();\n      },\n    };\n  }\n\n  async getEventsSince(\n    dir: FilePath,\n    snapshot: FilePath,\n    opts: WatcherOptions,\n  ): Promise<Array<Event>> {\n    let writableEvents = await this.writable.getEventsSince(\n      dir,\n      snapshot,\n      opts,\n    );\n    let readableEvents = await this.readable.getEventsSince(\n      dir,\n      snapshot,\n      opts,\n    );\n    return [...writableEvents, ...readableEvents];\n  }\n\n  async writeSnapshot(\n    dir: FilePath,\n    snapshot: FilePath,\n    opts: WatcherOptions,\n  ): Promise<void> {\n    await this.writable.writeSnapshot(dir, snapshot, opts);\n  }\n\n  findAncestorFile(\n    fileNames: Array<string>,\n    fromDir: FilePath,\n    root: FilePath,\n  ): ?FilePath {\n    return findAncestorFile(this, fileNames, fromDir, root);\n  }\n\n  findNodeModule(moduleName: string, fromDir: FilePath): ?FilePath {\n    return findNodeModule(this, moduleName, fromDir);\n  }\n\n  findFirstFile(filePaths: Array<FilePath>): ?FilePath {\n    return findFirstFile(this, filePaths);\n  }\n}\n\nclass FSError extends Error {\n  code: string;\n  path: FilePath;\n  constructor(code: string, path: FilePath, message: string) {\n    super(`${code}: ${path} ${message}`);\n    this.name = 'FSError';\n    this.code = code;\n    this.path = path;\n    Error.captureStackTrace?.(this, this.constructor);\n  }\n}\n\nregisterSerializableClass(`${packageJSON.version}:OverlayFS`, OverlayFS);\n"], "names": ["path", "stream", "promisify", "pipeline", "ncp", "sourceFS", "source", "destinationFS", "destination", "mkdirp", "files", "readdir", "file", "sourcePath", "join", "destPath", "stats", "stat", "isFile", "createReadStream", "createWriteStream", "isDirectory", "NodeFS", "constructor", "Error", "Readable", "Writable", "registerSerializableClass", "SharedBuffer", "packageJSON", "WorkerFarm", "<PERSON><PERSON>", "nullthrows", "EventEmitter", "findAncestorFile", "findNodeModule", "findFirstFile", "instances", "Map", "id", "MemoryFS", "_numWorkerInstances", "_workerRegisterResolves", "_emitter", "workerFarm", "farm", "_cwd", "resolve", "sep", "dirs", "Directory", "symlinks", "watchers", "events", "_<PERSON><PERSON><PERSON><PERSON>", "_eventQueue", "set", "on", "deserialize", "opts", "existing", "get", "getWorkerApi", "<PERSON><PERSON><PERSON><PERSON>", "handle", "fs", "WorkerFS", "serialize", "createReverseHandle", "fn", "args", "$$raw", "decrementWorkerInstance", "length", "emit", "cwd", "chdir", "dir", "_normalizePath", "filePath", "realpath", "normalize", "startsWith", "root", "base", "parse", "parts", "slice", "split", "concat", "last", "res", "part", "symlink", "writeFile", "contents", "options", "has", "FSError", "dirname", "buffer", "makeShared", "mode", "write", "File", "_sendWorkerEvent", "type", "entry", "_triggerEvent", "readFile", "encoding", "readFileSync", "read", "toString", "copyFile", "statSync", "lstatSync", "Stat", "S_IFLNK", "lstat", "readdirSync", "endsWith", "indexOf", "name", "withFileTypes", "push", "Dirent", "from", "unlink", "delete", "Promise", "<PERSON><PERSON><PERSON>", "keys", "<PERSON><PERSON><PERSON>", "destName", "exists", "ReadStream", "WriteStream", "realpathSync", "readlinkSync", "readlink", "target", "existsSync", "event", "size", "clearTimeout", "_watcher<PERSON>imer", "setTimeout", "watcher", "trigger", "_registerWorker", "all", "map", "worker<PERSON><PERSON><PERSON>", "workerApi", "watch", "Watcher", "Set", "add", "unsubscribe", "getEventsSince", "snapshot", "len", "Number", "ignore", "filter", "some", "i", "writeSnapshot", "fileNames", "fromDir", "moduleName", "filePaths", "code", "message", "captureStackTrace", "reading", "bytesRead", "_read", "then", "byteLength", "err", "emitClose", "autoDestroy", "<PERSON><PERSON><PERSON>", "alloc", "_write", "chunk", "callback", "c", "_final", "catch", "S_IFREG", "S_IFDIR", "S_IFMT", "Entry", "now", "Date", "atime", "mtime", "ctime", "birthtime", "access", "modify", "getSize", "fromEntry", "dev", "ino", "nlink", "uid", "gid", "rdev", "blksize", "blocks", "atimeMs", "mtimeMs", "ctimeMs", "birthtimeMs", "isBlockDevice", "isCharacterDevice", "isSymbolicLink", "isFIFO", "isSocket", "contentsBuffer", "process", "browser", "shared", "handleFn", "methodName", "version", "module", "exports", "JSON", "basename", "moduleDir", "pathRoot", "fileName", "OverlayFS", "deleted", "workerFarmOrFS", "readable", "writable", "_deletedThrows", "_checkExists", "_isSymlink", "segments", "pop", "parent", "dirent", "e", "_copyPathForWrite", "toDelete", "stack", "ent", "child<PERSON><PERSON>", "pathToDelete", "entries", "Array", "values", "writableSubscription", "readableSubscription", "writableEvents", "readableEvents"], "version": 3, "file": "browser.js.map"}