{"name": "@parcel/fs", "version": "2.15.2", "description": "Blazing fast, zero configuration web application bundler", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "main": "lib/index.js", "source": "src/index.js", "types": "index.d.ts", "engines": {"node": ">= 16.0.0"}, "targets": {"types": false, "main": {"includeNodeModules": {"@parcel/core": false, "@parcel/feature-flags": false, "@parcel/rust": false, "@parcel/types-internal": false, "@parcel/utils": false, "@parcel/watcher": false, "@parcel/watcher-watchman-js": false, "@parcel/workers": false}}, "browser": {"includeNodeModules": {"@parcel/core": false, "@parcel/feature-flags": false, "@parcel/rust": false, "@parcel/types-internal": false, "@parcel/utils": false, "@parcel/watcher": false, "@parcel/watcher-watchman-js": false, "@parcel/workers": false}}}, "scripts": {"build-ts": "mkdir -p lib && flow-to-ts src/types.js > lib/types.d.ts", "check-ts": "tsc --noEmit index.d.ts"}, "dependencies": {"@parcel/feature-flags": "2.15.2", "@parcel/rust": "2.15.2", "@parcel/types-internal": "2.15.2", "@parcel/utils": "2.15.2", "@parcel/watcher": "^2.0.7", "@parcel/workers": "2.15.2"}, "devDependencies": {"@parcel/watcher-watchman-js": "2.15.2", "graceful-fs": "^4.2.11", "ncp": "^2.0.0", "nullthrows": "^1.1.1", "utility-types": "^3.11.0"}, "peerDependencies": {"@parcel/core": "^2.15.2"}, "browser": {"./src/NodeFS.js": "./src/NodeFS.browser.js", "@parcel/fs": "./lib/browser.js"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}