{"name": "parcel", "version": "2.15.2", "description": "Blazing fast, zero configuration web application bundler", "license": "MIT", "publishConfig": {"access": "public"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "repository": {"type": "git", "url": "https://github.com/parcel-bundler/parcel.git"}, "bin": "lib/bin.js", "main": "lib/bin.js", "source": "src/bin.js", "engines": {"node": ">= 16.0.0"}, "dependencies": {"@parcel/config-default": "2.15.2", "@parcel/core": "2.15.2", "@parcel/diagnostic": "2.15.2", "@parcel/events": "2.15.2", "@parcel/feature-flags": "2.15.2", "@parcel/fs": "2.15.2", "@parcel/logger": "2.15.2", "@parcel/package-manager": "2.15.2", "@parcel/reporter-cli": "2.15.2", "@parcel/reporter-dev-server": "2.15.2", "@parcel/reporter-tracer": "2.15.2", "@parcel/utils": "2.15.2", "chalk": "^4.1.2", "commander": "^12.1.0", "get-port": "^4.2.0"}, "devDependencies": {"@babel/core": "^7.22.11", "@parcel/babel-register": "2.15.2"}, "gitHead": "b66f37168d0e830c030d0427bceac90117674cae"}