{"version": 3, "names": ["_file", "require", "_buildExternalHelpers", "resolvers", "_environment", "_types", "data", "Object", "defineProperty", "exports", "enumerable", "get", "_parser", "_traverse", "_template", "_index2", "_transform", "_transformFile", "_transformAst", "_parse", "version", "resolvePlugin", "name", "dirname", "filepath", "resolvePreset", "DEFAULT_EXTENSIONS", "freeze", "OptionManager", "init", "opts", "loadOptionsSync", "Plugin", "alias", "Error", "types", "traverse", "tokTypes", "template"], "sources": ["../src/index.ts"], "sourcesContent": ["if (!process.env.IS_PUBLISH && !USE_ESM && process.env.BABEL_8_BREAKING) {\n  throw new Error(\n    \"BABEL_8_BREAKING is only supported in ESM. Please run `make use-esm`.\",\n  );\n}\n\nexport const version = PACKAGE_JSON.version;\n\nexport { default as File } from \"./transformation/file/file.ts\";\nexport type { default as PluginPass } from \"./transformation/plugin-pass.ts\";\nexport { default as buildExternalHelpers } from \"./tools/build-external-helpers.ts\";\n\nimport * as resolvers from \"./config/files/index.ts\";\n// For backwards-compatibility, we expose the resolvers\n// with the old API.\nexport const resolvePlugin = (name: string, dirname: string) =>\n  resolvers.resolvePlugin(name, dirname, false).filepath;\nexport const resolvePreset = (name: string, dirname: string) =>\n  resolvers.resolvePreset(name, dirname, false).filepath;\n\nexport { getEnv } from \"./config/helpers/environment.ts\";\n\n// NOTE: Lazy re-exports aren't detected by the Node.js CJS-ESM interop.\n// These are handled by pluginInjectNodeReexportsHints in our babel.config.js\n// so that they can work well.\nexport * as types from \"@babel/types\";\nexport { tokTypes } from \"@babel/parser\";\nexport { default as traverse } from \"@babel/traverse\";\nexport { default as template } from \"@babel/template\";\n\n// rollup-plugin-dts assumes that all re-exported types are also valid values\n// Visitor is only a type, so we need to use this workaround to prevent\n// rollup-plugin-dts from breaking it.\n// TODO: Figure out how to fix this upstream.\nexport type { NodePath, Scope } from \"@babel/traverse\";\nexport type Visitor<S = unknown> = import(\"@babel/traverse\").Visitor<S>;\n\nexport {\n  createConfigItem,\n  createConfigItemAsync,\n  createConfigItemSync,\n} from \"./config/index.ts\";\n\nexport {\n  loadOptions,\n  loadOptionsAsync,\n  loadPartialConfig,\n  loadPartialConfigAsync,\n  loadPartialConfigSync,\n} from \"./config/index.ts\";\nimport { loadOptionsSync } from \"./config/index.ts\";\nexport { loadOptionsSync };\n\nexport type {\n  CallerMetadata,\n  ConfigItem,\n  InputOptions,\n  PluginAPI,\n  PluginObject,\n  PresetAPI,\n  PresetObject,\n} from \"./config/index.ts\";\n\nexport {\n  type FileResult,\n  transform,\n  transformAsync,\n  transformSync,\n} from \"./transform.ts\";\nexport {\n  transformFile,\n  transformFileAsync,\n  transformFileSync,\n} from \"./transform-file.ts\";\nexport {\n  transformFromAst,\n  transformFromAstAsync,\n  transformFromAstSync,\n} from \"./transform-ast.ts\";\nexport { parse, parseAsync, parseSync } from \"./parse.ts\";\n\n/**\n * Recommended set of compilable extensions. Not used in @babel/core directly, but meant as\n * as an easy source for tooling making use of @babel/core.\n */\nexport const DEFAULT_EXTENSIONS = Object.freeze([\n  \".js\",\n  \".jsx\",\n  \".es6\",\n  \".es\",\n  \".mjs\",\n  \".cjs\",\n] as const);\n\nif (!process.env.BABEL_8_BREAKING && !USE_ESM) {\n  // For easier backward-compatibility, provide an API like the one we exposed in Babel 6.\n  // eslint-disable-next-line no-restricted-globals\n  exports.OptionManager = class OptionManager {\n    init(opts: any) {\n      return loadOptionsSync(opts);\n    }\n  };\n\n  // eslint-disable-next-line no-restricted-globals\n  exports.Plugin = function Plugin(alias: string) {\n    throw new Error(\n      `The (${alias}) Babel 5 plugin is being run with an unsupported Babel version.`,\n    );\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA,IAAAA,KAAA,GAAAC,OAAA;AAEA,IAAAC,qBAAA,GAAAD,OAAA;AAEA,IAAAE,SAAA,GAAAF,OAAA;AAQA,IAAAG,YAAA,GAAAH,OAAA;AAAyD,SAAAI,OAAA;EAAA,MAAAC,IAAA,GAAAL,OAAA;EAAAI,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAAC,MAAA,CAAAC,cAAA,KAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAAAN,MAAA;EAAA;AAAA;AAMzD,SAAAO,QAAA;EAAA,MAAAN,IAAA,GAAAL,OAAA;EAAAW,OAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,UAAA;EAAA,MAAAP,IAAA,GAAAL,OAAA;EAAAY,SAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,UAAA;EAAA,MAAAR,IAAA,GAAAL,OAAA;EAAAa,SAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AASA,IAAAS,OAAA,GAAAd,OAAA;AA0BA,IAAAe,UAAA,GAAAf,OAAA;AAMA,IAAAgB,cAAA,GAAAhB,OAAA;AAKA,IAAAiB,aAAA,GAAAjB,OAAA;AAKA,IAAAkB,MAAA,GAAAlB,OAAA;AAA0D;AAzEnD,MAAMmB,OAAO,GAAAX,OAAA,CAAAW,OAAA,WAAuB;AASpC,MAAMC,aAAa,GAAGA,CAACC,IAAY,EAAEC,OAAe,KACzDpB,SAAS,CAACkB,aAAa,CAACC,IAAI,EAAEC,OAAO,EAAE,KAAK,CAAC,CAACC,QAAQ;AAACf,OAAA,CAAAY,aAAA,GAAAA,aAAA;AAClD,MAAMI,aAAa,GAAGA,CAACH,IAAY,EAAEC,OAAe,KACzDpB,SAAS,CAACsB,aAAa,CAACH,IAAI,EAAEC,OAAO,EAAE,KAAK,CAAC,CAACC,QAAQ;AAACf,OAAA,CAAAgB,aAAA,GAAAA,aAAA;AAmElD,MAAMC,kBAAkB,GAAAjB,OAAA,CAAAiB,kBAAA,GAAGnB,MAAM,CAACoB,MAAM,CAAC,CAC9C,KAAK,EACL,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,CACE,CAAC;AAEoC;EAG7ClB,OAAO,CAACmB,aAAa,GAAG,MAAMA,aAAa,CAAC;IAC1CC,IAAIA,CAACC,IAAS,EAAE;MACd,OAAO,IAAAC,uBAAe,EAACD,IAAI,CAAC;IAC9B;EACF,CAAC;EAGDrB,OAAO,CAACuB,MAAM,GAAG,SAASA,MAAMA,CAACC,KAAa,EAAE;IAC9C,MAAM,IAAIC,KAAK,CACb,QAAQD,KAAK,kEACf,CAAC;EACH,CAAC;AACH;AAAC,MAAAxB,OAAA,CAAA0B,KAAA,GAAA1B,OAAA,CAAA2B,QAAA,GAAA3B,OAAA,CAAA4B,QAAA,GAAA5B,OAAA,CAAA6B,QAAA", "ignoreList": []}